services:
  - type: web
    name: maia-web-frontend
    env: static
    buildCommand: |
      npm install -g typescript
      cd frontend
      npm install --include=dev
      npm install --save-dev @types/node
      npm run build
    staticPublishPath: ./frontend/dist
    routes:
      - type: rewrite
        source: /*
        destination: /index.html
    envVars:
      - key: NODE_VERSION
        value: 18
