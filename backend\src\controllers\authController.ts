import { Request, Response } from 'express';
import User, { IUser } from '../models/User';
import bcrypt from 'bcrypt';
import jwt from 'jsonwebtoken';
import { Types } from 'mongoose';

const secret = process.env.JWT_SECRET;

// Utility function to generate JWT
const generateToken = (id: Types.ObjectId | string): string => {
  if (!secret) {
    // It's crucial to handle the case where the secret is missing
    console.error('JWT_SECRET is not defined in environment variables.');
    throw new Error('JWT secret configuration error.');
  }
  // Add expiration time (e.g., '1d' for 1 day, '2h' for 2 hours)
  return jwt.sign({ id: id.toString() }, secret, {
    expiresIn: '1d', // Set token to expire in 1 day
  });
};

// Controller function for user registration
export const registerUser = async (req: Request, res: Response) => {
  const { email, password } = req.body;

  // Basic validation
  if (!email || !password) {
    return res.status(400).json({ message: 'Email and password are required' });
  }
  if (password.length < 6) { // Example: Enforce minimum password length
    return res.status(400).json({ message: 'Password must be at least 6 characters long' });
  }

  try {
    // Check if user already exists
    const existingUser = await User.findOne({ email: email.toLowerCase() });
    if (existingUser) {
      return res.status(409).json({ message: 'User already exists with this email' }); // 409 Conflict
    }

    // Hash the password
    const passwordHash = await bcrypt.hash(password, 10);

    // Create new user
    const newUser = new User({
      email: email.toLowerCase(),
      passwordHash,
      // subscriptionTier will default to 'free' as per schema
    });

    // Save the user to the database
    const savedUser: IUser = await newUser.save();

    // Generate JWT token
    const token = generateToken(savedUser.id);

    // Respond with user info (excluding password hash) and token
    res.status(201).json({ // 201 Created
      token,
      user: {
        id: savedUser.id,
        email: savedUser.email,
        subscriptionTier: savedUser.subscriptionTier,
      },
    });
  } catch (error) {
    console.error('Registration Error:', error);
    if (error instanceof Error && error.message.includes('validation failed')) {
        return res.status(400).json({ message: 'Validation failed', error: error.message });
    }
    res.status(500).json({ message: 'Server error during registration' });
  }
};

// Controller function for user login
export const loginUser = async (req: Request, res: Response) => {
  const { email, password } = req.body;

  // Basic validation
  if (!email || !password) {
    return res.status(400).json({ message: 'Email and password are required' });
  }

  try {
    // Find user by email
    const user: IUser | null = await User.findOne({ email: email.toLowerCase() });

    // Check if user exists AND if they have a password hash (for non-OAuth users)
    if (!user || !user.passwordHash) {
      return res.status(401).json({ message: 'Invalid credentials or user signed up with Google' });
    }

    // Compare provided password with stored hash
    const isMatch = await bcrypt.compare(password, user.passwordHash);
    if (!isMatch) {
      return res.status(401).json({ message: 'Invalid credentials' }); // Use generic message
    }

    // Generate JWT token
    const token = generateToken(user.id);

    // Respond with user info (excluding password hash) and token
    res.status(200).json({
      token,
      user: {
        id: user.id,
        email: user.email,
        subscriptionTier: user.subscriptionTier,
      },
    });
  } catch (error) {
    console.error('Login Error:', error);
    res.status(500).json({ message: 'Server error during login' });
  }
};

// Controller function to get current user info (will be protected by middleware)
export const getCurrentUser = async (req: Request, res: Response) => {
    // Ensure req.user and req.user.id exist before querying
    if (!req.user) {
      return res.status(401).json({ message: 'Unauthorized: No user data found in request' });
    }

    // Use type assertion to access the user ID
    const userId = req.user._id || (req.user as any).id;

    try {
        const user: IUser | null = await User.findById(userId).select('-passwordHash'); // Exclude password hash

        if (!user) {
            return res.status(404).json({ message: 'User not found' });
        }

        // Return user data
        res.status(200).json({
            id: user._id,
            email: user.email,
            subscriptionTier: user.subscriptionTier,
            createdAt: user.createdAt,
            lastUsedProfileId: user.lastUsedProfileId || null,
        });
    } catch (error) {
        console.error('Get Current User Error:', error);
        res.status(500).json({ message: 'Server error fetching user data' });
    }
};

// Controller function to update the user's last used profile ID
export const updateLastUsedProfile = async (req: Request, res: Response) => {
    if (!req.user) {
        return res.status(401).json({ message: 'Unauthorized: No user data found in request' });
    }

    // Use type assertion to access the user ID
    const userId = req.user._id || (req.user as any).id;
    const { profileId } = req.body;

    if (!profileId) {
        return res.status(400).json({ message: 'Profile ID is required' });
    }

    try {
        const user: IUser | null = await User.findById(userId);

        if (!user) {
            return res.status(404).json({ message: 'User not found' });
        }

        // Update the last used profile ID
        user.lastUsedProfileId = profileId;
        await user.save();

        // Return success message
        res.status(200).json({
            message: 'Last used profile updated successfully',
            lastUsedProfileId: profileId
        });
    } catch (error) {
        console.error('Update Last Used Profile Error:', error);
        res.status(500).json({ message: 'Server error updating last used profile' });
    }
};

// @desc    Handle Google OAuth Callback
// @route   GET /api/auth/google/callback (executed after passport.authenticate)
// @access  Public (but requires successful Google auth)
export const googleCallback = (req: Request, res: Response) => {
  // Passport attaches the authenticated user to req.user
  if (!req.user) {
    // Should ideally not happen if passport.authenticate succeeded
    console.error('Google OAuth callback reached without a valid user.');
    return res.redirect(`${process.env.FRONTEND_URL || 'http://localhost:5173'}/login?error=google_auth_failed`);
  }

  try {
    // Get the user ID from req.user
    const userId = req.user._id || (req.user as any).id;

    if (!userId) {
      console.error('Google OAuth callback: User ID not found in user object');
      return res.redirect(`${process.env.FRONTEND_URL || 'http://localhost:5173'}/login?error=google_auth_failed`);
    }

    // Generate JWT for the authenticated user
    const token = generateToken(userId);

    // Redirect user back to the frontend, passing the token as a query parameter
    // The frontend will need to capture this token and store it.
    res.redirect(`${process.env.FRONTEND_URL || 'http://localhost:5173'}/auth/callback?token=${token}`);

  } catch (error) {
    console.error('Error generating token or redirecting after Google OAuth:', error);
    res.redirect(`${process.env.FRONTEND_URL || 'http://localhost:5173'}/login?error=token_generation_failed`);
  }
};
