import React, { useState, useEffect, useRef } from 'react';
import { useConfigStore } from '../store/configStore';

// Import the base URL from config is no longer needed here as we use apiClient
// which already has the base URL configured

// Interface for model data
interface ModelData {
  id: string;
  name: string;
  contextWindow?: number;
  maxOutputTokens?: number;
}

// Function to normalize model data from different formats
const normalizeModelData = (data: any[]): ModelData[] => {
  // Keep existing implementation - it looks robust
  return data.map(item => {
    if (typeof item === 'string') {
      return { id: item, name: item };
    }
    if (typeof item === 'object' && item !== null) {
      return {
        id: item.id || item.name || '',
        name: item.name || item.id || '',
        contextWindow: item.contextWindow,
        maxOutputTokens: item.maxOutputTokens
      };
    }
    return { id: String(item), name: String(item) };
  });
}

/**
 * Component for configuring search agent settings
 */
const SearchAgentSettingsTab: React.FC = () => {
  const {
    searchAgentSettings,
    updateSearchAgentSettings,
    internetSettings,
    token,
    isAuthenticated
  } = useConfigStore();

  // Default values if not set
  // Ensure searchAgentSettings exists before accessing properties
  const provider = searchAgentSettings?.provider || 'Google';
  const model = searchAgentSettings?.model || 'gemini-2.0-flash';
  const temperature = searchAgentSettings?.temperature ?? 0.3; // Use nullish coalescing for numbers/booleans
  const maxTokens = searchAgentSettings?.maxTokens ?? 4000;
  const systemPrompt = searchAgentSettings?.systemPrompt || '';

  // Get agent configurations to check for matching providers
  const agentConfigurations = useConfigStore((state) => state.agentConfigurations);

  // Check if the current provider is used by any agent configuration's provider setting
  // Memoize this calculation if agentConfigurations is large or updates frequently, otherwise direct calculation is fine.
  const isProviderUsedByAgent = agentConfigurations.some(agent =>
    agent.provider && agent.provider.toLowerCase() === provider.toLowerCase()
  );

  // Get unique providers available from agent configurations
  // Memoize this calculation if agentConfigurations is large or updates frequently.
  const availableProvidersFromAgents = React.useMemo(() =>
    [...new Set(agentConfigurations
      .filter(agent => agent.provider) // Ensure provider exists
      .map(agent => agent.provider) // Get provider strings
    )], [agentConfigurations]
  );

  // Log for debugging
  // console.log(`[SearchAgent] Current provider: ${provider}, Used by agent: ${isProviderUsedByAgent}`);
  // console.log(`[SearchAgent] Available providers from agents:`, availableProvidersFromAgents);

  // State for providers and models
  const [providers, setProviders] = useState<string[]>([]);
  const [models, setModels] = useState<ModelData[]>([]);
  const [providersLoading, setProvidersLoading] = useState<boolean>(true);
  const [modelsLoading, setModelsLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);

  // Use ref to track if provider has changed, needed for resetting model selection logic
  const providerRef = useRef<string | undefined>(provider);

  // Fetch providers on mount or when auth changes
  useEffect(() => {
    if (!isAuthenticated) {
      setProviders([]);
      setProvidersLoading(false); // Ensure loading is set to false
      return;
    }

    const fetchProviders = async () => {
      setProvidersLoading(true);
      setError(null);
      let fetchedProviders: string[] = [];
      try {
        // Import apiClient to ensure auth token is included in all requests
        const apiClient = await import('../services/authApiService').then(module => module.default);

        const response = await apiClient.get('/api/llm/providers');
        const data = response.data;

        if (!Array.isArray(data)) throw new Error('Invalid data format received for providers');
        fetchedProviders = data;
        console.log(`[SearchAgent] Fetched providers from API:`, fetchedProviders);
      } catch (fetchError: any) {
        console.error(`[SearchAgent] Error fetching providers:`, fetchError);
        setError(`Failed to load providers from API: ${fetchError.message || 'Unknown error'}. ` +
                 (availableProvidersFromAgents.length > 0 ? 'Falling back to providers configured in agents.' : ''));
        // Fallback happens naturally below by combining with availableProvidersFromAgents
      } finally {
        // Combine unique API providers with unique providers from agent configurations
        const combinedProviders = [...new Set([...fetchedProviders, ...availableProvidersFromAgents])];
        console.log(`[SearchAgent] Combined providers list:`, combinedProviders);
        setProviders(combinedProviders);
        setProvidersLoading(false);
      }
    };

    fetchProviders();
    // Dependencies: Run only when auth state changes or available agent providers change.
    // If availableProvidersFromAgents caused loops, revert to [isAuthenticated, token]
  }, [isAuthenticated, token, availableProvidersFromAgents]);


  /*
   * REMOVED useEffect for automatic provider switching.
   * Rationale: Give user control. Warnings and backend checks handle compatibility/key issues.
   */
  // useEffect(() => {
  //   // ... code that automatically switched provider ...
  // }, [isProviderUsedByAgent, availableProvidersFromAgents, provider, searchAgentSettings, updateSearchAgentSettings, providersLoading, providers]);


  // Use a ref to track if we need to *automatically* update the model
  // This should only happen when the provider *changes* or on initial load.
  const shouldAutoUpdateModel = useRef<boolean>(true);

  // Fetch models when the selected provider changes
  useEffect(() => {
    // Reset model auto-update flag if provider has changed since last run
    if (provider !== providerRef.current) {
        shouldAutoUpdateModel.current = true;
        providerRef.current = provider; // Update ref to current provider
    }

    if (!isAuthenticated || !provider) {
      setModels([]);
      return;
    }

    const fetchModels = async () => {
      setModelsLoading(true);
      // Don't clear errors here, provider errors might still be relevant
      // setError(null);
      setModels([]); // Clear previous models immediately

      try {
        // Import apiClient to ensure auth token is included in all requests
        const apiClient = await import('../services/authApiService').then(module => module.default);

        console.log(`[SearchAgent] Fetching models for provider: ${provider}`);
        const response = await apiClient.get(`/api/llm/models/${provider}`);
        const rawData = response.data;

        if (!Array.isArray(rawData)) throw new Error('Invalid data format received for models');
        console.log(`[SearchAgent] Received ${rawData.length} models for provider: ${provider}`);

        const normalizedModels = normalizeModelData(rawData);
        setModels(normalizedModels);

        // Automatically select the first model ONLY if the flag is set (provider changed)
        // or if the current model isn't in the new list.
        const currentModelExists = normalizedModels.some(m => m.id === model);
        if (normalizedModels.length > 0 && (shouldAutoUpdateModel.current || !currentModelExists)) {
          console.log(`[SearchAgent] Auto-updating model from '${model}' to '${normalizedModels[0].id}' because provider changed or current model invalid.`);
          updateSearchAgentSettings({
            // Use callback form to ensure we have latest settings state
            ...(useConfigStore.getState().searchAgentSettings ?? {}), // Get latest state
            provider: provider, // Ensure provider is current
            model: normalizedModels[0].id
          });
          shouldAutoUpdateModel.current = false; // Reset flag after auto-update
        } else if (normalizedModels.length === 0) {
            // If no models are returned, clear the selected model
             updateSearchAgentSettings({
                ...(useConfigStore.getState().searchAgentSettings ?? {}), // Get latest state
                provider: provider, // Ensure provider is current
                model: ''
             });
             console.warn(`[SearchAgent] No models returned for provider: ${provider}. Clearing model selection.`);
        }

      } catch (error: any) {
        console.error(`[SearchAgent] Error fetching models:`, error);
        setError(`Failed to load models for ${provider}: ${error.message || 'Unknown error.'}`);
        setModels([]); // Ensure models are empty on error

        // Optionally, attempt to set a fallback/default model if the fetch fails
        // This logic seems reasonable to keep.
        // Ensure we don't overwrite an existing valid model if the API call fails temporarily
        const currentModelStillValid = models.some(m => m.id === model);
        if (!currentModelStillValid) {
            const defaultModels: Record<string, string> = { /* ... your defaults ... */ };
            const defaultModel = defaultModels[provider.toLowerCase()];
            if (defaultModel) {
                 console.log(`[SearchAgent] Setting default model for ${provider} due to fetch error: ${defaultModel}`);
                 updateSearchAgentSettings({
                    ...(useConfigStore.getState().searchAgentSettings ?? {}), // Get latest state
                    provider: provider, // Ensure provider is current
                    model: defaultModel
                 });
            } else {
                 // Clear model if no default exists for this provider
                 updateSearchAgentSettings({
                    ...(useConfigStore.getState().searchAgentSettings ?? {}), // Get latest state
                    provider: provider, // Ensure provider is current
                    model: ''
                 });
            }
        }

      } finally {
        setModelsLoading(false);
      }
    };

    fetchModels();
    // Dependencies: Run when auth state changes, token changes, or provider changes.
  }, [isAuthenticated, token, provider, updateSearchAgentSettings]); // Include updateSearchAgentSettings


  // Handle provider change
  const handleProviderChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const newProvider = e.target.value;
    console.log(`[SearchAgent] Provider changed to: ${newProvider}`);
    // Update settings - model will be updated by the useEffect when models are fetched
    updateSearchAgentSettings({
      // Use callback form or get latest state if needed
      ...(useConfigStore.getState().searchAgentSettings ?? {}),
      provider: newProvider,
      model: '' // Clear model immediately while new ones load
    });
    // No need to manage shouldAutoUpdateModel ref here, the effect handles it
  };

  // Handle model change
  const handleModelChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    updateSearchAgentSettings({
      ...(searchAgentSettings ?? {}), // Can use existing state here
      model: e.target.value
    });
  };

  // Handle temperature change
  const handleTemperatureChange = (_: Event | null, newValue: number | number[]) => {
    updateSearchAgentSettings({
      ...(searchAgentSettings ?? {}),
      temperature: newValue as number
    });
  };

  // Handle max tokens change
  const handleMaxTokensChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = parseInt(e.target.value);
    // Allow setting empty or 0 temporarily, maybe validate on save later?
    // Or enforce minimum here:
    const newValue = !isNaN(value) && value >= 100 ? value : 100; // Example: enforce min 100
    updateSearchAgentSettings({
      ...(searchAgentSettings ?? {}),
      maxTokens: newValue // Use validated/clamped value
    });
  };

  // Handle system prompt change
  const handleSystemPromptChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    updateSearchAgentSettings({
      ...(searchAgentSettings ?? {}),
      systemPrompt: e.target.value
    });
  };

  // ----- JSX Rendering -----
  // (Keep the existing JSX structure, but update the warning/note text if needed)

  return (
    <div className="p-4 space-y-6">
      <h4 className="text-lg font-semibold mb-4 text-gray-800 dark:text-gray-200">
        Search Agent Settings
      </h4>

      {/* Internet Disabled Warning */}
      {!internetSettings.enabled && (
        <div className="text-red-600 dark:text-red-400 mb-4 p-3 bg-red-50 dark:bg-red-900/30 border border-red-200 dark:border-red-800 rounded-md">
          Note: Internet search is currently disabled. Enable it in the Internet Settings tab to use the search agent.
        </div>
      )}

      {/* Provider Compatibility Warning (Information only) */}
      {internetSettings.enabled && provider && !isProviderUsedByAgent && (
         <div className="text-yellow-600 dark:text-yellow-400 mb-4 p-3 bg-yellow-50 dark:bg-yellow-900/30 border border-yellow-200 dark:border-yellow-800 rounded-md">
           Warning: The selected provider ({provider}) is not explicitly configured in your main Agent Settings. Ensure you have added the necessary API key for this provider in your global API key settings for the search agent to function correctly.
         </div>
      )}

      {/* General Error Display */}
      {error && (
        <div className="text-red-600 dark:text-red-400 mb-4 p-3 bg-red-50 dark:bg-red-900/30 border border-red-200 dark:border-red-800 rounded-md">
          Error: {error}
        </div>
      )}

      {/* Provider Dropdown */}
      <div className="mb-4">
        <label htmlFor="search-provider" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Provider</label>
        <select
          id="search-provider"
          className="mt-1 block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 disabled:opacity-50"
          value={provider}
          onChange={handleProviderChange}
          disabled={!internetSettings.enabled || providersLoading}
        >
          {providersLoading ? (
            <option value="">Loading providers...</option>
          ) : providers.length === 0 ? (
            <option value="">No providers available</option>
          ) : (
            // Sort providers: used by agent first, then alphabetically
            [...providers].sort((a, b) => {
              const aUsed = agentConfigurations.some(agent => agent.provider && agent.provider.toLowerCase() === a.toLowerCase());
              const bUsed = agentConfigurations.some(agent => agent.provider && agent.provider.toLowerCase() === b.toLowerCase());
              if (aUsed && !bUsed) return -1;
              if (!aUsed && bUsed) return 1;
              return a.localeCompare(b);
            }).map(p => (
              <option key={p} value={p}>
                {/* Capitalize first letter for display */}
                {p.charAt(0).toUpperCase() + p.slice(1)}
                {agentConfigurations.some(agent => agent.provider && agent.provider.toLowerCase() === p.toLowerCase()) ? ' (Used by Agent)' : ''}
              </option>
            ))
          )}
        </select>
      </div>

      {/* Model Dropdown */}
      <div className="mb-4">
        <label htmlFor="search-model" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Model</label>
        <select
          id="search-model"
          className="mt-1 block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 disabled:opacity-50"
          value={model}
          onChange={handleModelChange}
          disabled={!internetSettings.enabled || modelsLoading || models.length === 0 || !provider}
        >
          {modelsLoading ? (
            <option value="">Loading models for {provider}...</option>
          ) : models.length === 0 ? (
            <option value="">{provider ? `No models available for ${provider}` : 'Select a provider first'}</option>
          ) : (
            models.map((m) => (
              // Use model id as key, ensure it's unique
              <option key={m.id} value={m.id}>{m.name || m.id}</option>
            ))
          )}
        </select>
         {/* Optional: Display context window/max output if available */}
         {/* {selectedModelDetails && (
             <p className="mt-1 text-xs text-gray-500 dark:text-gray-400">
                Context: {selectedModelDetails.contextWindow || 'N/A'} tokens | Max Output: {selectedModelDetails.maxOutputTokens || 'N/A'} tokens
             </p>
         )} */}
      </div>

      {/* Temperature Slider */}
      <div className="mb-4">
        <label htmlFor="search-temperature" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Temperature: {temperature.toFixed(2)}</label>
        <input
          id="search-temperature"
          type="range"
          className="w-full h-2 bg-gray-200 dark:bg-gray-700 rounded-lg appearance-none cursor-pointer disabled:opacity-50"
          min="0"
          max="1" // Or 2, depending on provider capabilities
          step="0.01"
          value={temperature}
          onChange={(e) => handleTemperatureChange(null, parseFloat(e.target.value))}
          disabled={!internetSettings.enabled}
        />
        <div className="flex justify-between text-xs mt-1 text-gray-500 dark:text-gray-400">
          <span>More Focused</span>
          <span>More Creative</span>
        </div>
      </div>

      {/* Max Tokens Input */}
      <div className="mb-4">
        <label htmlFor="search-max-tokens" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Max Tokens</label>
        <input
          id="search-max-tokens"
          type="number"
          className="mt-1 block w-36 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 disabled:opacity-50"
          min="100"
          // Max depends on the selected model, 4000 is a reasonable default max limit for the input field itself
          max="16384" // Increased potential max
          step="100" // Larger step might be user-friendly
          value={maxTokens}
          onChange={handleMaxTokensChange}
          disabled={!internetSettings.enabled}
        />
        <p className="mt-1 text-xs text-gray-500 dark:text-gray-400">
          Maximum number of tokens in the search agent's response (e.g., 1500-4000).
        </p>
      </div>

      {/* System Prompt Textarea */}
      <div className="mb-4">
        <label htmlFor="search-system-prompt" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">System Prompt</label>
        <textarea
          id="search-system-prompt"
          rows={10}
          className="mt-1 block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 disabled:opacity-50"
          onChange={handleSystemPromptChange}
          disabled={!internetSettings.enabled}
          placeholder="Enter system prompt for the search agent..."
          value={systemPrompt}
        />
        <p className="mt-1 text-xs text-gray-500 dark:text-gray-400">
          System prompt that guides how the search agent processes and synthesizes search results.
        </p>
      </div>
      {/* OpenRouter Advanced Parameters */}
      {provider === 'OpenRouter' && (
        <div className="mb-4 p-4 border border-blue-200 dark:border-blue-700 rounded-md bg-blue-50 dark:bg-blue-900/20">
          <h4 className="text-sm font-semibold text-blue-800 dark:text-blue-200 mb-3">OpenRouter Advanced Parameters</h4>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-x-4 gap-y-2">
            {/* Top K */}
            <div>
              <label htmlFor="openrouter-top-k" className="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">Top K</label>
              <input
                id="openrouter-top-k"
                type="number"
                min="0"
                step="1"
                value={searchAgentSettings?.topK ?? 0}
                onChange={e => updateSearchAgentSettings({ ...(searchAgentSettings ?? {}), topK: parseInt(e.target.value) })}
                className="w-full px-2 py-1 border border-gray-300 dark:border-gray-600 rounded bg-gray-50 dark:bg-gray-800"
                disabled={!internetSettings.enabled}
              />
            </div>
            {/* Repetition Penalty */}
            <div>
              <label htmlFor="openrouter-repetition-penalty" className="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">Repetition Penalty</label>
              <input
                id="openrouter-repetition-penalty"
                type="number"
                min="0"
                max="2"
                step="0.01"
                value={searchAgentSettings?.repetitionPenalty ?? 1.0}
                onChange={e => updateSearchAgentSettings({ ...(searchAgentSettings ?? {}), repetitionPenalty: parseFloat(e.target.value) })}
                className="w-full px-2 py-1 border border-gray-300 dark:border-gray-600 rounded bg-gray-50 dark:bg-gray-800"
                disabled={!internetSettings.enabled}
              />
            </div>
            {/* Min P */}
            <div>
              <label htmlFor="openrouter-min-p" className="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">Min P</label>
              <input
                id="openrouter-min-p"
                type="number"
                min="0"
                max="1"
                step="0.01"
                value={searchAgentSettings?.minP ?? 0.0}
                onChange={e => updateSearchAgentSettings({ ...(searchAgentSettings ?? {}), minP: parseFloat(e.target.value) })}
                className="w-full px-2 py-1 border border-gray-300 dark:border-gray-600 rounded bg-gray-50 dark:bg-gray-800"
                disabled={!internetSettings.enabled}
              />
            </div>
            {/* Top A */}
            <div>
              <label htmlFor="openrouter-top-a" className="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">Top A</label>
              <input
                id="openrouter-top-a"
                type="number"
                min="0"
                max="1"
                step="0.01"
                value={searchAgentSettings?.topA ?? 0.0}
                onChange={e => updateSearchAgentSettings({ ...(searchAgentSettings ?? {}), topA: parseFloat(e.target.value) })}
                className="w-full px-2 py-1 border border-gray-300 dark:border-gray-600 rounded bg-gray-50 dark:bg-gray-800"
                disabled={!internetSettings.enabled}
              />
            </div>
            {/* Seed */}
            <div>
              <label htmlFor="openrouter-seed" className="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">Seed</label>
              <input
                id="openrouter-seed"
                type="number"
                step="1"
                value={searchAgentSettings?.seed ?? ''}
                onChange={e => updateSearchAgentSettings({ ...(searchAgentSettings ?? {}), seed: e.target.value ? parseInt(e.target.value) : undefined })}
                className="w-full px-2 py-1 border border-gray-300 dark:border-gray-600 rounded bg-gray-50 dark:bg-gray-800"
                disabled={!internetSettings.enabled}
              />
            </div>
            {/* Top Logprobs */}
            <div>
              <label htmlFor="openrouter-top-logprobs" className="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">Top Logprobs</label>
              <input
                id="openrouter-top-logprobs"
                type="number"
                min="0"
                max="20"
                step="1"
                value={searchAgentSettings?.topLogprobs ?? ''}
                onChange={e => updateSearchAgentSettings({ ...(searchAgentSettings ?? {}), topLogprobs: e.target.value ? parseInt(e.target.value) : undefined })}
                className="w-full px-2 py-1 border border-gray-300 dark:border-gray-600 rounded bg-gray-50 dark:bg-gray-800"
                disabled={!internetSettings.enabled}
              />
            </div>
            {/* Logprobs */}
            <div className="flex items-center mt-5">
              <input
                id="openrouter-logprobs"
                type="checkbox"
                checked={!!searchAgentSettings?.logprobs}
                onChange={e => updateSearchAgentSettings({ ...(searchAgentSettings ?? {}), logprobs: e.target.checked })}
                className="mr-2"
                disabled={!internetSettings.enabled}
              />
              <label htmlFor="openrouter-logprobs" className="text-xs font-medium text-gray-700 dark:text-gray-300">Logprobs</label>
            </div>
            {/* Structured Outputs */}
            <div className="flex items-center mt-5">
              <input
                id="openrouter-structured-outputs"
                type="checkbox"
                checked={!!searchAgentSettings?.structuredOutputs}
                onChange={e => updateSearchAgentSettings({ ...(searchAgentSettings ?? {}), structuredOutputs: e.target.checked })}
                className="mr-2"
                disabled={!internetSettings.enabled}
              />
              <label htmlFor="openrouter-structured-outputs" className="text-xs font-medium text-gray-700 dark:text-gray-300">Structured Outputs</label>
            </div>
            {/* Logit Bias (JSON) */}
            <div className="col-span-2">
              <label htmlFor="openrouter-logit-bias" className="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">Logit Bias (JSON)</label>
              <input
                id="openrouter-logit-bias"
                type="text"
                value={searchAgentSettings?.logitBias ? JSON.stringify(searchAgentSettings.logitBias) : ''}
                onChange={e => {
                  let val = {};
                  try { val = JSON.parse(e.target.value); } catch {}
                  updateSearchAgentSettings({ ...(searchAgentSettings ?? {}), logitBias: val });
                }}
                className="w-full px-2 py-1 border border-gray-300 dark:border-gray-600 rounded bg-gray-50 dark:bg-gray-800"
                placeholder='e.g. {"123": -100, "456": 100}'
                disabled={!internetSettings.enabled}
              />
            </div>
            {/* Response Format (JSON) */}
            <div className="col-span-2">
              <label htmlFor="openrouter-response-format" className="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">Response Format (JSON)</label>
              <input
                id="openrouter-response-format"
                type="text"
                value={searchAgentSettings?.responseFormat ? JSON.stringify(searchAgentSettings.responseFormat) : ''}
                onChange={e => {
                  let val = {};
                  try { val = JSON.parse(e.target.value); } catch {}
                  updateSearchAgentSettings({ ...(searchAgentSettings ?? {}), responseFormat: val });
                }}
                className="w-full px-2 py-1 border border-gray-300 dark:border-gray-600 rounded bg-gray-50 dark:bg-gray-800"
                placeholder='e.g. {"type": "json_object"}'
                disabled={!internetSettings.enabled}
              />
            </div>
            {/* Stop (comma-separated tokens) */}
            <div className="col-span-2">
              <label htmlFor="openrouter-stop" className="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">Stop (comma-separated tokens)</label>
              <input
                id="openrouter-stop"
                type="text"
                value={Array.isArray(searchAgentSettings?.stop) ? searchAgentSettings.stop.join(',') : ''}
                onChange={e => updateSearchAgentSettings({ ...(searchAgentSettings ?? {}), stop: e.target.value.split(',').map(s => s.trim()).filter(Boolean) })}
                className="w-full px-2 py-1 border border-gray-300 dark:border-gray-600 rounded bg-gray-50 dark:bg-gray-800"
                placeholder='e.g. <|end|>,<|stop|>'
                disabled={!internetSettings.enabled}
              />
            </div>
            {/* Tool Choice (JSON) */}
            <div className="col-span-2">
              <label htmlFor="openrouter-tool-choice" className="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">Tool Choice (JSON)</label>
              <input
                id="openrouter-tool-choice"
                type="text"
                value={searchAgentSettings?.toolChoice ? JSON.stringify(searchAgentSettings.toolChoice) : ''}
                onChange={e => {
                  let val = {};
                  try { val = JSON.parse(e.target.value); } catch {}
                  updateSearchAgentSettings({ ...(searchAgentSettings ?? {}), toolChoice: val });
                }}
                className="w-full px-2 py-1 border border-gray-300 dark:border-gray-600 rounded bg-gray-50 dark:bg-gray-800"
                placeholder='e.g. {"type": "function", "function": {"name": "my_function"}}'
                disabled={!internetSettings.enabled}
              />
            </div>
            {/* Max Price (JSON) */}
            <div className="col-span-2">
              <label htmlFor="openrouter-max-price" className="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">Max Price (JSON)</label>
              <input
                id="openrouter-max-price"
                type="text"
                value={searchAgentSettings?.maxPrice ? JSON.stringify(searchAgentSettings.maxPrice) : ''}
                onChange={e => {
                  let val = {};
                  try { val = JSON.parse(e.target.value); } catch {}
                  updateSearchAgentSettings({ ...(searchAgentSettings ?? {}), maxPrice: val });
                }}
                className="w-full px-2 py-1 border border-gray-300 dark:border-gray-600 rounded bg-gray-50 dark:bg-gray-800"
                placeholder='e.g. {"prompt": 1, "completion": 2}'
                disabled={!internetSettings.enabled}
              />
            </div>
          </div>
        </div>
      )}

      {/* Informational Text */}
      <p className="mt-6 text-sm text-gray-600 dark:text-gray-400">
        The search agent processes web search results using the selected provider and model to synthesize information, aiming to provide concise and relevant answers based on the search data.
      </p>

      {/* API Key Note */}
      <div className="mt-4 p-3 bg-yellow-50 dark:bg-yellow-900/30 border border-yellow-200 dark:border-yellow-800 rounded-md">
        <h5 className="text-sm font-medium text-yellow-800 dark:text-yellow-300">API Key Required</h5>
        <p className="text-xs text-yellow-700 dark:text-yellow-400 mt-1">
          The search agent requires a valid API key for the selected provider ({provider || 'None selected'}) to function. Please ensure the necessary key is configured in the global API key settings. It utilizes the same keys as your main agents.
        </p>
      </div>
    </div>
  );
};

export default SearchAgentSettingsTab;

