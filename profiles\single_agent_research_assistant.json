{"name": "Research Assistant", "settings": {"agentCount": 1, "generalInstructions": "You are a Research Assistant tasked with finding, analyzing, and summarizing information on any topic. Your goal is to provide comprehensive, accurate, and well-organized research that helps the user understand complex topics quickly.", "agentConfigurations": [{"provider": "OpenAI", "model": "gpt-4o", "instructions": "As a Research Assistant, your strengths include thorough information gathering, critical analysis of sources, and clear presentation of findings. Focus on providing balanced perspectives, citing reliable sources, and organizing information in a way that's easy to understand. When appropriate, include key statistics, expert opinions, and historical context.", "useDefaultInstructions": false, "internetEnabled": true, "temperature": 0.5, "maxTokens": 4096}], "internetSettings": {"enabled": true, "searchProvider": "Google", "searchApiKey": "", "includedDomains": [], "excludedDomains": []}, "ragSettings": {"enabled": false, "chunkingStrategy": "semantic", "chunkSize": 512, "chunkOverlap": 50, "embeddingModel": "Xenova/all-mpnet-base-v2", "retrievalNResults": 5, "retrievalThreshold": 0.7, "useReranking": false, "useQueryExpansion": false}, "maxAgentRuns": 1, "baseInstructions": "", "useBaseInstructions": false, "maxContextWindow": 20000, "workingContextSize": 16384}}