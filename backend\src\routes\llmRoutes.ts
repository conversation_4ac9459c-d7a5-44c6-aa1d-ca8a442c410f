import express from 'express';
import { getLocalModels, getProviders, getModelsForProvider, getApiKey } from '../controllers/llmController'; // Import controller functions
import { protect } from '../middleware/authMiddleware'; // Import authentication middleware

const router = express.Router();

// Apply authentication middleware only to sensitive routes
// Public routes
router.get('/providers', getProviders);
router.get('/local-models/:provider', getLocalModels);

// Protected routes
router.use('/key', protect);
router.use('/models', protect);

// --- Routes for general providers/models (moved from server.ts) ---
// Route for models is defined with protection middleware above
router.get('/models/:provider', getModelsForProvider);

// Route to get API key for a specific service
router.get('/key/:serviceName', getApiKey);
// --- End General Routes ---

export default router;
