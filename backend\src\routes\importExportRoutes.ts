import express from 'express';
import {
  exportProfile,
  getExampleProfiles,
  getExampleProfile
} from '../controllers/importExportController';
import { protect } from '../middleware/authMiddleware';

const router = express.Router();

// Export route
router.get('/export/:id', protect, exportProfile);

// Example profiles routes
router.get('/examples', protect, getExampleProfiles);
router.get('/examples/:filename', protect, getExampleProfile);

export default router;
