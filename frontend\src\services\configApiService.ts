// Remove local axios import, import the configured client from authApiService
// import axios from 'axios';
import apiClient from './authApiService'; // Import the configured apiClient
// Removed unused useConfigStore import
import { LoadableConfigurationSettings } from '../store/configStore'; // Import type

// Define the structure for the settings part of the profile, matching the store/backend model
// (Excluding actions and transient state like isProcessing)
interface ConfigurationSettingsData {
  agentCount: number;
  generalInstructions: string;
  agentConfigurations: {
    provider: string;
    model: string;
    instructions: string;
  }[];
  internetSettings: {
    enabled: boolean;
    searchProvider: string;
    searchApiKey: string;
    includedDomains: string[];
    excludedDomains: string[];
  };
  ragSettings: {
    enabled: boolean;
    chunkingStrategy: string;
    chunkSize: number;
    chunkOverlap: number;
    embeddingModel: string;
    retrievalNResults: number;
    retrievalThreshold: number;
    useReranking: boolean;
    useQueryExpansion: boolean;
  };
  // Add the missing field to match the store and backend schema
  userManagedModels?: Record<string, {
    added: string[];
    removed: string[];
    lastSelected?: string;
  }>;
  // Include other fields if they exist in LoadableConfigurationSettings but not here yet
  maxAgentRuns?: number;
  baseInstructions?: string;
  useBaseInstructions?: boolean;
}

// Interface for the profile data returned by the list endpoint
export interface ProfileListItem {
  _id: string;
  name: string;
  createdAt: string;
  updatedAt: string;
}

// Interface for the full profile data returned by getById
export interface FullProfileData {
    _id: string;
    name: string;
    settings: ConfigurationSettingsData;
    createdAt: string;
    updatedAt: string;
}


// Remove redundant API_BASE_URL and local apiClient creation
// const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:3000/api';
// const apiClient = axios.create({
//   baseURL: API_BASE_URL,
// });

// Function to extract savable settings from the Zustand store - NO LONGER USED HERE
// const getSettingsForSave = (): LoadableConfigurationSettings => {
//   const state = useConfigStore.getState();
//   return {
//     agentCount: state.agentCount, // Keep existing fields
//     generalInstructions: state.generalInstructions,
//     agentConfigurations: state.agentConfigurations,
//     internetSettings: state.internetSettings,
//     ragSettings: state.ragSettings,
//     // Include the missing fields when getting settings from the store
//     userManagedModels: state.userManagedModels,
//     maxAgentRuns: state.maxAgentRuns,
//     baseInstructions: state.baseInstructions,
//     useBaseInstructions: state.useBaseInstructions,
//   };
// };

// --- API Service Functions ---

/**
 * Saves the current configuration as a new profile.
 * @param name The name for the new profile.
 * @param settings The configuration settings object to save.
 * @returns The saved profile data.
 */
export const saveConfigurationProfile = async (name: string, settings: LoadableConfigurationSettings): Promise<FullProfileData> => {
  // Settings are passed directly now
  try {
    // Use the imported apiClient which has the interceptor
    const response = await apiClient.post('/api/config/profiles', { name, settings });
    return response.data;
  } catch (error) {
    console.error('Error saving configuration profile:', error);
    // Rethrow or handle error appropriately for the UI
    throw error;
  }
};

/**
 * Fetches the list of saved configuration profiles (ID and name).
 * @returns An array of profile list items.
 */
export const getConfigurationProfiles = async (): Promise<ProfileListItem[]> => {
  try {
    const response = await apiClient.get('/api/config/profiles');
    return response.data;
  } catch (error) {
    console.error('Error fetching configuration profiles:', error);
    throw error;
  }
};

/**
 * Fetches the full details of a specific configuration profile by its ID.
 * @param id The ID of the profile to fetch.
 * @returns The full profile data including settings.
 */
export const getConfigurationProfileById = async (id: string): Promise<FullProfileData> => {
  try {
    const response = await apiClient.get(`/api/config/profiles/${id}`);
    return response.data;
  } catch (error) {
    console.error(`Error fetching configuration profile with ID ${id}:`, error);
    throw error;
  }
};

/**
 * Updates an existing configuration profile with the current settings.
 * @param id The ID of the profile to update.
 * @param data An object containing the new name and settings.
 * @returns The updated profile data.
 */
export const updateConfigurationProfile = async (id: string, data: { name: string, settings: LoadableConfigurationSettings }): Promise<FullProfileData> => {
   // Settings are passed directly in the 'data' object
   const payload = data;
   // if (name) { // Logic moved to saveCurrentProfile action
   //     payload.name = name;
   // }
  try {
    // Use the imported apiClient which has the interceptor
    const response = await apiClient.put(`/api/config/profiles/${id}`, payload);
    return response.data;
  } catch (error) {
    console.error(`Error updating configuration profile with ID ${id}:`, error);
    throw error;
  }
};


/**
 * Deletes a specific configuration profile by its ID.
 * @param id The ID of the profile to delete.
 * @returns Success message or confirmation.
 */
export const deleteConfigurationProfile = async (id: string): Promise<{ message: string }> => {
  try {
    const response = await apiClient.delete(`/api/config/profiles/${id}`);
    return response.data; // Should contain { message: 'Profile deleted successfully' }
  } catch (error) {
    console.error(`Error deleting configuration profile with ID ${id}:`, error);
    throw error;
  }
};
