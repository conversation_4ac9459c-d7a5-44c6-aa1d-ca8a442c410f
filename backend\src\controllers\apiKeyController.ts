import { Request, Response } from 'express';
import <PERSON><PERSON><PERSON><PERSON>, { IApi<PERSON><PERSON> } from '../models/ApiKey';
import { encrypt, decrypt } from '../utils/cryptoUtils';
import { AuthUser } from '../types/express';

// Use the standard Request type with Express.User
type AuthenticatedRequest = Request;

// Controller function to get API keys for the logged-in user
export const getApiKeys = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
    if (!req.user?.id) {
        res.status(401).json({ message: 'User not authenticated' });
        return;
    }

    try {
        // Fetch keys for the user, selecting only necessary fields (exclude the apiKey itself)
        const keys = await ApiKey.find({ userId: req.user.id }).select('_id serviceName createdAt updatedAt');

        res.status(200).json(keys);
    } catch (error) {
        console.error('Error fetching API keys:', error);
        res.status(500).json({ message: 'Failed to fetch API keys' });
    }
};

// Controller function to add a new API key
export const addApiKey = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
    if (!req.user?.id) {
        res.status(401).json({ message: 'User not authenticated' });
        return;
    }

    const { serviceName, apiKey } = req.body;

    if (!serviceName || !apiKey) {
        res.status(400).json({ message: 'Service name and API key are required' });
        return;
    }

    // Encrypt the API key before saving
    const encryptedApiKey = encrypt(apiKey);
    if (!encryptedApiKey) {
        res.status(500).json({ message: 'Failed to encrypt API key. Check server logs and ENCRYPTION_SECRET.' });
        return;
    }

    try {
        // Check if a key for this service already exists for the user
        const existingKey = await ApiKey.findOne({ userId: req.user.id, serviceName });
        if (existingKey) {
            res.status(409).json({ message: `API key for service '${serviceName}' already exists. Use update instead.` });
            return;
        }

        const newApiKey = new ApiKey({
            userId: req.user.id,
            serviceName,
            apiKey: encryptedApiKey,
        });

        await newApiKey.save();

        // Return only safe information
        res.status(201).json({
            _id: newApiKey._id,
            serviceName: newApiKey.serviceName,
            createdAt: newApiKey.createdAt,
            updatedAt: newApiKey.updatedAt,
            message: 'API key added successfully'
        });

    } catch (error: any) {
        console.error('Error adding API key:', error);
        // Handle potential duplicate key error from the compound index
        if (error.code === 11000) {
             res.status(409).json({ message: `API key for service '${serviceName}' already exists.` });
        } else {
            res.status(500).json({ message: 'Failed to add API key' });
        }
    }
};

// Controller function to update an existing API key
export const updateApiKey = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
    if (!req.user?.id) {
        res.status(401).json({ message: 'User not authenticated' });
        return;
    }

    const { id } = req.params; // Get key ID from URL parameter
    const { apiKey } = req.body; // Only allow updating the key value itself

    if (!apiKey) {
        res.status(400).json({ message: 'New API key value is required' });
        return;
    }

    // Encrypt the new API key
    const encryptedApiKey = encrypt(apiKey);
     if (!encryptedApiKey) {
        res.status(500).json({ message: 'Failed to encrypt API key. Check server logs and ENCRYPTION_SECRET.' });
        return;
    }

    try {
        // Find the key by ID and ensure it belongs to the logged-in user
        const keyToUpdate = await ApiKey.findOne({ _id: id, userId: req.user.id });

        if (!keyToUpdate) {
            res.status(404).json({ message: 'API key not found or you do not have permission to update it' });
            return;
        }

        keyToUpdate.apiKey = encryptedApiKey;
        // Mongoose automatically updates the `updatedAt` timestamp due to { timestamps: true }
        await keyToUpdate.save();

         // Return only safe information
        res.status(200).json({
            _id: keyToUpdate._id,
            serviceName: keyToUpdate.serviceName,
            createdAt: keyToUpdate.createdAt,
            updatedAt: keyToUpdate.updatedAt, // Show the updated timestamp
            message: 'API key updated successfully'
        });

    } catch (error) {
        console.error('Error updating API key:', error);
        res.status(500).json({ message: 'Failed to update API key' });
    }
};

// Controller function to delete an API key
export const deleteApiKey = async (req: AuthenticatedRequest, res: Response): Promise<void> => {
    if (!req.user?.id) {
        res.status(401).json({ message: 'User not authenticated' });
        return;
    }

    const { id } = req.params; // Get key ID from URL parameter

    try {
        // Find the key by ID and ensure it belongs to the logged-in user before deleting
        const result = await ApiKey.deleteOne({ _id: id, userId: req.user.id });

        if (result.deletedCount === 0) {
            res.status(404).json({ message: 'API key not found or you do not have permission to delete it' });
            return;
        }

        res.status(200).json({ message: 'API key deleted successfully' });

    } catch (error) {
        console.error('Error deleting API key:', error);
        res.status(500).json({ message: 'Failed to delete API key' });
    }
};

// Utility function (internal use only, NOT exposed via API)
// To be used by backend services needing to retrieve and decrypt a key
export const getDecryptedApiKey = async (userId: string, serviceName: string): Promise<string | null> => {
    try {
        const apiKeyDoc = await ApiKey.findOne({ userId, serviceName });
        if (!apiKeyDoc || !apiKeyDoc.apiKey) {
            console.warn(`API key for service '${serviceName}' not found for user ${userId}`);
            return null;
        }

        const decryptedKey = decrypt(apiKeyDoc.apiKey);
        if (!decryptedKey) {
            console.error(`Failed to decrypt API key for service '${serviceName}' for user ${userId}. Check ENCRYPTION_SECRET.`);
            return null;
        }
        return decryptedKey;
    } catch (error) {
        console.error(`Error retrieving/decrypting API key for service '${serviceName}' for user ${userId}:`, error);
        return null;
    }
};
