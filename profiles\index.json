{"profiles": [{"name": "Research Assistant", "file": "single_agent_research_assistant.json", "agentCount": 1, "description": "A comprehensive research agent that can find, analyze, and summarize information on any topic."}, {"name": "Code Reviewer", "file": "single_agent_code_reviewer.json", "agentCount": 1, "description": "An agent specialized in reviewing code for bugs, security vulnerabilities, and adherence to best practices."}, {"name": "Content Creation Team", "file": "two_agent_content_creation.json", "agentCount": 2, "description": "A writer and editor team that works together to create high-quality content."}, {"name": "Debate Partners", "file": "two_agent_debate_partners.json", "agentCount": 2, "description": "Two agents that present different perspectives on a topic to provide a balanced view."}, {"name": "Software Development Team", "file": "three_agent_software_development.json", "agentCount": 3, "description": "A team consisting of an architect, developer, and tester that works together to design, implement, and test software solutions."}, {"name": "Travel Planning Team", "file": "three_agent_travel_planning.json", "agentCount": 3, "description": "A team of destination researcher, itinerary planner, and budget analyst that creates comprehensive travel plans."}, {"name": "Business Strategy Team", "file": "four_agent_business_strategy.json", "agentCount": 4, "description": "A team of market analyst, financial advisor, product strategist, and risk assessor that develops comprehensive business strategies."}, {"name": "Fantasy Adventure Party", "file": "four_agent_fantasy_adventure.json", "agentCount": 4, "description": "A fun role-playing team consisting of a wizard, warrior, rogue, and healer that approaches problems from different perspectives."}]}