// src/types/express/index.d.ts
import { IUser } from '../../models/User';
import { Types } from 'mongoose';

// Define a user interface for authenticated requests
interface AuthUser {
  _id: Types.ObjectId | string;
  id: string; // For compatibility with JWT payload
  email: string;
  googleId?: string;
  subscriptionTier: 'free' | 'basic' | 'premium';
  lastUsedProfileId?: Types.ObjectId | string;
  createdAt: Date;
  updatedAt: Date;
}

// Augment the Express namespace to add the 'user' property to the Request object
declare global {
  namespace Express {
    // Define the User property attached by auth middleware
    interface User extends AuthUser {}

    // Extend the Request interface
    interface Request {
      user?: User; // Make user optional as it's not always present
    }
  }
}

// Adding this export makes the file a module, which is necessary for augmentation
export { AuthUser };

