// Simple script to test the RAG system
import { <PERSON><PERSON><PERSON><PERSON><PERSON> } from './services/ragHandler';

async function testRag() {
  try {
    console.log('Initializing RAG handler...');
    const ragHandler = new RagHandler({
      embeddingModel: 'Xenova/all-MiniLM-L6-v2',
      chunkSize: 512,
      chunkOverlap: 50,
      retrievalThreshold: 0.5
    });

    // Wait for initialization
    await new Promise(resolve => setTimeout(resolve, 10000));

    console.log('Testing query: "Tell me about the B400 radar"');
    const results = await ragHandler.getRelevantChunks('Tell me about the B400 radar');

    console.log(`Retrieved ${results.length} chunks:`);
    results.forEach((chunk, i) => {
      console.log(`\nChunk ${i + 1}:`);
      console.log(`Content: ${chunk.content.substring(0, 150)}...`);
      console.log(`Source: ${chunk.metadata.source}`);
      console.log(`Similarity Score: ${chunk.similarity}`);
      if ('l2Distance' in chunk) {
        console.log(`L2 Distance: ${chunk.l2Distance}`);
      }
    });

    console.log('\nTest completed successfully!');
  } catch (error) {
    console.error('Error during RAG test:', error);
  }
}

testRag();
