import React, { useEffect, useState } from 'react';
import { useSearchParams, useNavigate } from 'react-router-dom';
import { useConfigStore } from '../store/configStore';

const AuthCallbackPage: React.FC = () => {
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const setAuthToken = useConfigStore((state) => state.setAuthToken);
  const [message, setMessage] = useState('Processing authentication...');

  useEffect(() => {
    const token = searchParams.get('token');
    const error = searchParams.get('error');

    if (token) {
      setMessage('Authentication successful! Redirecting...');
      setAuthToken(token)
        .then(() => {
          navigate('/app');
        })
        .catch((error) => {
          console.error('Failed to process auth token:', error);
          setMessage('Authentication failed during token processing. Redirecting to login...');
          setTimeout(() => navigate('/login'), 3000);
        });
    } else if (error) {
      setMessage(`Authentication failed: ${error}. Redirecting to login...`);
      const timer = setTimeout(() => {
        navigate('/login');
      }, 3000);
      return () => clearTimeout(timer);
    } else {
      setMessage('Invalid callback state. Redirecting to login...');
      const timer = setTimeout(() => {
        navigate('/login');
      }, 3000);
      return () => clearTimeout(timer);
    }
  }, [searchParams, navigate, setAuthToken]);

  return (
    <div className="flex justify-center items-center h-screen">
      <div className="text-center">
        <p>{message}</p>
        {/* Optional: Add a spinner here */}
      </div>
    </div>
  );
};

export default AuthCallbackPage;
