# Stage 1: Build the React application
FROM node:20-alpine AS build

# Set working directory
WORKDIR /app/frontend

# Copy package.json and package-lock.json (or yarn.lock)
COPY frontend/package*.json ./

# Install dependencies
RUN npm install

# Copy the rest of the frontend application code
COPY frontend/ .

# Build the application for production
# Pass VITE_API_BASE_URL and VITE_SOCKET_SERVER_URL as build arguments
# Default to empty strings if not provided during build
ARG VITE_API_BASE_URL=""
ARG VITE_SOCKET_SERVER_URL=""
ENV VITE_API_BASE_URL=${VITE_API_BASE_URL}
ENV VITE_SOCKET_SERVER_URL=${VITE_SOCKET_SERVER_URL}
RUN npm run build

# Stage 2: Serve the static files using a lightweight web server
FROM nginx:stable-alpine

# Copy the built static files from the build stage
COPY --from=build /app/frontend/dist /usr/share/nginx/html

# Copy custom nginx configuration to handle SPA routing
COPY nginx.conf /etc/nginx/conf.d/default.conf

# Expose port 80
EXPOSE 80

# Start nginx server
CMD ["nginx", "-g", "daemon off;"]
