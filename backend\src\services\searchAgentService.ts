import { SearchResult } from './internetSearchService';
import { callLL<PERSON>pi, LLMServiceInput } from './llmService';
import { decrypt } from '../utils/cryptoUtils';
import Api<PERSON><PERSON> from '../models/ApiKey';

/**
 * Interface for the search agent configuration
 */
interface SearchAgentConfig {
    provider: string;
    model: string;
    temperature: number;
    maxTokens?: number;
    apiKey?: string;
    systemPrompt?: string;
}

/**
 * Interface for the search agent response
 */
interface SearchAgentResponse {
    synthesizedContent: string;
    sources: string[];
    success: boolean;
    error?: string;
}

/**
 * Fetches and decrypts an API key for the search agent
 */
async function _getSearchAgentApiKey(userId: string, provider: string): Promise<string | undefined> {
    if (!userId || !provider) {
        console.warn(`[Search Agent] Missing userId or provider.`);
        return undefined;
    }

    // Map provider to service name
    const providerToServiceMap: { [key: string]: string } = {
        'openai': 'OPENAI_API_KEY',
        'anthropic': 'ANTHROPIC_API_KEY',
        'google': 'GOOGLE_API_KEY',
        'azure': 'AZURE_OPENAI_API_KEY',
        'groq': 'GROQ_API_KEY',
        'mistral': 'MISTRAL_API_KEY',
        'ollama': 'OLLAMA_API_KEY'
    };

    const serviceName = providerToServiceMap[provider.toLowerCase()];
    if (!serviceName) {
        console.warn(`[Search Agent] Unknown provider: ${provider}`);
        return undefined;
    }

    try {
        const apiKeyRecord = await ApiKey.findOne({ userId: userId, serviceName: serviceName });
        if (apiKeyRecord?.apiKey) {
            const decryptedResult = decrypt(apiKeyRecord.apiKey);
            if (decryptedResult !== null) {
                return decryptedResult;
            } else {
                console.error(`[Search Agent] Decryption failed for service: ${serviceName}, user: ${userId}.`);
            }
        }
    } catch (keyError: any) {
        console.error(`[Search Agent] Error fetching/decrypting key for ${serviceName}:`, keyError);
    }

    return undefined;
}

function cleanSearchContent(content: string): string {
    // Remove CSS variables and styling
    content = content.replace(/--[a-zA-Z0-9-]+:[^;]+;/g, '');
    content = content.replace(/\{[^}]+\}/g, '');

    // Remove HTML tags
    content = content.replace(/<[^>]+>/g, '');

    // Remove CSS classes and IDs
    content = content.replace(/\.[a-zA-Z0-9_-]+/g, '');
    content = content.replace(/#[a-zA-Z0-9_-]+/g, '');

    // Remove any remaining CSS/HTML artifacts
    content = content.replace(/(@media|\{|\}|\.dcr-|--[a-zA-Z-]+:|var\(--[^)]+\))/g, '');

    // Remove multiple spaces and newlines
    content = content.replace(/\s+/g, ' ').trim();

    // Restore paragraph breaks for readability
    content = content.replace(/\. /g, '.\n');

    return content;
}

/**
 * Processes search results using a dedicated LLM to synthesize information
 *
 * @param searchResults - The search results to process
 * @param userQuery - The original user query
 * @param userId - The user ID for API key retrieval
 * @param config - Configuration for the search agent
 * @returns A promise resolving to the processed search results
 */
export async function processSearchResults(
    searchResults: SearchResult[],
    userQuery: string,
    userId: string,
    config: SearchAgentConfig
): Promise<SearchAgentResponse> {
    console.log(`[Search Agent] Processing ${searchResults.length} search results for query: "${userQuery}"`);
    console.log(`[Search Agent] Using provider: ${config.provider}, model: ${config.model}, temperature: ${config.temperature}, maxTokens: ${config.maxTokens || 4000}`);

    if (!searchResults || searchResults.length === 0) {
        return {
            synthesizedContent: "No search results were found for your query.",
            sources: [],
            success: false,
            error: "No search results available"
        };
    }

    try {
        // Get API key for the search agent
        let apiKey = config.apiKey || await _getSearchAgentApiKey(userId, config.provider);

        // If no API key is found, try to use the environment variable as a fallback
        if (!apiKey) {
            console.warn(`[Search Agent] No user API key available for provider: ${config.provider}, trying environment variable`);

            // Map provider to environment variable
            const providerToEnvMap: { [key: string]: string | undefined } = {
                'openai': process.env.OPENAI_API_KEY,
                'anthropic': process.env.ANTHROPIC_API_KEY,
                'google': process.env.GOOGLE_API_KEY,
                'azure': process.env.AZURE_OPENAI_API_KEY,
                'groq': process.env.GROQ_API_KEY,
                'mistral': process.env.MISTRAL_API_KEY,
                'ollama': process.env.OLLAMA_API_KEY
            };

            apiKey = providerToEnvMap[config.provider.toLowerCase()];
        }

        if (!apiKey) {
            console.error(`[Search Agent] No API key available for provider: ${config.provider} (checked user keys and environment variables)`);
            return {
                synthesizedContent: "Unable to process search results due to missing API key. Please add an API key for this provider in your settings.",
                sources: [],
                success: false,
                error: "Missing API key"
            };
        }

        // Prepare search results content with cleaning
        const searchContent = searchResults.slice(0, 5).map((result, index) => {
            const cleanContent = cleanSearchContent(result.content || result.snippet || 'No content available.');
            return `Source ${index + 1}: ${result.title || 'Untitled'}\nURL: ${result.url}\nContent:\n${cleanContent}`;
        }).join('\n\n---\n\n');

        // Create system prompt for the search agent with current date
        const currentDate = new Date();
        const formattedDate = currentDate.toLocaleDateString('en-US', {
            weekday: 'long',
            year: 'numeric',
            month: 'long',
            day: 'numeric'
        });

        // Use custom system prompt from config if provided, otherwise use default
        let systemPrompt = config.systemPrompt || '';

        // If no custom prompt is provided or it's empty, use the default prompt
        if (!systemPrompt) {
            systemPrompt = `You are a specialized Search Synthesis Agent. Your purpose is to analyze web search results and create a comprehensive, accurate, and contextually relevant summary based *only* on the provided information.

TODAY'S DATE IS: ${formattedDate}. Use this as your reference point for recency when evaluating search results.

**Your Core Task:**

1. **Analyze:** Carefully examine all provided search result snippets and content.
2. **Extract & Synthesize:** Identify and extract the most relevant and accurate information related to the user's query. Synthesize this into a well-structured response that includes **key information and important contextual details** necessary for a full understanding.
3. **Connect (If Applicable & Supported):** If multiple reliable sources clearly point to an objective trend, consensus, or connection directly relevant to the query, you may highlight this, ensuring it is strongly supported by and grounded in the provided sources.
4. **Cite:** Accurately cite the source URLs for the information presented.
5. **Prioritize Recency:** Give priority to information that is most current relative to TODAY'S DATE, especially for time-sensitive queries like "news".
6. **Clean Content:** The search results may contain HTML, CSS, or other formatting artifacts. Ignore these technical elements and focus only on the actual content. If you see CSS variables, HTML tags, or other code elements, do not include them in your response.

**Important Guidelines:**

* **Strictly Grounded:** Your entire response **must** be based *solely* on the information contained within the provided search results. Do **not** introduce external knowledge, personal opinions, or interpretations not directly and clearly supported by the sources.
* **Factual Accuracy:** Ensure all presented information is factually accurate according to the sources.
* **Handle Limitations:** If the search results lack sufficient relevant information or are outdated, clearly state this limitation (e.g., "The search results did not contain recent information on X," or "The available information on Y appears to be from [Date/Source]").
* **Date Reporting:** You may report future dates *if they are explicitly mentioned as factual information within the search results* (e.g., 'an election is scheduled for [Date]'). Do **not** speculate about the future or misrepresent the current date (TODAY'S DATE IS: ${formattedDate}).
* **Clarity and Structure:** Organize the synthesized information logically using paragraphs, bullet points, or lists as appropriate for readability.
* **Neutral Tone:** Maintain an objective and neutral tone.
* **No Self-Reference:** Do **not** mention that you are a search agent or refer to the process of analyzing search results. Act as if you are presenting the synthesized information directly.
* **Downstream Use:** Your synthesis will likely be used by another AI agent for further analysis or response generation. Aim for a balance of comprehensiveness (including relevant context) and conciseness.

**Output Format:**

    [Raw data received from search engine]
[Your synthesized answer here, well-structured and incorporating relevant context from the sources.]

Sources:
- [URL1]
- [URL2]
...`;
        } else {
            // Replace date placeholders in custom prompt if they exist
            systemPrompt = systemPrompt.replace(/\$\{formattedDate\}/g, formattedDate);
        }

        // Create user message with query and search results
        const userMessage = `User Query: ${userQuery}\n\nSearch Results:\n${searchContent}`;

        // Prepare LLM input
        const llmInput: LLMServiceInput = {
            provider: config.provider,
            model: config.model,
            temperature: config.temperature,
            maxTokens: config.maxTokens || 4000, // Increased from 1500 to 4000 for better news summaries
            messages: [{ role: 'user', content: userMessage }],
            systemPrompt: systemPrompt,
            apiKey: apiKey,
            instructions: '' // Required by LLMServiceInput interface
            // Note: stream is handled automatically by callLLMApi
        };

        console.log(`[Search Agent] Using LLM configuration: provider=${config.provider}, model=${config.model}, temperature=${config.temperature}, maxTokens=${config.maxTokens || 4000}`);

        // Validate provider and model
        if (!config.provider) {
            console.error(`[Search Agent] Provider is missing or invalid`);
            return {
                synthesizedContent: "Unable to process search results due to invalid provider configuration.",
                sources: [],
                success: false,
                error: "Invalid provider configuration"
            };
        }

        if (!config.model) {
            console.error(`[Search Agent] Model is missing or invalid for provider: ${config.provider}`);
            return {
                synthesizedContent: "Unable to process search results due to invalid model configuration.",
                sources: [],
                success: false,
                error: "Invalid model configuration"
            };
        }

        // Call LLM API
        const response = await callLLMApi(llmInput);
        let responseContent = '';

        // Process the response
        for await (const chunk of response) {
            if (typeof chunk === 'string') {
                responseContent += chunk;
            } else if (chunk.content) {
                responseContent += chunk.content;
            }
        }

        // Extract sources from the response
        const sources: string[] = [];
        const sourcesMatch = responseContent.match(/Sources:\s*\n((?:- .*\n?)+)/i);

        if (sourcesMatch && sourcesMatch[1]) {
            const sourcesList = sourcesMatch[1].split('\n');
            for (const sourceLine of sourcesList) {
                const trimmedLine = sourceLine.trim();
                if (trimmedLine.startsWith('- ')) {
                    const source = trimmedLine.substring(2).trim();
                    if (source) sources.push(source);
                }
            }
        }

        // Clean up the response by removing the sources section
        let cleanedResponse = responseContent;
        if (sourcesMatch) {
            cleanedResponse = responseContent.replace(/Sources:\s*\n((?:- .*\n?)+)/i, '').trim();
        }

        return {
            synthesizedContent: cleanedResponse,
            sources: sources,
            success: true
        };

    } catch (error: any) {
        console.error(`[Search Agent] Error processing search results:`, error);
        return {
            synthesizedContent: "An error occurred while processing search results.",
            sources: [],
            success: false,
            error: error.message || "Unknown error"
        };
    }
}




