import { Request, Response } from 'express';
import Conversation from '../models/Conversation';

// @desc    Get list of conversations (ID, title, timestamps) for the logged-in user
// @route   GET /api/conversations
// @access  Private
export const listConversations = async (req: Request, res: Response): Promise<void> => {
    try {
        // Use type assertion to access the user ID
        const userId = req.user?._id || (req.user as any)?.id;
        if (!userId) {
            res.status(401).json({ message: 'Not authorized' });
            return;
        }
        console.log(`[ListConversations] Fetching conversations for userId: ${userId}`); // ADD LOG

        const conversations = await Conversation.find({ userId: userId }) // Filter by userId
            .sort({ updatedAt: -1 })
            .select('_id title createdAt updatedAt');

        console.log(`[ListConversations] Found ${conversations.length} conversations for userId: ${userId}`); // ADD LOG

        res.status(200).json(conversations);
    } catch (error: any) {
        console.error(`[ListConversations] Error listing conversations for userId: ${req.user?._id || (req.user as any)?.id}:`, error); // Log userId on error too
        res.status(500).json({ message: 'Server error listing conversations.', error: error.message });
    }
};

// @desc    Get a specific conversation by ID for the logged-in user
// @route   GET /api/conversations/:id
// @access  Private
export const getConversationById = async (req: Request, res: Response): Promise<void> => {
    try {
        const conversationId = req.params.id;
        // Use type assertion to access the user ID
        const userId = req.user?._id || (req.user as any)?.id;
        if (!userId) {
            res.status(401).json({ message: 'Not authorized' });
            return;
        }

        // Find by ID and ensure it belongs to the user
        const conversation = await Conversation.findOne({ _id: conversationId, userId: userId });

        if (!conversation) {
            res.status(404).json({ message: 'Conversation not found' });
            return;
        }

        res.status(200).json(conversation);
    } catch (error: any) {
        console.error(`Error fetching conversation ${req.params.id}:`, error);
        res.status(500).json({ message: 'Server error fetching conversation.', error: error.message });
    }
};

// @desc    Delete a specific conversation by ID for the logged-in user
// @route   DELETE /api/conversations/:id
// @access  Private
export const deleteConversation = async (req: Request, res: Response): Promise<void> => {
     const conversationId = req.params.id;
     // Use type assertion to access the user ID
     const userId = req.user?._id || (req.user as any)?.id;

     if (!userId) {
         res.status(401).json({ message: 'Not authorized' });
         return;
     }
     if (!conversationId) {
         res.status(400).json({ message: 'Conversation ID parameter is required.' });
         return;
     }

    try {
        // Delete only if the conversation belongs to the user
        const result = await Conversation.deleteOne({ _id: conversationId, userId: userId });

        if (result.deletedCount === 0) {
            res.status(404).json({ message: 'Conversation not found' });
            return;
        }

        res.status(200).json({ message: 'Conversation deleted successfully' });

    } catch (error: any) {
        console.error(`Error deleting conversation ${conversationId}:`, error);
        res.status(500).json({ message: 'Server error deleting conversation.', error: error.message });
    }
};

// Note: Saving/Updating conversations will likely happen via the socket connection
// during or after the agent orchestration process, not via a standard POST/PUT API route.

// @desc    Delete ALL conversations for the logged-in user
// @route   DELETE /api/conversations/all
// @access  Private
export const deleteAllConversations = async (req: Request, res: Response): Promise<void> => {
    // Use type assertion to access the user ID
    const userId = req.user?._id || (req.user as any)?.id;

    if (!userId) {
        res.status(401).json({ message: 'Not authorized' });
        return;
    }

    try {
        console.log(`[DeleteAll] Attempting to delete all conversations for userId: ${userId}`); // ADD LOG
        const result = await Conversation.deleteMany({ userId: userId }); // Delete all matching the userId

        console.log(`[DeleteAll] Deleted ${result.deletedCount} conversations for userId: ${userId}`); // ADD LOG

        res.status(200).json({ message: `Successfully deleted ${result.deletedCount} conversations.` });

    } catch (error: any) {
        console.error(`[DeleteAll] Error deleting all conversations for userId: ${userId}:`, error); // ADD LOG
        res.status(500).json({ message: 'Server error deleting all conversations.', error: error.message });
    }
};
