// logger.ts - Simple logger implementation for the application

/**
 * Simple logger implementation with different log levels.
 * In a production environment, this could be replaced with a more robust solution like Winston or Pino.
 */
const logger = {
    /**
     * Log debug level messages (verbose information for debugging)
     * @param message The message to log
     */
    debug: (message: string) => console.debug(`[DEBUG] ${message}`),
    
    /**
     * Log informational messages (general information about application flow)
     * @param message The message to log
     */
    info: (message: string) => console.info(`[INFO] ${message}`),
    
    /**
     * Log warning messages (potential issues that don't prevent the application from working)
     * @param message The message to log
     */
    warn: (message: string) => console.warn(`[WARN] ${message}`),
    
    /**
     * Log error messages (issues that prevent a function from working properly)
     * @param message The message to log
     */
    error: (message: string) => console.error(`[ERROR] ${message}`),
    
    /**
     * Log trace level messages (very detailed information, typically only enabled during detailed debugging)
     * @param message The message to log
     */
    trace: (message: string) => console.trace(`[TRACE] ${message}`),
};

export default logger;
