# Test Environment Configuration
NODE_ENV=test
PORT=3001

# Test Database (will be overridden by in-memory MongoDB)
MONGODB_URI=mongodb://localhost:27017/maia-web-test

# Test JWT Configuration
JWT_SECRET=test-jwt-secret-key-for-testing-only-do-not-use-in-production
JWT_EXPIRATION=1h

# Test Encryption Secret (32 characters)
ENCRYPTION_SECRET=test-encryption-secret-32-chars-long

# Test Frontend URL
FRONTEND_URL=http://localhost:3000

# Mock API Keys for Testing (these won't be used in actual API calls)
OPENAI_API_KEY=test-openai-key
ANTHROPIC_API_KEY=test-anthropic-key
GOOGLE_API_KEY=test-google-key
GROQ_API_KEY=test-groq-key
SERPER_API_KEY=test-serper-key

# Test Google OAuth (mock values)
GOOGLE_CLIENT_ID=test-google-client-id
GOOGLE_CLIENT_SECRET=test-google-client-secret

# Test Local Model URLs
OLLAMA_BASE_URL=http://localhost:11434
LMSTUDIO_BASE_URL=http://localhost:1234
