// Message types
export interface Message {
  role: 'user' | 'assistant' | 'system' | 'tool';
  content: string;
  agentName?: string;
  timestamp: string;
  localImagePath?: string;
  imageUrl?: string;
  imageDataUrl?: string;
}

export interface GeneratedImageMessage {
  type: 'image_generated';
  key: string;
  localImagePath: string;
  imageUrl?: string;
  imageDataUrl?: string;
  error?: string;
}

// Conversation data from backend
export interface FullConversationData {
  _id: string;
  title: string;
  messages: Array<Message>;
  metadata?: Record<string, any>;
  createdAt: string;
  updatedAt: string;
}

// Discussion panel message types
export type DiscussionMessage = string | GeneratedImageMessage;
export type DiscussionRecord = Record<string, DiscussionMessage>;
