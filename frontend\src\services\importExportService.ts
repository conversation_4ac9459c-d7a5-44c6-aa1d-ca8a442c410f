import apiClient from './authApiService';

// Interface for example profile list item
export interface ExampleProfileListItem {
  name: string;
  file: string;
  agentCount: number;
  description: string;
}

// Interface for example profiles index
export interface ExampleProfilesIndex {
  profiles: ExampleProfileListItem[];
}



/**
 * Exports a profile to a JSON object
 * @param id The ID of the profile to export
 * @returns The profile data
 */
export const exportProfile = async (id: string): Promise<any> => {
  try {
    const response = await apiClient.get(`/api/import-export/export/${id}`);
    return response.data;
  } catch (error) {
    console.error('Error exporting profile:', error);
    throw error;
  }
};

/**
 * Gets the list of example profiles
 * @returns The list of example profiles
 */
export const getExampleProfiles = async (): Promise<ExampleProfilesIndex> => {
  try {
    const response = await apiClient.get('/api/import-export/examples');
    return response.data;
  } catch (error) {
    console.error('Error getting example profiles:', error);
    throw error;
  }
};

/**
 * Gets a specific example profile
 * @param filename The filename of the example profile
 * @returns The example profile data
 */
export const getExampleProfile = async (filename: string): Promise<any> => {
  try {
    const response = await apiClient.get(`/api/import-export/examples/${filename}`);
    return response.data;
  } catch (error) {
    console.error('Error getting example profile:', error);
    throw error;
  }
};
