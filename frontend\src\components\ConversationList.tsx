import React, { useState, useEffect, useCallback } from 'react';
import apiClient from '../services/authApiService';
import { useConfigStore } from '../store/configStore';
import Modal from './Modal'; // <<< Import Modal component

// Interface for conversation list item
interface ConversationListItem {
    _id: string;
    title: string;
    updatedAt: string;
    createdAt: string;
}

// Props for the component
interface ConversationListProps {
    onLoadConversation: (conversationId: string) => void;
}

const ConversationList: React.FC<ConversationListProps> = ({ onLoadConversation }) => {
    const [conversations, setConversations] = useState<ConversationListItem[]>([]);
    const [isLoading, setIsLoading] = useState(false);
    const [error, setError] = useState<string | null>(null);
    // Modal states
    const [isSingleDeleteModalOpen, setIsSingleDeleteModalOpen] = useState(false);
    const [isDeleteAllModalOpen, setIsDeleteAllModalOpen] = useState(false);
    const [conversationToDelete, setConversationToDelete] = useState<{ id: string; title: string } | null>(null);

    // Get authentication state from the store
    const isAuthenticated = useConfigStore((state) => state.isAuthenticated);
    const logout = useConfigStore((state) => state.logout);

    // Log the perceived authentication state when the component renders
    console.log('[ConversationList Render] isAuthenticated:', isAuthenticated);

    // Wrap fetchConversations in useCallback to stabilize its identity for useEffect dependency
    const fetchConversations = useCallback(async () => {
        // Don't attempt to fetch if not authenticated
        if (!isAuthenticated) {
            setConversations([]); // Clear list if logged out
            setError(null);
            return;
        }

        setIsLoading(true);
        setError(null);
        console.log("Fetching conversations (authenticated)...");

        try {
            // Use the apiClient instance which includes the token interceptor
            const response = await apiClient.get<ConversationListItem[]>('/api/conversations'); // Use apiClient
            console.log("[ConversationList] Received API response data:", response.data); // ADD LOG
            setConversations(Array.isArray(response.data) ? response.data : []); // Ensure it's an array
        } catch (err: any) {
            console.error("[ConversationList] Error fetching conversations:", err); // Add component context to log
            if (err.response?.status === 401) {
                 setError("Session expired or invalid. Please log in again.");
                 setConversations([]);
                 // Optionally trigger logout after a short delay
                 // setTimeout(() => logout(), 1500);
            } else if (err.response?.status === 404) {
                 console.log("No conversations found for user.");
                 setConversations([]);
                 setError(null);
            } else {
                 setError(err.response?.data?.message || err.message || "Failed to fetch conversations.");
            }
        } finally {
            setIsLoading(false);
        }
    }, [isAuthenticated, logout]);

    // --- Single Delete ---
    const openSingleDeleteModal = (id: string, title: string) => {
        setConversationToDelete({ id, title });
        setIsSingleDeleteModalOpen(true);
        setError(null); // Clear previous errors
    };

    const confirmSingleDelete = async () => {
        if (!conversationToDelete) return;
        const { id, title } = conversationToDelete;

        // Check auth status before delete attempt
        if (!isAuthenticated) {
            setError("Not authorized, please log in.");
            setIsSingleDeleteModalOpen(false); // Close modal on auth error
            setConversationToDelete(null);
            return;
        }

        setIsLoading(true); // Use general loading state for now
        setError(null);
        setIsSingleDeleteModalOpen(false); // Close modal before API call
         try {
             // Use the apiClient instance
             await apiClient.delete(`/api/conversations/${id}`); // Use apiClient
             // Refresh list after delete
             fetchConversations();
             setConversationToDelete(null); // Clear selection
         } catch (err: any) {
             console.error(`Error deleting conversation ${id}:`, err);
             const message = err.response?.data?.message || err.message || `Failed to delete conversation "${title}".`;
             setError(message);
             if (err.response?.status === 401) {
                 // Handle potential logout or session expiry message
                 setError("Session expired or invalid. Please log in again.");
             }
             setConversationToDelete(null); // Clear selection even on error
         } finally {
             setIsLoading(false);
         }
    };

    // --- Delete All ---
    const openDeleteAllModal = () => {
        setIsDeleteAllModalOpen(true);
        setError(null); // Clear previous errors
    };

    const confirmDeleteAll = async () => {
        if (!isAuthenticated) {
            setError("Not authorized, please log in.");
            setIsDeleteAllModalOpen(false); // Close modal on auth error
            return;
        }

        setIsLoading(true);
        setError(null);
        setIsDeleteAllModalOpen(false); // Close modal before API call
        try {
            // Call the new backend endpoint
            await apiClient.delete('/api/conversations/all');
             // Refresh the list
             fetchConversations();
             console.log("All conversations deleted successfully.");
             // Optionally show success message via global store or local state
         } catch (err: any) {
             console.error('Error deleting all conversations:', err);
             const message = err.response?.data?.message || err.message || "Failed to delete all conversations.";
             setError(message);
             if (err.response?.status === 401) {
                 setError("Session expired or invalid. Please log in again.");
             }
         } finally {
             setIsLoading(false);
         }
    };

    // Fetch conversations when the component mounts or when authentication status changes
    useEffect(() => {
        if (isAuthenticated) {
            fetchConversations();
        } else {
            // Clear list and errors if user logs out or was never logged in
            setConversations([]);
            setError(null);
        }
    }, [isAuthenticated, fetchConversations]); // Depend on isAuthenticated and the stable fetchConversations

    return (
        <div className="p-4 border-r border-gray-200 dark:border-gray-700 h-full flex flex-col bg-surface-light dark:bg-surface-dark">
            <h3 className="text-lg font-semibold mb-4 text-gray-800 dark:text-gray-200">Conversations</h3>
            <button
                id="refresh-conversations-button"
                onClick={fetchConversations}
                disabled={isLoading || !isAuthenticated} // Disable if loading or not authenticated
                className="mb-4 px-3 py-1 text-xs bg-gray-200 dark:bg-gray-600 text-gray-700 dark:text-gray-200 rounded hover:bg-gray-300 dark:hover:bg-gray-500 transition-colors disabled:opacity-50"
            >
                {isLoading ? 'Refreshing...' : 'Refresh List'}
            </button>
            {/* Delete All Button */}
            <button
                onClick={openDeleteAllModal} // Open modal instead of direct action
                disabled={isLoading || !isAuthenticated || conversations.length === 0}
                className="mb-4 ml-2 px-3 py-1 text-xs bg-red-500 dark:bg-red-700 text-white rounded hover:bg-red-600 dark:hover:bg-red-600 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                title="Delete all conversations"
            >
                Clear All
            </button>

            {error && <p className="text-xs text-red-600 dark:text-red-400 mb-2">{error}</p>}

            <div className="flex-grow overflow-y-auto space-y-2 pr-2">
                {!isAuthenticated && !isLoading && (
                     <p className="text-sm text-gray-500 dark:text-gray-400 italic">Please log in to view conversations.</p>
                )}
                {isAuthenticated && conversations.length === 0 && !isLoading && !error && (
                    <p className="text-sm text-gray-500 dark:text-gray-400 italic">No saved conversations found.</p>
                )}
                {isAuthenticated && conversations.map((conv) => (
                    <div key={conv._id} className="p-2 rounded hover:bg-gray-100 dark:hover:bg-gray-700 group">
                        <div className="flex justify-between items-center">
                             <button
                                onClick={() => onLoadConversation(conv._id)}
                                className="text-sm text-left truncate text-gray-800 dark:text-gray-200 hover:underline flex-grow mr-2"
                                title={conv.title}
                            >
                                {conv.title}
                             </button>
                             <button
                                onClick={() => openSingleDeleteModal(conv._id, conv.title)} // Open modal
                                disabled={isLoading}
                                className="px-1 py-0.5 text-xs text-red-600 dark:text-red-400 opacity-0 group-hover:opacity-100 transition-opacity hover:bg-red-100 dark:hover:bg-red-900/50 rounded disabled:opacity-20"
                                title="Delete Conversation"
                            >
                                X
                            </button>
                        </div>
                        <p className="text-xs text-gray-400 dark:text-gray-500 mt-1">
                            Updated: {new Date(conv.updatedAt).toLocaleString()}
                        </p>
                    </div>
                ))}
            </div>

            {/* Single Delete Confirmation Modal */}
            <Modal
                isOpen={isSingleDeleteModalOpen}
                onClose={() => { setIsSingleDeleteModalOpen(false); setConversationToDelete(null); }}
                title="Confirm Delete Conversation"
            >
                <div className="p-4">
                    <p className="text-sm text-gray-600 dark:text-gray-300 mb-4">
                        Are you sure you want to permanently delete the conversation titled "<strong className="font-medium">{conversationToDelete?.title}</strong>"? This action cannot be undone.
                    </p>
                    <div className="flex justify-end space-x-3">
                        <button
                            onClick={() => { setIsSingleDeleteModalOpen(false); setConversationToDelete(null); }}
                            className="px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-gray-200 dark:bg-gray-600 rounded-md hover:bg-gray-300 dark:hover:bg-gray-500 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 dark:focus:ring-offset-gray-800"
                        >
                            Cancel
                        </button>
                        <button
                            onClick={confirmSingleDelete}
                            disabled={isLoading}
                            className="px-4 py-2 text-sm font-medium text-white bg-red-600 rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 dark:focus:ring-offset-gray-800 disabled:opacity-50"
                        >
                            {isLoading ? 'Deleting...' : 'Delete'}
                        </button>
                    </div>
                </div>
            </Modal>

            {/* Delete All Confirmation Modal */}
            <Modal
                isOpen={isDeleteAllModalOpen}
                onClose={() => setIsDeleteAllModalOpen(false)}
                title="Confirm Delete All Conversations"
            >
                <div className="p-4">
                    <p className="text-sm text-gray-600 dark:text-gray-300 mb-4">
                        Are you sure you want to permanently delete <strong className="font-medium">ALL</strong> conversations? This action cannot be undone.
                    </p>
                    <div className="flex justify-end space-x-3">
                        <button
                            onClick={() => setIsDeleteAllModalOpen(false)}
                            className="px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-gray-200 dark:bg-gray-600 rounded-md hover:bg-gray-300 dark:hover:bg-gray-500 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 dark:focus:ring-offset-gray-800"
                        >
                            Cancel
                        </button>
                        <button
                            onClick={confirmDeleteAll}
                            disabled={isLoading}
                            className="px-4 py-2 text-sm font-medium text-white bg-red-600 rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 dark:focus:ring-offset-gray-800 disabled:opacity-50"
                        >
                            {isLoading ? 'Deleting...' : 'Delete All'}
                        </button>
                    </div>
                </div>
            </Modal>
        </div>
    );
};

export default ConversationList;
