import React from 'react';

interface ImageStyleSelectorProps {
  onQualitySelect: (quality: string) => void;
  onStyleSelect: (style: string) => void;
  selectedQuality: string;
  selectedStyle: string;
}

const ImageStyleSelector: React.FC<ImageStyleSelectorProps> = ({
  onQualitySelect,
  onStyleSelect,
  selectedQuality,
  selectedStyle,
}) => {
  const qualities = [
    { value: 'standard', label: 'Standard' },
    { value: 'hd', label: 'HD' },
  ];

  const styles = [
    { value: 'vivid', label: 'Vivid' },
    { value: 'natural', label: 'Natural' },
  ];

  return (
    <div className="flex flex-col space-y-4">
      <div>
        <label className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 block">
          Image Quality
        </label>
        <div className="flex space-x-2">
          {qualities.map((quality) => (
            <button
              key={quality.value}
              onClick={() => onQualitySelect(quality.value)}
              className={`px-4 py-2 text-sm rounded-md flex-1 transition-colors ${
                selectedQuality === quality.value
                  ? 'bg-blue-600 text-white'
                  : 'bg-gray-200 dark:bg-gray-700 text-gray-800 dark:text-gray-200 hover:bg-gray-300 dark:hover:bg-gray-600'
              }`}
            >
              {quality.label}
            </button>
          ))}
        </div>
      </div>

      <div>
        <label className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 block">
          Image Style
        </label>
        <div className="flex space-x-2">
          {styles.map((style) => (
            <button
              key={style.value}
              onClick={() => onStyleSelect(style.value)}
              className={`px-4 py-2 text-sm rounded-md flex-1 transition-colors ${
                selectedStyle === style.value
                  ? 'bg-blue-600 text-white'
                  : 'bg-gray-200 dark:bg-gray-700 text-gray-800 dark:text-gray-200 hover:bg-gray-300 dark:hover:bg-gray-600'
              }`}
            >
              {style.label}
            </button>
          ))}
        </div>
      </div>
    </div>
  );
};

export default ImageStyleSelector;
