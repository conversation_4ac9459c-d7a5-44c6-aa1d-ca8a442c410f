import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { vi } from 'vitest'
import PromptInputArea from '../../components/PromptInputArea'

// Mock the socket service
vi.mock('../../services/socketService', () => ({
  getSocket: vi.fn(() => ({
    connected: true,
    emit: vi.fn()
  }))
}))

// Mock the config store
const mockConfigStore = {
  agentCount: 2,
  generalInstructions: '',
  agentConfigurations: [
    { provider: 'openai', model: 'gpt-4' },
    { provider: 'anthropic', model: 'claude-3' }
  ],
  internetSettings: { enabled: false },
  searchAgentSettings: {},
  ragSettings: { enabled: false },
  maxAgentRuns: 5,
  baseInstructions: '',
  useBaseInstructions: false,
  isProcessing: false,
  isCancelling: false,
  setIsProcessing: vi.fn(),
  setIsCancelling: vi.fn(),
  clearGlobalMessages: vi.fn(),
  setGlobalError: vi.fn()
}

vi.mock('../../store/configStore', () => ({
  useConfigStore: vi.fn(() => mockConfigStore)
}))

describe('PromptInputArea', () => {
  const mockProps = {
    toggleConsole: vi.fn(),
    clearChat: vi.fn(),
    currentConversationId: 'test-conversation-id',
    setAgentDiscussion: vi.fn(),
    setDiscussionOrder: vi.fn()
  }

  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('renders correctly', () => {
    render(<PromptInputArea {...mockProps} />)
    
    expect(screen.getByPlaceholderText(/Enter your prompt here/)).toBeInTheDocument()
    expect(screen.getByRole('button', { name: /Send/ })).toBeInTheDocument()
    expect(screen.getByRole('button', { name: /Image Mode/ })).toBeInTheDocument()
    expect(screen.getByRole('button', { name: /Clear/ })).toBeInTheDocument()
    expect(screen.getByRole('button', { name: /Toggle Console/ })).toBeInTheDocument()
  })

  it('allows typing in the textarea', async () => {
    const user = userEvent.setup()
    render(<PromptInputArea {...mockProps} />)
    
    const textarea = screen.getByPlaceholderText(/Enter your prompt here/)
    await user.type(textarea, 'Test prompt')
    
    expect(textarea).toHaveValue('Test prompt')
  })

  it('disables send button when prompt is empty', () => {
    render(<PromptInputArea {...mockProps} />)
    
    const sendButton = screen.getByRole('button', { name: /Send/ })
    expect(sendButton).toBeDisabled()
  })

  it('enables send button when prompt has content', async () => {
    const user = userEvent.setup()
    render(<PromptInputArea {...mockProps} />)
    
    const textarea = screen.getByPlaceholderText(/Enter your prompt here/)
    const sendButton = screen.getByRole('button', { name: /Send/ })
    
    await user.type(textarea, 'Test prompt')
    expect(sendButton).not.toBeDisabled()
  })

  it('calls clearChat when clear button is clicked', async () => {
    const user = userEvent.setup()
    render(<PromptInputArea {...mockProps} />)
    
    const clearButton = screen.getByRole('button', { name: /Clear/ })
    await user.click(clearButton)
    
    expect(mockProps.clearChat).toHaveBeenCalledTimes(1)
  })

  it('calls toggleConsole when toggle console button is clicked', async () => {
    const user = userEvent.setup()
    render(<PromptInputArea {...mockProps} />)
    
    const toggleButton = screen.getByRole('button', { name: /Toggle Console/ })
    await user.click(toggleButton)
    
    expect(mockProps.toggleConsole).toHaveBeenCalledTimes(1)
  })

  it('toggles image generation mode', async () => {
    const user = userEvent.setup()
    render(<PromptInputArea {...mockProps} />)

    const imageModeButton = screen.getByRole('button', { name: /Image Mode/ })
    await user.click(imageModeButton)

    // Should change to "Generate Image" text
    await waitFor(() => {
      expect(screen.getByRole('button', { name: /Generate Image/ })).toBeInTheDocument()
    }, { timeout: 3000 })

    // Placeholder should change
    expect(screen.getByPlaceholderText(/Describe the image you want to generate/)).toBeInTheDocument()
  })

  it('handles Ctrl+Enter keyboard shortcut', async () => {
    const user = userEvent.setup()
    render(<PromptInputArea {...mockProps} />)
    
    const textarea = screen.getByPlaceholderText(/Enter your prompt here/)
    await user.type(textarea, 'Test prompt')
    
    // Simulate Ctrl+Enter
    await user.keyboard('{Control>}{Enter}{/Control}')
    
    // Should clear the textarea after sending
    await waitFor(() => {
      expect(textarea).toHaveValue('')
    })
  })

  it('handles drag and drop events', () => {
    render(<PromptInputArea {...mockProps} />)

    // Find the main container that handles drag and drop
    const container = screen.getByPlaceholderText(/Enter your prompt here/).closest('.relative')

    // Simulate drag enter
    fireEvent.dragEnter(container!)
    expect(container).toHaveClass('border-dashed')

    // Simulate drag leave
    fireEvent.dragLeave(container!)
    expect(container).not.toHaveClass('border-dashed')
  })

  it('clears prompt after sending', async () => {
    const user = userEvent.setup()
    render(<PromptInputArea {...mockProps} />)
    
    const textarea = screen.getByPlaceholderText(/Enter your prompt here/)
    const sendButton = screen.getByRole('button', { name: /Send/ })
    
    await user.type(textarea, 'Test prompt')
    await user.click(sendButton)
    
    await waitFor(() => {
      expect(textarea).toHaveValue('')
    })
  })
})
