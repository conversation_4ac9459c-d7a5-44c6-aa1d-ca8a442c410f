import express from 'express';
import { protect } from '../middleware/authMiddleware'; // Import authentication middleware
import {
  getApi<PERSON><PERSON>s,
  addApi<PERSON><PERSON>,
  update<PERSON>pi<PERSON><PERSON>,
  delete<PERSON>pi<PERSON><PERSON>,
} from '../controllers/apiKeyController'; // Import controller functions

const router = express.Router();

// Apply authentication middleware to all routes in this file
router.use(protect);

// Define routes for API Key management
router.route('/')
  .get(getApiKeys)    // GET /api/apikeys - Fetches all keys for the logged-in user
  .post(addApiKey);   // POST /api/apikeys - Adds a new key for the logged-in user

router.route('/:id')
  .put(updateApiKey)   // PUT /api/apikeys/:id - Updates a specific key by ID
  .delete(deleteApiKey); // DELETE /api/apikeys/:id - Deletes a specific key by ID

export default router;
