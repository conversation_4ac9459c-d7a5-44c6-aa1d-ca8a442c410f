// conversationManager.ts - Adapts logic from conversation_manager.py

import { Socket } from 'socket.io'; // For potential future use (e.g., emitting history updates)

// Define the structure for a message in the conversation history
// This should match the structure defined in the Conversation model
export interface ConversationMessage {
    role: 'user' | 'assistant' | 'system' | 'tool';
    content: string;
    agentName?: string;
    timestamp: string; // Make timestamp required
    localImagePath?: string; // Optional path for saved image
    imageUrl?: string; // Optional original URL
    imageDataUrl?: string; // Optional original data URL
    hasImage?: boolean; // Flag to indicate if message has an image
}

// Rough token estimation factor (words * factor)
const TOKEN_ESTIMATION_FACTOR = 1.3;
// Default context size if not specified (adjust as needed)
const DEFAULT_WORKING_CONTEXT_SIZE = 16384;

export class ConversationManager {
    private conversationHistory: ConversationMessage[];
    private maxContextWindow: number; // Max allowed by models (e.g., 20000)
    private workingContextSize: number; // Target size for context window (e.g., 16384)

    constructor(maxContextWindow: number = 20000, workingContextSize: number = DEFAULT_WORKING_CONTEXT_SIZE) {
        this.conversationHistory = [];
        this.maxContextWindow = maxContextWindow;
        this.workingContextSize = workingContextSize;
    }

    /**
     * Starts a new conversation history with the initial prompt.
     * @param initialPrompt The user's first message.
     * @param imageData Optional base64 data URL of an accompanying image.
     */
    startNewConversation(initialPrompt: string, imageData?: string | null): void {
        this.conversationHistory = []; // Clear previous history
        // TODO: Decide how to incorporate imageData into the first message if needed
        this.addMessage(initialPrompt, 'user', undefined, imageData);
    }

    /**
     * Loads an existing conversation history.
     * @param history An array of ConversationMessage objects.
     */
    loadHistory(history: ConversationMessage[]): void {
        // Ensure all loaded messages have a timestamp (defensive)
        this.conversationHistory = history.map(msg => ({
            ...msg,
            timestamp: msg.timestamp || new Date(0).toISOString() // Provide default if missing
        }));
        console.log(`ConversationManager: Loaded ${this.conversationHistory.length} messages from history.`);
    }

    /**
     * Adds a message to the current conversation history.
     * @param content The message content.
     * @param role The role of the message sender.
     * @param agentName Optional name of the agent if role is 'assistant'.
     * @param imageData Optional base64 data URL of an accompanying image (usually for user role).
     * @param localImagePath Optional path to the locally saved image.
     * @param imageUrl Optional original URL of the image.
     * @param imageDataUrl Optional original data URL of the image (can be redundant if imageData is passed).
     */
    addMessage(
        content: string,
        role: ConversationMessage['role'],
        agentName?: string,
        imageData?: string | null, // Keep for potential direct image input handling
        localImagePath?: string | null,
        imageUrl?: string | null,
        imageDataUrl?: string | null
    ): void {
        // Handle image data logging
        if (imageData) {
            console.log(`[ConversationManager] Adding message with direct image data (length: ${imageData?.length || 0})`);
            // Decide if imageData itself needs storing or just the derived URLs/paths
        }
        if (localImagePath) {
             console.log(`[ConversationManager] Adding message with local image path: ${localImagePath}`);
        }

        // Ensure content is not empty for image messages
        let finalContent = content;
        if (!finalContent && (imageData || localImagePath || imageUrl || imageDataUrl)) {
            // If content is empty but we have an image, provide a default description
            finalContent = '[Image message]';
            console.log(`[ConversationManager] Empty content for image message, using default text: ${finalContent}`);
        } else if (!finalContent) {
            // If content is empty and no image, use a placeholder to avoid validation errors
            finalContent = ' '; // Single space to pass validation but appear empty
            console.log(`[ConversationManager] Empty content with no image, using space character`);
        }

        // Check if this message has any image data
        const hasImage = !!(imageData || localImagePath || imageUrl || imageDataUrl);

        const message: ConversationMessage = {
            role,
            content: finalContent,
            timestamp: new Date().toISOString(),
            hasImage, // Add the hasImage flag
            ...(agentName && { agentName }),
            ...(localImagePath && { localImagePath }), // Add if provided
            ...(imageUrl && { imageUrl }), // Add if provided
            ...(imageDataUrl && { imageDataUrl }), // Add if provided
            // Note: We are not storing the raw imageData in the history array itself by default
        };
        this.conversationHistory.push(message);
        // TODO: Implement history trimming/summarization if it exceeds maxContextWindow significantly
    }

    /**
     * Gets the conversation history formatted for LLM context, respecting token limits.
     * (Adapted from Python's get_context_window)
     *
     * @param maxTokens Optional maximum tokens for the context window (defaults to workingContextSize).
     * @returns An array of ConversationMessage suitable for LLM input.
     */
    getContextMessages(maxTokens?: number): ConversationMessage[] {
        if (!this.conversationHistory.length) {
            return [];
        }

        const targetMaxTokens = maxTokens || this.workingContextSize;
        const contextMessages: ConversationMessage[] = [];
        let tokenCount = 0;

        // Iterate backwards through history to prioritize recent messages
        for (let i = this.conversationHistory.length - 1; i >= 0; i--) {
            const message = this.conversationHistory[i];
            // Simple estimation: count words * factor
            // More accurate token counting (e.g., using tiktoken) could be added later
            const estimatedTokens = (message.content?.split(' ').length || 0) * TOKEN_ESTIMATION_FACTOR;

            // Ensure we don't exceed the token limit
            if (tokenCount + estimatedTokens > targetMaxTokens) {
                // If adding the first message (initial prompt) exceeds limit, we might need special handling
                // For now, we just stop adding messages.
                if (contextMessages.length === 0) {
                     console.warn(`Initial message alone might exceed token limit (${estimatedTokens} > ${targetMaxTokens}). Context will be empty or truncated.`);
                }
                break;
            }

            // Add message to the beginning of the context array
            // Make sure to include image information if present
            const messageWithImageInfo = { ...message };

            // If the message has an image path, ensure it's included in the context
            if (message.localImagePath) {
                // Add a note about the image in the content if not already present
                if (!messageWithImageInfo.content.includes('[This message includes an image:')) {
                    messageWithImageInfo.content += `\n\n[This message includes an image: ${message.localImagePath}]`;
                }
                // Add extra tokens for the image reference
                tokenCount += 20; // Rough estimate for the image reference
            }

            contextMessages.unshift(messageWithImageInfo);
            tokenCount += estimatedTokens;
        }

        console.log(`Prepared context window with ~${Math.round(tokenCount)} tokens for target <= ${targetMaxTokens}. Messages: ${contextMessages.length}/${this.conversationHistory.length}`);
        return contextMessages;
    }

    /**
     * Returns the full conversation history.
     * @returns The complete array of ConversationMessage.
     */
    getFullHistory(): ConversationMessage[] {
        return [...this.conversationHistory]; // Return a copy
    }

    // --- Methods Skipped from Python Version (for now) ---
    // - load_conversation / save_current_conversation (using files) -> Handled by DB persistence
    // - optimize_rag_content -> To be implemented with RAG feature
    // - get_conversation_list -> Handled by API endpoint
}
