import axios from 'axios';

// Import the configured Axios instance which includes the auth interceptor
import apiClient from './authApiService';

// Interface for the data returned by the GET /api/apikeys endpoint
export interface ApiKeyInfo {
  _id: string;
  serviceName: string;
  createdAt: string; // Dates are typically strings in JSON
  updatedAt: string;
}

// Interface for the data returned when adding/updating a key
export interface ApiKeyResponse extends ApiKeyInfo {
    message: string;
}

// Function to fetch API keys for the logged-in user
export const getApiKeys = async (): Promise<ApiKeyInfo[]> => {
  try {
    const response = await apiClient.get<ApiKeyInfo[]>('/api/apikeys');
    return response.data;
  } catch (error: any) {
    console.error('Error fetching API keys:', error.response?.data || error.message);
    // Re-throw the error or handle it as needed by the UI component
    throw error.response?.data || new Error('Failed to fetch API keys');
  }
};

// Function to add a new API key
export const addApiKey = async (serviceName: string, apiKey: string): Promise<ApiKeyResponse> => {
  try {
    const response = await apiClient.post<ApiKeyResponse>('/api/apikeys', { serviceName, apiKey });
    return response.data;
  } catch (error: any) {
    console.error('Error adding API key:', error.response?.data || error.message);
    throw error.response?.data || new Error('Failed to add API key');
  }
};

// Function to update an existing API key
// Note: We only send the new apiKey value in the body
export const updateApiKey = async (id: string, apiKey: string): Promise<ApiKeyResponse> => {
  try {
    const response = await apiClient.put<ApiKeyResponse>(`/api/apikeys/${id}`, { apiKey });
    return response.data;
  } catch (error: any) {
    console.error('Error updating API key:', error.response?.data || error.message);
    throw error.response?.data || new Error('Failed to update API key');
  }
};

// Function to delete an API key
export const deleteApiKey = async (id: string): Promise<{ message: string }> => {
  try {
    const response = await apiClient.delete<{ message: string }>(`/api/apikeys/${id}`);
    return response.data;
  } catch (error: any) {
    console.error('Error deleting API key:', error.response?.data || error.message);
    throw error.response?.data || new Error('Failed to delete API key');
  }
};
