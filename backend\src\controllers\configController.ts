import { Request, Response } from 'express';
import ConfigurationProfile from '../models/ConfigurationProfile';
import { IUser } from '../models/User';

// --- Define Interfaces for Settings (Mirroring Frontend Store) ---
// Note: These should ideally live in a shared types definition if used elsewhere in backend
interface AgentConfig {
  provider: string;
  model: string;
  instructions: string;
  useDefaultInstructions?: boolean;
  internetEnabled?: boolean;
  temperature?: number;
  maxTokens?: number;
  topP?: number;
  presencePenalty?: number;
  frequencyPenalty?: number;
}

interface InternetSettings {
    enabled: boolean;
    searchProvider: string;
    searchApiKey: string; // Note: Sensitive, consider not storing directly if possible
    googleCxId?: string;
    includedDomains: string[];
    excludedDomains: string[];
}

interface RagSettings {
    enabled: boolean;
    chunkingStrategy: string;
    chunkSize: number;
    chunkOverlap: number;
    embeddingModel: string;
    retrievalNResults: number;
    retrievalThreshold: number;
    useReranking: boolean;
    useQueryExpansion: boolean;
}

interface LoadableConfigurationSettings {
  agentCount: number;
  generalInstructions: string;
  agentConfigurations: AgentConfig[];
  internetSettings: InternetSettings;
  ragSettings: RagSettings;
  maxAgentRuns?: number;
  baseInstructions?: string;
  useBaseInstructions?: boolean;
  // Token limit settings
  maxContextWindow?: number;
  workingContextSize?: number;
  userManagedModels?: Record<string, {
    added: string[];
    removed: string[];
    lastSelected?: string;
  }>;
}
// --- End Interfaces ---


// Helper function to extract unique model names used by local providers
const extractLocalModelNames = (settings: Partial<LoadableConfigurationSettings>): string[] => {
    if (!settings?.agentConfigurations || !Array.isArray(settings.agentConfigurations)) {
        return [];
    }
    const localModelNames = new Set<string>();
    // Use AgentConfig type for the agent parameter
    settings.agentConfigurations.forEach((agent: AgentConfig) => {
        const provider = agent?.provider?.toLowerCase();
        if ((provider === 'ollama' || provider === 'lm studio') && agent.model) {
            localModelNames.add(agent.model);
        }
    });
    return Array.from(localModelNames);
};


// @desc    Create a new configuration profile for the logged-in user
// @route   POST /api/config/profiles
// @access  Private
export const createProfile = async (req: Request, res: Response): Promise<void> => {
  try {
    const { name, settings } = req.body;
    const userId = (req.user as IUser)?._id; // Cast req.user to IUser

    if (!userId) {
      res.status(401).json({ message: 'Not authorized' });
      return;
    }

    if (!name || !settings) {
      res.status(400).json({ message: 'Profile name and settings are required' });
      return;
    }

    // Construct the settings object carefully for saving
    const settingsToSave = {
        agentCount: settings.agentCount,
        generalInstructions: settings.generalInstructions,
        agentConfigurations: settings.agentConfigurations,
        internetSettings: settings.internetSettings,
        ragSettings: settings.ragSettings,
        maxAgentRuns: settings.maxAgentRuns,
        baseInstructions: settings.baseInstructions,
        useBaseInstructions: settings.useBaseInstructions,
        // Token limit settings
        maxContextWindow: settings.maxContextWindow,
        workingContextSize: settings.workingContextSize,
        // Explicitly handle userManagedModels
        userManagedModels: (settings.userManagedModels && typeof settings.userManagedModels === 'object')
                           ? settings.userManagedModels
                           : {} // Default to empty object if invalid or missing
    };

    console.log('[createProfile] Settings being saved:', JSON.stringify(settingsToSave, null, 2)); // Log settings before creating

    const newProfile = new ConfigurationProfile({
      name,
      settings: settingsToSave, // Save the carefully constructed settings
      userId: userId
    });

    const savedProfile = await newProfile.save();
    console.log('[createProfile] Profile saved successfully:', JSON.stringify(savedProfile.toObject({ minimize: false }), null, 2)); // Log after saving
    res.status(201).json(savedProfile);
  } catch (error) {
    console.error('Error creating profile:', error);
    if (error instanceof Error) {
        // Handle potential duplicate key error from the compound index
        if ((error as any).code === 11000) {
             res.status(400).json({ message: `Profile with name '${req.body.name}' already exists for this user` });
             return;
        }
        res.status(500).json({ message: 'Server error creating profile', error: error.message });
    } else {
        res.status(500).json({ message: 'Unknown server error creating profile' });
    }
  }
};

// @desc    Get all configuration profile names and IDs for the logged-in user
// @route   GET /api/config/profiles
// @access  Private
export const getProfiles = async (req: Request, res: Response): Promise<void> => {
  try {
    const userId = (req.user as IUser)?._id; // Cast req.user to IUser
    if (!userId) {
      res.status(401).json({ message: 'Not authorized' });
      return;
    }

    // Only select name and _id for listing, filtered by userId
    const profiles = await ConfigurationProfile.find({ userId: userId }).select('name _id createdAt updatedAt');
    res.status(200).json(profiles);
  } catch (error) {
    console.error('Error fetching profiles:', error);
     if (error instanceof Error) {
        res.status(500).json({ message: 'Server error fetching profiles', error: error.message });
    } else {
        res.status(500).json({ message: 'Unknown server error fetching profiles' });
    }
  }
};

// @desc    Get a specific configuration profile by ID for the logged-in user
// @route   GET /api/config/profiles/:id
// @access  Private
export const getProfileById = async (req: Request, res: Response): Promise<void> => {
  try {
    const profileId = req.params.id;
    const userId = (req.user as IUser)?._id; // Cast req.user to IUser

    if (!userId) {
      res.status(401).json({ message: 'Not authorized' });
      return;
    }

    // Find by ID and ensure it belongs to the logged-in user
    const profile = await ConfigurationProfile.findOne({ _id: profileId, userId: userId });

    if (!profile) {
      res.status(404).json({ message: 'Profile not found' });
      return;
    }

    res.status(200).json(profile);
  } catch (error) {
    console.error('Error fetching profile by ID:', error);
     if (error instanceof Error) {
        // Handle potential CastError if ID format is invalid
        if (error.name === 'CastError') {
            res.status(400).json({ message: 'Invalid profile ID format' });
            return;
        }
        res.status(500).json({ message: 'Server error fetching profile', error: error.message });
    } else {
        res.status(500).json({ message: 'Unknown server error fetching profile' });
    }
  }
};

// @desc    Update an existing configuration profile by ID for the logged-in user
// @route   PUT /api/config/profiles/:id
// @access  Private
export const updateProfile = async (req: Request, res: Response): Promise<void> => {
  try {
    const profileId = req.params.id;
    const userId = (req.user as IUser)?._id; // Cast req.user to IUser
    const { name, settings } = req.body;

    if (!userId) {
      res.status(401).json({ message: 'Not authorized' });
      return;
    }

    if (!name && !settings) {
      res.status(400).json({ message: 'Profile name or settings must be provided for update' });
      return;
    }

    // Find the profile ensuring it belongs to the user
    const profile = await ConfigurationProfile.findOne({ _id: profileId, userId: userId });

    if (!profile) {
      res.status(404).json({ message: 'Profile not found' });
      return;
    }

    // Update name if provided
    if (name) {
        profile.name = name;
    }

    // Update settings if provided - Merge carefully
    if (settings) {
        console.log('[updateProfile] Received settings in request body:', JSON.stringify(settings, null, 2));

        let settingsModified = false; // Flag to track if any settings changed

        // Merge top-level fields & track changes
        if (settings.agentCount !== undefined && profile.settings.agentCount !== settings.agentCount) { profile.settings.agentCount = settings.agentCount; settingsModified = true; }
        if (settings.generalInstructions !== undefined && profile.settings.generalInstructions !== settings.generalInstructions) { profile.settings.generalInstructions = settings.generalInstructions; settingsModified = true; }
        if (settings.agentConfigurations !== undefined && JSON.stringify(profile.settings.agentConfigurations) !== JSON.stringify(settings.agentConfigurations)) { profile.settings.agentConfigurations = settings.agentConfigurations; settingsModified = true; } // Basic deep compare
        if (settings.internetSettings !== undefined && JSON.stringify(profile.settings.internetSettings) !== JSON.stringify(settings.internetSettings)) { profile.settings.internetSettings = settings.internetSettings; settingsModified = true; }
        if (settings.ragSettings !== undefined && JSON.stringify(profile.settings.ragSettings) !== JSON.stringify(settings.ragSettings)) { profile.settings.ragSettings = settings.ragSettings; settingsModified = true; }
        if (settings.maxAgentRuns !== undefined && profile.settings.maxAgentRuns !== settings.maxAgentRuns) { profile.settings.maxAgentRuns = settings.maxAgentRuns; settingsModified = true; }
        if (settings.baseInstructions !== undefined && profile.settings.baseInstructions !== settings.baseInstructions) { profile.settings.baseInstructions = settings.baseInstructions; settingsModified = true; }
        if (settings.useBaseInstructions !== undefined && profile.settings.useBaseInstructions !== settings.useBaseInstructions) { profile.settings.useBaseInstructions = settings.useBaseInstructions; settingsModified = true; }

        // Explicitly update the userManagedModels Object
        if (settings.userManagedModels && typeof settings.userManagedModels === 'object') {
            // Check if the incoming object is different from the stored one
            // Note: Mongoose might return a Map here, convert to plain object for comparison
            const currentManagedModelsObject = profile.settings.userManagedModels instanceof Map
                                              ? Object.fromEntries(profile.settings.userManagedModels)
                                              : (profile.settings.userManagedModels || {});
            if (JSON.stringify(currentManagedModelsObject) !== JSON.stringify(settings.userManagedModels)) {
                 profile.settings.userManagedModels = settings.userManagedModels;
                 profile.markModified('settings.userManagedModels'); // Mark this specific path
                 settingsModified = true; // Mark parent path as well
                 console.log('[updateProfile] Updated profile.settings.userManagedModels with:', JSON.stringify(settings.userManagedModels, null, 2));
            }

        } else if (settings.hasOwnProperty('userManagedModels') && !settings.userManagedModels) {
             // If the key exists but is explicitly null/undefined, clear the object
             if (profile.settings.userManagedModels && Object.keys(profile.settings.userManagedModels).length > 0) {
                 profile.settings.userManagedModels = {};
                 profile.markModified('settings.userManagedModels');
                 settingsModified = true;
                 console.log('[updateProfile] Cleared profile.settings.userManagedModels');
             }
        }

        // If any part of the settings object was modified, mark the whole path
        if (settingsModified) {
             profile.markModified('settings');
        }
    }

    console.log('[updateProfile] Profile object before save:', JSON.stringify(profile.toObject({ minimize: false }), null, 2));

    const updatedProfile = await profile.save();
    console.log('[updateProfile] Profile object after save:', JSON.stringify(updatedProfile.toObject({ minimize: false }), null, 2));

    res.status(200).json(updatedProfile);
  } catch (error) {
    console.error('Error updating profile:', error);
     if (error instanceof Error) {
        // Handle potential duplicate key error from the compound index
        if ((error as any).code === 11000) {
             res.status(400).json({ message: `Another profile with name '${req.body.name}' already exists for this user` });
             return;
        }
         // Handle potential CastError if ID format is invalid
        if (error.name === 'CastError') {
            res.status(400).json({ message: 'Invalid profile ID format' });
            return;
        }
        res.status(500).json({ message: 'Server error updating profile', error: error.message });
    } else {
        res.status(500).json({ message: 'Unknown server error updating profile' });
    }
  }
};

// @desc    Delete a configuration profile by ID for the logged-in user
// @route   DELETE /api/config/profiles/:id
// @access  Private
export const deleteProfile = async (req: Request, res: Response): Promise<void> => {
  try {
    const profileId = req.params.id;
    const userId = (req.user as IUser)?._id; // Cast req.user to IUser

    if (!userId) {
      res.status(401).json({ message: 'Not authorized' });
      return;
    }

    // Find the profile ensuring it belongs to the user before deleting
    const result = await ConfigurationProfile.deleteOne({ _id: profileId, userId: userId });

    if (result.deletedCount === 0) {
      res.status(404).json({ message: 'Profile not found' });
      return;
    }

    res.status(200).json({ message: 'Profile deleted successfully' });
  } catch (error) {
    console.error('Error deleting profile:', error);
     if (error instanceof Error) {
         // Handle potential CastError if ID format is invalid
        if (error.name === 'CastError') {
            res.status(400).json({ message: 'Invalid profile ID format' });
            return;
        }
        res.status(500).json({ message: 'Server error deleting profile', error: error.message });
    } else {
        res.status(500).json({ message: 'Unknown server error deleting profile' });
    }
  }
};
