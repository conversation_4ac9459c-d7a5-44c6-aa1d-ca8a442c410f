import React from 'react'; // Remove useState import if no longer needed locally
import AgentConfigCard from './AgentConfigCard';
import { useConfigStore } from '../store/configStore'; // Import the Zustand store

const MIN_AGENTS = 1;
const MAX_AGENTS = 5;

// Define props interface
interface AgentSettingsTabProps {
  providers: string[];
  providersLoading: boolean;
  providersError: string | null;
}

const AgentSettingsTab: React.FC<AgentSettingsTabProps> = ({ 
  providers, 
  providersLoading, 
  providersError 
}) => {
  // Use Zustand store for agent count
  const agentCount = useConfigStore((state) => state.agentCount);
  const setAgentCount = useConfigStore((state) => state.setAgentCount);

  const handleAgentCountChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    let count = parseInt(e.target.value, 10);
    if (isNaN(count)) {
      // If input is cleared or invalid, maybe default to MIN_AGENTS or handle differently
      // For now, let's just ensure it doesn't break the store update
      count = MIN_AGENTS; 
    }
    // Store action already handles clamping
    setAgentCount(count); 
  };

  return (
    <div>
      <h3 className="text-lg font-semibold mb-4">Agent Settings</h3>
      
      {/* Agent Count Input */}
      <div className="mb-6">
        <label htmlFor="agentCount" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
          Number of Agents:
        </label>
        <input
          type="number"
          id="agentCount"
          name="agentCount"
          value={agentCount}
          onChange={handleAgentCountChange}
          min={MIN_AGENTS}
          max={MAX_AGENTS}
          className="w-20 p-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-white"
        />
        <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
          Select between {MIN_AGENTS} and {MAX_AGENTS} agents.
        </p>
      </div>

      {/* Display Loading/Error for Providers */}
      {providersLoading && <p className="text-sm text-gray-500 dark:text-gray-400">Loading providers...</p>}
      {providersError && <p className="text-sm text-red-600 dark:text-red-400">Error loading providers: {providersError}</p>}

      {/* Dynamically Render Agent Config Cards */}
      <div className="mt-6 space-y-4">
        {Array.from({ length: agentCount }, (_, index) => (
          // Pass providers list down to each card
          <AgentConfigCard 
            key={index} 
            agentNumber={index + 1} 
            providers={providers} 
            isLoading={providersLoading} // Pass loading state
          />
        ))}
      </div>
    </div>
  );
};

export default AgentSettingsTab;
