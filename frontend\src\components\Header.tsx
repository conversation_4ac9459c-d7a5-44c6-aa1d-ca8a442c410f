import React from 'react';
import { Link } from 'react-router-dom'; // Import Link
import { useConfigStore } from '../store/configStore'; // Import store

// Define the props interface
interface HeaderProps {
  theme: 'light' | 'dark';
  toggleTheme: () => void;
  toggleConversationList: () => void;
  startNewChat: () => void;
}

const Header: React.FC<HeaderProps> = ({ theme, toggleTheme, toggleConversationList, startNewChat }) => {
  // Get auth state and logout action from store
  const isAuthenticated = useConfigStore((state) => state.isAuthenticated);
  const logout = useConfigStore((state) => state.logout);
  const userEmail = useConfigStore((state) => state.user?.email); // Get user email for display

  return (
    <header className="flex justify-between items-center p-4 border-b border-gray-200 dark:border-gray-700 flex-shrink-0 bg-white dark:bg-gray-900 shadow-sm">
      <div className="flex items-center space-x-3 md:space-x-5">
         {/* Conversation List Toggle Button */}
         <button
            onClick={toggleConversationList}
            className="p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-800 focus:outline-none focus:ring-2 focus:ring-primary-dark dark:focus:ring-primary-light transition-colors"
            aria-label="Toggle conversation list"
         >
             {/* Simple SVG Hamburger Icon */}
              <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 text-gray-600 dark:text-gray-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                 <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
              </svg>
          </button>
          {/* New Chat Button */}
          <button
            onClick={startNewChat}
            className="flex items-center gap-2 px-4 py-2 rounded-xl bg-gradient-to-r from-primary to-blue-600 text-white hover:from-primary-dark hover:to-blue-700 dark:from-primary-light dark:to-blue-500 dark:text-white dark:hover:from-primary dark:hover:to-blue-600 transition-all duration-200 font-semibold shadow-lg hover:shadow-xl transform hover:-translate-y-0.5"
            aria-label="Start new chat"
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
            </svg>
            New Chat
          </button>
          <h1 className="text-lg md:text-xl font-bold text-primary-dark dark:text-primary-light flex items-center">
            <img src="/logo.svg" alt="MAIAChat Logo" className="h-8 w-8 mr-2" />
            MAIAChat
          </h1>
      </div>
      <div className="flex items-center space-x-3 md:space-x-4"> {/* Group right-side buttons */}
        {isAuthenticated && (
          <Link
            to="/account"
            className="flex items-center text-sm text-gray-600 dark:text-gray-300 hover:text-primary dark:hover:text-primary-light transition-colors hidden sm:flex gap-1 px-3 py-1.5 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-800"
            title="Account Settings"
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-6-3a2 2 0 11-4 0 2 2 0 014 0zm-2 4a5 5 0 00-4.546 2.916A5.986 5.986 0 0010 16a5.986 5.986 0 004.546-2.084A5 5 0 0010 11z" clipRule="evenodd" />
            </svg>
            {userEmail || 'Account'}
          </Link>
        )}
         {isAuthenticated && (
          <button
            onClick={logout}
            className="flex items-center gap-1 px-3 py-1.5 rounded-lg text-sm bg-red-500 hover:bg-red-600 text-white transition-colors shadow-md hover:shadow-lg"
            aria-label="Logout"
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M3 3a1 1 0 00-1 1v12a1 1 0 001 1h12a1 1 0 001-1V4a1 1 0 00-1-1H3zm11 3a1 1 0 10-2 0v6a1 1 0 102 0V6zm-8 7a1 1 0 100 2h4a1 1 0 100-2H6z" clipRule="evenodd" />
            </svg>
            Logout
          </button>
        )}
        <button
          onClick={toggleTheme}
          className="p-2 rounded-lg bg-gray-100 dark:bg-gray-800 hover:bg-gray-200 dark:hover:bg-gray-700 transition-colors shadow-md"
          aria-label="Toggle theme"
        >
          {theme === 'light' ? (
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-gray-700" viewBox="0 0 20 20" fill="currentColor">
              <path d="M17.293 13.293A8 8 0 016.707 2.707a8.001 8.001 0 1010.586 10.586z" />
            </svg>
          ) : (
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-yellow-300" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M10 2a1 1 0 011 1v1a1 1 0 11-2 0V3a1 1 0 011-1zm4 8a4 4 0 11-8 0 4 4 0 018 0zm-.464 4.95l.707.707a1 1 0 001.414-1.414l-.707-.707a1 1 0 00-1.414 1.414zm2.12-10.607a1 1 0 010 1.414l-.706.707a1 1 0 11-1.414-1.414l.707-.707a1 1 0 011.414 0zM17 11a1 1 0 100-2h-1a1 1 0 100 2h1zm-7 4a1 1 0 011 1v1a1 1 0 11-2 0v-1a1 1 0 011-1zM5.05 6.464A1 1 0 106.465 5.05l-.708-.707a1 1 0 00-1.414 1.414l.707.707zm1.414 8.486l-.707.707a1 1 0 01-1.414-1.414l.707-.707a1 1 0 011.414 1.414zM4 11a1 1 0 100-2H3a1 1 0 000 2h1z" clipRule="evenodd" />
            </svg>
          )}
        </button>
      </div>
    </header>
  );
};

export default Header;
