import passport from 'passport';
import { Strategy as GoogleStrategy } from 'passport-google-oauth20';
import mongoose from 'mongoose';
import User, { IUser } from '../models/User';
import { AuthUser } from '../types/express';

// Helper function to transform a Mongoose user document into our AuthUser format
const transformUserForAuth = (user: IUser): AuthUser => {
  const userObj = user.toObject();
  return {
    ...userObj,
    _id: userObj._id,
    id: userObj._id.toString(), // Add id property that maps to _id
  } as AuthUser;
};

// Basic user serialization/deserialization for Passport's internal flow
// We primarily use JWTs, but these are needed for the OAuth handshake.
passport.serializeUser((user: any, done) => {
  // Use _id if available, otherwise use id
  const userId = user._id ? user._id.toString() : user.id;
  done(null, userId);
});

passport.deserializeUser(async (id: string, done) => {
  try {
    const user = await User.findById(id);
    if (!user) {
      return done(null, null);
    }
    // Transform the user document to include the id property
    const authUser = transformUserForAuth(user);
    done(null, authUser);
  } catch (err) {
    done(err, null);
  }
});

// Configure Google OAuth 2.0 Strategy
if (!process.env.GOOGLE_CLIENT_ID || !process.env.GOOGLE_CLIENT_SECRET) {
  console.warn('WARNING: GOOGLE_CLIENT_ID or GOOGLE_CLIENT_SECRET environment variables not set. Google OAuth will not function.');
} else {
  passport.use(
    new GoogleStrategy(
      {
        clientID: process.env.GOOGLE_CLIENT_ID,
        clientSecret: process.env.GOOGLE_CLIENT_SECRET,
        // IMPORTANT: This callbackURL must match the one registered in Google Cloud Console
        // and should be the full URL including your backend domain.
        // We define the route part here; the full URL depends on deployment.
        callbackURL: '/api/auth/google/callback', // Relative path handled by router
        scope: ['profile', 'email'], // Request profile and email information
      },
      async (accessToken, refreshToken, profile, done) => {
        try {
          // 1. Find user by Google ID
          let user = await User.findOne({ googleId: profile.id });

          if (user) {
            // Transform the user document to include the id property
            return done(null, transformUserForAuth(user)); // User found, return it
          }

          // 2. If not found by Google ID, find by email
          // Ensure profile.emails exists and has at least one verified email
          const email = profile.emails?.find(e => e.verified)?.value;
          if (!email) {
            return done(new Error('No verified email found in Google profile.'), undefined);
          }

          user = await User.findOne({ email: email });

          if (user) {
            // User found by email, link Google ID (they might have signed up with email/pass before)
            user.googleId = profile.id;
            // Optionally update other fields like name/picture if needed
            await user.save();
            // Transform the user document to include the id property
            return done(null, transformUserForAuth(user));
          }

          // 3. If not found by Google ID or email, create a new user
          const newUser = new User({
            googleId: profile.id,
            email: email,
            // Set default subscription tier or other required fields
            subscriptionTier: 'free',
            // Password is not set for OAuth users
          });
          await newUser.save();
          // Transform the user document to include the id property
          return done(null, transformUserForAuth(newUser));

        } catch (err) {
          console.error("Error during Google OAuth strategy verification:", err);
          return done(err, undefined);
        }
      }
    )
  );
}

export default passport;
