# Stage 1: Build the TypeScript application
FROM node:20-alpine AS build

# Set working directory
WORKDIR /app/backend

# Copy package.json and package-lock.json
COPY backend/package*.json ./

# Install dependencies
RUN npm install

# Copy the rest of the backend application code
COPY backend/ .

# Compile TypeScript to JavaScript
RUN npm run build

# Stage 2: Setup production environment
FROM node:20-alpine

WORKDIR /app/backend

# Create a non-root user and group
RUN addgroup -S appgroup && adduser -S appuser -G appgroup

# Copy package files first for better caching
COPY --from=build /app/backend/package*.json ./

# Install ONLY production dependencies
# Using npm install instead of npm ci to handle package-lock.json mismatches
RUN npm install --omit=dev

# Copy compiled JavaScript code from build stage
COPY --from=build /app/backend/dist ./dist

# Copy public assets if needed by the server directly (e.g., for serving static files not handled by a proxy)
# COPY --from=build /app/backend/public ./public

# Change ownership to non-root user
<PERSON><PERSON> chown -R appuser:appgroup /app/backend

# Switch to non-root user
USER appuser

# Copy .env.example (optional, if needed for reference or entrypoint script)
# COPY --from=build /app/backend/.env.example ./.env.example

# Expose the port the application runs on (default 3000)
EXPOSE 3000

# Command to run the application
# Assumes your build output is in 'dist' and the entry point is 'server.js'
CMD ["node", "dist/server.js"]
