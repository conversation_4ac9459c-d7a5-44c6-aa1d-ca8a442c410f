// Define custom error classes for different parts of the application

export class BaseError extends Error {
    public readonly code: string;
    public readonly details?: unknown; // Use unknown instead of any

    constructor(message: string, code: string = 'INTERNAL_ERROR', details?: unknown) { // Use unknown
        super(message);
        this.name = this.constructor.name; // Set the error name to the class name
        this.code = code;
        this.details = details;
        // Ensure the stack trace is captured correctly (important for V8 environments like Node.js)
        if (Error.captureStackTrace) {
            Error.captureStackTrace(this, this.constructor);
        }
    }
}

export class LlmError extends BaseError {
    constructor(message: string, code: string = 'LLM_ERROR', details?: unknown) { // Use unknown
        super(message, code, details);
    }
}

export class RagError extends BaseError {
    constructor(message: string, code: string = 'RAG_ERROR', details?: unknown) { // Use unknown
        super(message, code, details);
    }
}

export class SearchError extends BaseError {
    constructor(message: string, code: string = 'SEARCH_ERROR', details?: unknown) { // Use unknown
        super(message, code, details);
    }
}

export class OrchestrationError extends BaseError {
    constructor(message: string, code: string = 'ORCHESTRATION_ERROR', details?: unknown) { // Use unknown
        super(message, code, details);
    }
}

export class ApiKeyError extends BaseError {
    constructor(message: string, code: string = 'API_KEY_ERROR', details?: unknown) { // Use unknown
        super(message, code, details);
    }
}

export class AuthError extends BaseError {
    constructor(message: string, code: string = 'AUTH_ERROR', details?: unknown) { // Use unknown
        super(message, code, details);
    }
}

// Add more specific error types as needed
