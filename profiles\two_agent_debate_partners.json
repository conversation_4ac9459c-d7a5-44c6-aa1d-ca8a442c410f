{"name": "Debate Partners", "settings": {"agentCount": 2, "generalInstructions": "You are Debate Partners tasked with exploring different perspectives on a topic. The first agent will present one perspective, and the second agent will present an opposing or alternative perspective. The goal is to provide a balanced and nuanced understanding of the topic.", "agentConfigurations": [{"provider": "OpenAI", "model": "gpt-4o", "instructions": "As the first debater, your role is to present the strongest case for one perspective on the topic. Use logical reasoning, evidence, and persuasive techniques to make your case. Be respectful but confident in your position. Consider potential counterarguments and address them proactively.", "useDefaultInstructions": false, "internetEnabled": true, "temperature": 0.7, "maxTokens": 4096}, {"provider": "OpenAI", "model": "gpt-4o", "instructions": "As the second debater, your role is to present a different or opposing perspective to the first debater. Analyze their arguments and provide thoughtful counterpoints. Use logical reasoning, evidence, and persuasive techniques to make your case. Be respectful but confident in your position. Your goal is not to 'win' but to help explore the full complexity of the topic.", "useDefaultInstructions": false, "internetEnabled": true, "temperature": 0.7, "maxTokens": 4096}], "internetSettings": {"enabled": true, "searchProvider": "Google", "searchApiKey": "", "includedDomains": [], "excludedDomains": []}, "ragSettings": {"enabled": false, "chunkingStrategy": "semantic", "chunkSize": 512, "chunkOverlap": 50, "embeddingModel": "Xenova/all-mpnet-base-v2", "retrievalNResults": 5, "retrievalThreshold": 0.7, "useReranking": false, "useQueryExpansion": false}, "maxAgentRuns": 1, "baseInstructions": "", "useBaseInstructions": false, "maxContextWindow": 20000, "workingContextSize": 16384}}