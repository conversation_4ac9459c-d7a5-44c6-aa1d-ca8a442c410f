import { Request, Response, NextFunction } from 'express';
import { verifyToken } from '../utils/authUtils';
import User, { IUser } from '../models/User';
import { Socket } from 'socket.io';
import { AuthUser } from '../types/express';

// Extend the Socket.IO Socket interface to include the user object
declare module 'socket.io' {
  interface Socket {
    user?: AuthUser; // Use the AuthUser interface for consistency
  }
}

// Helper function to transform a Mongoose user document into our AuthUser format
const transformUserForAuth = (user: IUser): AuthUser => {
  const userObj = user.toObject();
  return {
    ...userObj,
    _id: userObj._id,
    id: userObj._id.toString(), // Add id property that maps to _id
  } as AuthUser;
};


export const protect = async (req: Request, res: Response, next: NextFunction) => {
  let token;

  // Check for token in Authorization header (Bearer token)
  if (
    req.headers.authorization &&
    req.headers.authorization.startsWith('Bearer')
  ) {
    try {
      // Get token from header (split 'Bearer <token>')
      token = req.headers.authorization.split(' ')[1];

      // Verify token
      const decoded = verifyToken(token);

      // Use type assertion for decoded payload if verifyToken returns a generic object
      const payload = decoded as { id: string; email: string; /* other fields */ };

      if (!payload || !payload.id) {
        return res.status(401).json({ message: 'Not authorized, token failed verification' });
      }

      // Attach user payload to the request object
      // Fetch the full user from DB here to ensure they still exist and attach the model
      const user = await User.findById(payload.id).select('-passwordHash');
      if (!user) {
        return res.status(401).json({ message: 'Not authorized, user not found' });
      }

      // Transform the user document to include the id property
      const authUser = transformUserForAuth(user);

      req.user = authUser as Express.User; // Attach transformed user object

      next(); // Proceed to the next middleware or route handler
    } catch (error) {
      console.error('Auth Middleware Error:', error);
      res.status(401).json({ message: 'Not authorized, token processing error' });
    }
  }

  if (!token) {
    res.status(401).json({ message: 'Not authorized, no token provided' });
  }
};

// Optional: Middleware for specific roles or subscription tiers (Example)
// export const authorize = (...roles: string[]) => {
//   return (req: Request, res: Response, next: NextFunction) => {
//     if (!req.user || !req.user.subscriptionTier || !roles.includes(req.user.subscriptionTier)) {
//       return res.status(403).json({ message: 'Forbidden: Insufficient permissions' });
//     }
//     next();
//   };
// };


// --- Socket.IO Middleware ---

// Middleware function for Socket.IO connections - Make it async
export const socketProtect = async (socket: Socket, next: (err?: Error) => void) => {
  // Extract token from handshake query or auth object (adjust based on client implementation)
  const token = socket.handshake.auth?.token || socket.handshake.query?.token;

  if (!token || typeof token !== 'string') {
    console.error('Socket Auth Error: No token provided');
    return next(new Error('Authentication error: No token provided'));
  }

  try {
    // Verify the token
    const decoded = verifyToken(token);

    // Use type assertion for decoded payload
    const payload = decoded as { id: string; email: string; /* other fields */ };

    if (!payload || !payload.id) {
      console.error('Socket Auth Error: Token verification failed');
      return next(new Error('Authentication error: Invalid token'));
    }

    // Fetch the full user from DB
    const user = await User.findById(payload.id).select('-passwordHash');
    if (!user) {
        console.error(`Socket Auth Error: User not found for ID: ${payload.id}`);
        return next(new Error('Authentication error: User not found'));
    }

    // Transform the user document to include the id property
    const authUser = transformUserForAuth(user);

    // Attach user object to the socket
    socket.user = authUser; // Attach transformed user object

    console.log(`Socket authenticated: ${socket.id}, User ID: ${socket.user._id}`); // Use _id here
    next(); // Proceed with the connection
  } catch (error) {
    console.error('Socket Auth Middleware Error:', error);
    next(new Error('Authentication error: Token processing error'));
  }
};
