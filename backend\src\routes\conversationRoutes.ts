import express from 'express';
import {
    listConversations,
    getConversationById,
    deleteConversation,
    deleteAllConversations // Import the new controller function
} from '../controllers/conversationController';
import { protect } from '../middleware/authMiddleware'; // Import protect middleware

const router = express.Router();

// GET /api/conversations - List all conversations (summary) for the logged-in user
router.get('/', protect, listConversations); // Apply protect middleware

// GET /api/conversations/:id - Get a specific conversation for the logged-in user
router.get('/:id', protect, getConversationById); // Apply protect middleware

// --- Reorder DELETE routes: specific before parameterized ---
// DELETE /api/conversations/all - Delete ALL conversations for the logged-in user
router.delete('/all', protect, deleteAllConversations); // Apply protect middleware

// DELETE /api/conversations/:id - Delete a specific conversation for the logged-in user
router.delete('/:id', protect, deleteConversation); // Apply protect middleware


export default router;
