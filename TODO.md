# MAIA-Web Development Tasks & Refinements

## Development Process Instructions

1.  **Work Sequentially:** Address tasks one by one from the relevant section (Minor, Middle, Major). Prioritize tasks within the "Middle" section, especially **Parameter Handling** and **Testing**. Do not combine multiple unrelated tasks in a single commit.
2.  **Focus:** Concentrate only on the current task. Implement the required changes and necessary supporting code.
3.  **Test:** After completing each task, **thoroughly test** the changes locally. Ensure the core functionality works as expected and no regressions were introduced. *Crucially, implement automated tests as part of the "Middle Tasks" section.*
4.  **Record Completion:** Once a task is completed and tested, add an entry to the `Steps_Completed.md` file.
    *   The date of completion.
    *   A clear description of the completed task (you can copy/paste from this `TODO.md`).
    *   Optionally, a brief note about the implementation or challenges.
    *   Example entry: `* **2025-04-05:** Reworked AgentConfigCard to dynamically display parameters based on selected provider.`
5.  **Update TODO:** Mark the completed task in this `TODO.md` file using `- [x]`.
6.  **Proceed:** Move to the next task only after completing, testing, and recording the current one.

---

## Critical Tasks (Production Deployment)

- [x] **Fix API Endpoint Configuration:** Update the frontend to use environment-based API endpoints instead of hardcoded localhost URLs. Implement proper environment configuration to support both development and production environments. (Requires manual creation of `.env` from `.env.example`)

- [x] **CORS Configuration:** Update the backend CORS settings to accept requests from the production domain (https://maiachat.onrender.com) instead of only localhost. (Achieved via `FRONTEND_URL` env variable in existing code)

- [x] **Backend Deployment:** Deploy the backend service to Render or another cloud provider, ensuring it's accessible to the frontend application. (Requires manual deployment on Render using build command `npm install && npm run build` and start command `npm start`, with appropriate environment variables set)

- [x] **Database Setup:** Configure MongoDB Atlas or another cloud database service for production use instead of relying on a local MongoDB instance. (Requires manual setup on MongoDB Atlas and setting the `MONGO_URI` environment variable on Render)

- [x] **Environment Variables:** Set up proper environment variables in the production environment for API keys, database connections, and other sensitive information. (Requires manual verification and configuration in Render backend service environment settings, based on `.env.example`. Also requires `VITE_BACKEND_BASE_URL` set in frontend deployment.)

- [x] **Google Authentication:** Implement Google OAuth login functionality as an alternative to email/password authentication.

- [x] **Landing Page:** Create an informative landing/home page that users see before logging in, explaining the application's features and benefits.

- [x] **Favicon and Assets:** Add proper favicon and missing assets to improve the visual appearance and branding of the application.

---

## Minor Tasks

- [ ] **Image Visibility for Agents:** Modify the agent system to allow agents to see and reference images shared in the conversation. Currently, agents cannot see images that are generated or uploaded in the chat.

- [ ] **Knowledge Base Folder Selection:** Fix the Knowledge Base folder change button to either:
  - Allow selection of local hard drive folders outside the project, or
  - Restrict navigation to only the designated knowledge base area without allowing access to root folders

- [ ] **Multi-User Knowledge Base Support:** Implement user-specific knowledge base paths (e.g., `./user_id/Knowledge_base/`) to support multiple users with separate knowledge bases

- [ ] **Fix Knowledge Base Path Handling:** Resolve the issue where selecting a folder different from `knowledge_base_ts` still sets it as that folder with incorrect path messages

- [ ] **Local vs Server Storage Functionality:** Implement actual functionality for the Local Storage and Server Storage selection options for knowledge bases

- [ ] **Multiple File Upload Support:** Enhance the Upload Document button to allow selecting and uploading multiple files at once instead of just a single file

- [ ] **Large Content Handling:** Fix the issue where pasting very large content (e.g., 2000+ lines of code) into the prompt window causes the agent to not process it and report that no data was provided, while smaller content (e.g., 900 lines) works fine

- [ ] **RAG Word Document Support:** Fix RAG processing to properly handle Word documents and possibly other extensions that should be supported, which currently aren't being processed correctly (unlike PDF files which seems to work fine)

- [ ] **Image Generation Button:** Add a dedicated Image Mode button instead of using the current logic that tries to detect image generation intent, to avoid misinterpretations by AI models.

- [ ] **Feature Request Form:** Add a "Request New Features" button near the email or at the top of the screen that opens a new window with a form for users to submit feature requests, bug reports, or feedback directly to your inbox, with a dropdown menu to categorize the submission type

---

## Pre-Release Preparation Tasks (Critical for Publication)

### Testing Infrastructure (High Priority)

- [ ] **Backend Unit Tests:** Set up Jest testing framework for backend services (llmService, ragHandler, agentOrchestrator, authController)

- [ ] **Frontend Unit Tests:** Set up Vitest/Jest for React components testing (AgentDiscussionPanel, PromptInputArea, ConfigurationTabs)

- [ ] **Integration Tests:** Create tests for API endpoints, database operations, and service integrations

- [ ] **End-to-End Tests:** Set up Playwright or Cypress for complete user workflow testing (login, chat, configuration)

- [ ] **Test Coverage:** Achieve minimum 70% test coverage for critical business logic

### Security Hardening (High Priority)

- [x] **Rate Limiting:** Implement express-rate-limit for API endpoints to prevent abuse

- [x] **Security Headers:** Add helmet.js middleware for security headers (CSP, HSTS, etc.)

- [x] **Input Validation:** Add comprehensive input validation and sanitization middleware

- [x] **Dependency Security Audit:** Run npm audit and fix security vulnerabilities

- [x] **Environment Security:** Ensure all sensitive data uses environment variables, no hardcoded secrets

- [ ] **HTTPS Configuration:** Set up SSL/TLS certificates for production deployment

### Legal and Compliance (High Priority)

- [x] **License File:** Add appropriate open-source license (MIT, Apache 2.0, or proprietary)

- [x] **Terms of Service:** Create comprehensive terms of service document

- [x] **Privacy Policy:** Write privacy policy covering data collection, storage, and usage

- [ ] **Copyright Notices:** Add proper copyright notices to source files

- [x] **GDPR Compliance:** Implement data protection measures if targeting EU users

### Documentation Completion (Medium Priority)

- [ ] **API Documentation:** Generate comprehensive API documentation using Swagger/OpenAPI

- [ ] **Deployment Guide:** Create detailed production deployment instructions

- [ ] **User Manual:** Write user guide covering all features and functionality

- [ ] **Developer Documentation:** Document code architecture, contribution guidelines, and development setup

- [ ] **Troubleshooting Guide:** Create common issues and solutions documentation

### Production Deployment Setup (Medium Priority)

- [ ] **Environment Configuration:** Set up production environment variables and configuration

- [ ] **Database Migration Scripts:** Create database migration and seeding scripts

- [ ] **Health Check Endpoints:** Implement comprehensive health check and status endpoints

- [ ] **Graceful Shutdown:** Implement proper application shutdown handling

- [ ] **Container Optimization:** Optimize Docker images for production (multi-stage builds, security)

### Error Handling and Monitoring (Medium Priority)

- [ ] **Centralized Logging:** Implement structured logging with Winston or similar

- [ ] **Error Tracking:** Set up error monitoring service (Sentry, Bugsnag, or similar)

- [ ] **Performance Monitoring:** Add application performance monitoring (APM)

- [ ] **Uptime Monitoring:** Set up external uptime monitoring and alerting

- [ ] **Log Aggregation:** Configure log collection and analysis system

### Performance Optimization (Medium Priority)

- [ ] **Database Optimization:** Add database indexes, query optimization, connection pooling

- [ ] **Caching Strategy:** Implement Redis caching for frequently accessed data

- [ ] **Asset Optimization:** Optimize frontend assets (minification, compression, CDN)

- [ ] **Load Testing:** Perform load testing to identify performance bottlenecks

- [ ] **Memory Management:** Optimize memory usage and prevent memory leaks

### Data Management and Backup (Medium Priority)

- [ ] **Database Backup Strategy:** Set up automated database backups

- [ ] **Data Migration Tools:** Create tools for data import/export and migration

- [ ] **Disaster Recovery Plan:** Document and test disaster recovery procedures

- [ ] **Data Retention Policy:** Implement data retention and cleanup policies

### User Experience Polish (Low Priority)

- [ ] **Accessibility Compliance:** Ensure WCAG 2.1 AA compliance for accessibility

- [ ] **Mobile Responsiveness:** Test and optimize mobile user experience

- [ ] **User Onboarding:** Create guided tour or tutorial for new users

- [ ] **Error Messages:** Improve user-friendly error messages and feedback

- [ ] **Loading States:** Enhance loading indicators and progress feedback

### Release Preparation (Final Steps)

- [ ] **Version Management:** Set up semantic versioning and release tagging

- [ ] **Release Notes:** Create comprehensive release notes and changelog

- [ ] **Beta Testing:** Conduct beta testing with limited user group

- [ ] **Launch Checklist:** Create and execute final pre-launch checklist

- [ ] **Marketing Materials:** Prepare project description, screenshots, and promotional content

---
