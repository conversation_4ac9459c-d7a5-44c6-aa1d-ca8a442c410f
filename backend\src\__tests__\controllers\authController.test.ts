import request from 'supertest';
import express from 'express';
import { registerUser, loginUser, getCurrentUser } from '../../controllers/authController';
import User from '../../models/User';
import { protect } from '../../middleware/authMiddleware';

// Create test app
const app = express();
app.use(express.json());

// Setup routes
app.post('/register', registerUser);
app.post('/login', loginUser);
app.get('/me', protect, getCurrentUser);

describe('Auth Controller', () => {
  const testUser = {
    email: '<EMAIL>',
    password: 'testPassword123'
  };

  describe('POST /register', () => {
    it('should register a new user successfully', async () => {
      const response = await request(app)
        .post('/register')
        .send(testUser)
        .expect(201);

      expect(response.body).toHaveProperty('token');
      expect(response.body).toHaveProperty('user');
      expect(response.body.user.email).toBe(testUser.email);
      expect(response.body.user).not.toHaveProperty('passwordHash');
    });

    it('should not register user with existing email', async () => {
      // First registration
      await request(app)
        .post('/register')
        .send(testUser)
        .expect(201);

      // Second registration with same email
      const response = await request(app)
        .post('/register')
        .send(testUser)
        .expect(400);

      expect(response.body).toHaveProperty('message');
      expect(response.body.message).toContain('already exists');
    });

    it('should validate required fields', async () => {
      const response = await request(app)
        .post('/register')
        .send({ email: '<EMAIL>' }) // Missing password
        .expect(400);

      expect(response.body).toHaveProperty('message');
    });

    it('should validate email format', async () => {
      const response = await request(app)
        .post('/register')
        .send({
          email: 'invalid-email',
          password: 'testPassword123'
        })
        .expect(400);

      expect(response.body).toHaveProperty('message');
    });
  });

  describe('POST /login', () => {
    beforeEach(async () => {
      // Create a test user before each login test
      await request(app)
        .post('/register')
        .send(testUser);
    });

    it('should login with valid credentials', async () => {
      const response = await request(app)
        .post('/login')
        .send(testUser)
        .expect(200);

      expect(response.body).toHaveProperty('token');
      expect(response.body).toHaveProperty('user');
      expect(response.body.user.email).toBe(testUser.email);
    });

    it('should not login with invalid email', async () => {
      const response = await request(app)
        .post('/login')
        .send({
          email: '<EMAIL>',
          password: testUser.password
        })
        .expect(401);

      expect(response.body).toHaveProperty('message');
    });

    it('should not login with invalid password', async () => {
      const response = await request(app)
        .post('/login')
        .send({
          email: testUser.email,
          password: 'wrongPassword'
        })
        .expect(401);

      expect(response.body).toHaveProperty('message');
    });

    it('should validate required fields', async () => {
      const response = await request(app)
        .post('/login')
        .send({ email: testUser.email }) // Missing password
        .expect(400);

      expect(response.body).toHaveProperty('message');
    });
  });

  describe('GET /me', () => {
    let authToken: string;

    beforeEach(async () => {
      // Register and get token
      const response = await request(app)
        .post('/register')
        .send(testUser);
      
      authToken = response.body.token;
    });

    it('should get current user with valid token', async () => {
      const response = await request(app)
        .get('/me')
        .set('Authorization', `Bearer ${authToken}`)
        .expect(200);

      expect(response.body).toHaveProperty('user');
      expect(response.body.user.email).toBe(testUser.email);
    });

    it('should not get user without token', async () => {
      const response = await request(app)
        .get('/me')
        .expect(401);

      expect(response.body).toHaveProperty('message');
    });

    it('should not get user with invalid token', async () => {
      const response = await request(app)
        .get('/me')
        .set('Authorization', 'Bearer invalid-token')
        .expect(401);

      expect(response.body).toHaveProperty('message');
    });
  });
});
