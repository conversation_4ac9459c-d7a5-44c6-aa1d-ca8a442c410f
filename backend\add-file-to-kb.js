// <PERSON>ript to manually add a file to the knowledge base
const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// Function to manually add a file to the knowledge base
async function addFileToKnowledgeBase() {
  try {
    console.log('Starting manual file addition process...');
    
    // Find the PDF file in the uploads directory
    const uploadsDir = path.join(__dirname, 'uploads', 'rag_temp');
    if (!fs.existsSync(uploadsDir)) {
      console.log('Uploads directory does not exist:', uploadsDir);
      return;
    }
    
    // List all files in the uploads directory
    const files = fs.readdirSync(uploadsDir);
    const pdfFiles = files.filter(file => file.includes('blighter-b400-series'));
    
    if (pdfFiles.length === 0) {
      console.log('No matching PDF files found in uploads directory');
      return;
    }
    
    console.log(`Found ${pdfFiles.length} matching PDF files:`, pdfFiles);
    
    // Create a knowledge base directory if it doesn't exist
    const kbDir = path.join(__dirname, 'knowledge_bases', 'default_kb');
    if (!fs.existsSync(kbDir)) {
      fs.mkdirSync(kbDir, { recursive: true });
      console.log(`Created knowledge base directory: ${kbDir}`);
    }
    
    // Copy the PDF file to the knowledge base directory
    for (const pdfFile of pdfFiles) {
      const sourcePath = path.join(uploadsDir, pdfFile);
      const destPath = path.join(kbDir, pdfFile);
      
      fs.copyFileSync(sourcePath, destPath);
      console.log(`Copied ${pdfFile} to knowledge base directory`);
    }
    
    // Delete the existing index files to force reindexing
    const indexFiles = [
      path.join(kbDir, 'faiss_index.bin'),
      path.join(kbDir, 'index_metadata.json'),
      path.join(kbDir, 'embeddings_cache.bin')
    ];
    
    for (const indexFile of indexFiles) {
      if (fs.existsSync(indexFile)) {
        fs.unlinkSync(indexFile);
        console.log(`Deleted ${indexFile}`);
      }
    }
    
    console.log('Manual file addition process completed successfully!');
    console.log('Restart the server to reindex the knowledge base.');
  } catch (error) {
    console.error('Error during manual file addition:', error);
  }
}

addFileToKnowledgeBase();
