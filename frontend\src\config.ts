// frontend/src/config.ts

// Define the base URL for the backend server.
// Adjust this if your backend runs on a different port or domain.
// Reads from VITE_API_BASE_URL environment variable, defaulting to localhost:3000 if not set.
// Note: We use VITE_API_BASE_URL for consistency with other components
export const BACKEND_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:3000';

// You can add other frontend-specific configurations here later.
