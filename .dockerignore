# Git files
.git
.gitignore

# Node modules (these should be installed within the container)
node_modules
frontend/node_modules
backend/node_modules

# Build artifacts (if any outside specific build stages)
frontend/dist
backend/dist

# Docker files themselves
Dockerfile*
docker-compose*

# Env files (should be mounted or passed as variables, not copied)
.env
backend/.env

# OS generated files
.DS_Store
Thumbs.db

# Log files
*.log
logs
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Editor directories and files
.idea
.vscode/*
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json
*.sublime-workspace

# Misc
.cache
*.env.*.local
*.env.local
