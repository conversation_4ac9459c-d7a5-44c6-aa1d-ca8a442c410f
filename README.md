# Multi-Agent AI Assistant Web Application (Project: MAIA-Web)

**Objective:** To build a modern, responsive, subscription-based web application replacing an existing Python/PyQt6 desktop tool. This platform facilitates interaction with multiple collaborative AI agents, featuring advanced RAG, internet search, and comprehensive configuration.

**Development Approach:** This project is being developed incrementally following a detailed step-by-step plan provided in separate prompts. This README describes the **target features and architecture** of the final application.

## Target Features

### 1. Core User Interface & Layout
    - **Responsive Design:** Adapts seamlessly to desktop, tablet, and mobile screens using Tailwind CSS.
    - **Three-Column Layout:**
        - **Left Panel:** Conversation history list (`ConversationList`).
        - **Center Panel (Vertical Split):**
            - *Top:* Real-time stream of the multi-agent discussion (`AgentDiscussionPanel`). Includes loading/error states for generated images.
            - *Bottom:* User prompt input area (`PromptInputArea`) fixed at the bottom, featuring a multi-line textarea, enhanced image preview, and action buttons (Send, Stop, Clear).
    - **Right Panel:** Collapsible panel containing tabbed configuration options (`ConfigurationTabs`).
    - **Console View:** Optional, toggleable area below the prompt input for viewing system logs and status messages (`ConsoleOutput`).
    - **Theming:** Supports Dark and Light modes.
    - **File Upload:** Drag-and-drop in prompt area (images only), dedicated upload button in RAG tab.
    - **Modals:** Uses custom modal component for confirmations (e.g., deleting files/conversations).

### 2. Configuration System (`ConfigurationTabs` - Right Panel)
    - **Tabbed Interface:** Organizes settings into logical sections.
    - **Agent Configuration Tab (`AgentSettingsTab`):**
        - Select the number of active agents.
        - Configure each agent individually (`AgentConfigCard`): Provider (OpenAI, Anthropic, Google, Groq, Ollama, etc.), Model selection (with improved UI for adding/managing custom models), Custom Instructions/Role.
        - Internet access toggle *per agent*.
    - **Internet Settings Tab (`InternetSettingsTab`):**
        - Global internet access toggle.
        - Configure search providers (Serper, Google Search API).
        - Domain filtering options (exclude/include).
    - **RAG Settings Tab (`RagSettingsTab`):**
        - Global RAG toggle.
        - Configure chunking strategy, chunk size, overlap.
        - Configure embedding model selection.
        - Configure retrieval parameters (n_results, alpha threshold).
        - Configure reranking and query expansion toggles.
    - **API Keys Tab (`ApiKeysSettingsTab`):**
        - Securely manage API keys for external services. Includes show/hide toggle for key input.
    - **System Settings Tab (`SystemSettingsTab`):**
        - Configure overall system prompt or general instructions applied to all agents.
    - **Persistence:** Save/Load named configuration profiles specific to the logged-in user.

### 3. Multi-Agent Collaboration (`AgentDiscussionPanel`, Backend Service)
    - Orchestrates workflow based on loaded configuration and user prompt.
    - Agents build upon each other's responses according to instructions.
    - Real-time streaming of agent interactions to the UI via WebSockets.
    - Support for diverse LLM providers and local models (Ollama, LM Studio).

### 4. Advanced RAG (`RagSettingsTab`, Backend Service)
    - **Knowledge Base Management:** UI for uploading, viewing, and deleting indexed files (with confirmation modals).
    - **File Processing:** Handles PDF, DOCX, TXT, MD, XLSX, CSV, HTML uploads.
    - **Indexing Pipeline:** Implements advanced chunking, embedding (using `all-mpnet-base-v2` or similar), and storage in a vector store (FAISS initially).
    - **Retrieval Pipeline:** Uses hybrid search (vector + BM25), optional cross-encoder reranking, query expansion, and metadata filtering.
    - **Integration:** Retrieved context seamlessly integrated into the agent prompts.

### 5. Internet Search (`InternetSettingsTab`, Backend Service)
    - Fetches real-time search results from configured providers.
    - Performs content scraping (using Readability) and cleaning.
    - Caches results effectively.
    - Integrates search results into the agent prompts when enabled.

### 6. File & Image Handling
    - Handles document uploads via RAG tab.
    - Handles image uploads/drag-and-drop via prompt input area.
    - Processes images included in prompts.
    - Integrates with vision-capable LLMs for image analysis/understanding.
    - Image generation capabilities (OpenAI DALL-E 3, Google Imagen/Gemini).
    - Displays generated images in the chat panel with loading/error states.

### 7. Authentication & User Data
    - Secure User Registration and Login (JWT-based).
    - User-specific storage for Configuration Profiles, Conversation History, and API Keys.
    - (Conceptual) Subscription tiers and role-based access (implementation details deferred).

### 8. Conversation Management
    - Persistent storage of conversation history per user (linked to auth).
    - Ability to load and continue past conversations (`ConversationList`).
    - Delete single or all conversations (with confirmation modals).
    - Export conversation functionality (PDF, JSON - *Future*).


## Target Tech Stack

*   **Frontend:** React.js (Vite), TypeScript, Tailwind CSS, Zustand (State Management), Socket.IO Client, Axios, `@heroicons/react`.
*   **Backend:** Node.js, Express.js, TypeScript, Socket.IO, Mongoose (MongoDB ODM), JWT (Authentication), Winston/Morgan (Logging), `xlsx`, `csv-parse`, `node-html-parser`, `pdf-parse`, `mammoth`, `@xenova/transformers`, `faiss-node`.
*   **Database:** MongoDB.
*   **Vector Store:** FAISS (initially).
*   **Deployment:** Docker, Docker Compose.


## Project Structure (Target)

maia-web/
├── frontend/ # React frontend application
│ ├── src/
│ │ ├── components/ # Reusable UI: Layout, ConfigTabs, AgentCard, ChatDisplays, etc.
│ │ ├── pages/ # Top-level views: MainApp, Login, Register
│ │ ├── hooks/ # Custom React hooks
│ │ ├── store/ # Zustand store (config, auth, ui state)
│ │ ├── services/ # API call functions, Socket.IO service
│ │ └── App.tsx # Main application component
│ └── tailwind.config.js
├── backend/ # Node.js backend application
│ ├── src/
│ │ ├── controllers/ # API route handlers
│ │ ├── models/ # Mongoose schemas (User, Configuration, Conversation, ApiKey)
│ │ ├── services/ # Business logic (Agent Orchestration, RAG, Search, Auth, LLM)
│ │ ├── middleware/ # Express middleware (Auth JWT check)
│ │ ├── managers/ # State/logic managers (e.g., ConversationManager)
│ │ ├── utils/ # Utility functions (crypto, instructions, errors)
│ │ └── server.ts # Express server setup
│ └── tsconfig.json
├── docker-compose.yml # Orchestration for dev/prod
├── Dockerfile.frontend # Frontend Docker build
├── Dockerfile.backend # Backend Docker build
└── .env.example # Example environment variables

## Setup & Running

### Method 1: Local Development (Without Docker)

1.  **Clone:** `git clone <repository-url>`
2.  **Backend Setup:**
    *   Navigate to the `backend` directory: `cd backend`
    *   Copy `.env.example` to `.env`: `cp .env.example .env` (or copy manually)
    *   **Edit `backend/.env`:**
        *   Set `MONGO_URI` to your local MongoDB instance (e.g., `mongodb://localhost:27017/maia-web`).
        *   Set a strong `JWT_SECRET`.
        *   Set a strong `ENCRYPTION_SECRET` (used for API keys).
        *   Add your LLM/Search API keys (e.g., `OPENAI_API_KEY`, `SERPER_API_KEY`).
        *   Ensure `FRONTEND_URL` is `http://localhost:5173`.
        *   Set `OLLAMA_BASE_URL` / `LMSTUDIO_BASE_URL` if using local models.
    *   Install dependencies: `npm install` (This includes `xlsx`, `csv-parse`, `node-html-parser`, etc.)
    *   Run the backend server: `npm run dev` (Usually listens on http://localhost:3000)
3.  **Frontend Setup:**
    *   Navigate to the `frontend` directory: `cd ../frontend`
    *   Install dependencies: `npm install` (This includes `@heroicons/react`)
    *   Run the frontend dev server: `npm run dev` (Usually opens http://localhost:5173 in your browser)

### Method 2: Using Docker Compose (Recommended for Production-like Environment)

1.  **Clone:** `git clone <repository-url>`
2.  **Install Docker:** Ensure Docker Desktop or Docker Engine with Compose plugin is installed and running.
3.  **Root Environment File:**
    *   Copy `backend/.env.example` to the **project root** directory (where `docker-compose.yml` is) and rename it to `.env`.
    *   **Edit the root `.env` file:**
        *   Set a strong `JWT_SECRET`.
        *   Set a strong `ENCRYPTION_SECRET`.
        *   Add your LLM/Search API keys.
        *   Verify `MONGO_URI` is `mongodb://mongo:27017/maia_db` (uses the Docker service name).
        *   Verify `FRONTEND_URL` (e.g., `http://localhost:5173`).
        *   Verify `VITE_API_BASE_URL` is `/api` (for Nginx proxying - if using).
        *   Verify `VITE_SOCKET_SERVER_URL` is empty (for same-origin connection via Nginx - if using).
        *   Adjust `OLLAMA_BASE_URL` / `LMSTUDIO_BASE_URL` if you run local models on the host (e.g., `http://host.docker.internal:11434`).
4.  **Build and Run:**
    *   Open a terminal in the project root directory.
    *   Run: `docker compose up --build -d` (`-d` runs in detached mode).
5.  **Access:** Open http://localhost:5173 (or the host port mapped to the `frontend` service in `docker-compose.yml`) in your browser.
6.  **Stopping:** Run `docker compose down` in the project root directory.

## Deployment

Use the provided Docker configurations (`Dockerfile.frontend`, `Dockerfile.backend`, `docker-compose.yml`) as a base for containerized deployment. Adapt the `docker-compose.yml` and potentially create an `nginx.conf` for production environments (HTTPS, caching, load balancing, etc.). Ensure all necessary environment variables (including `JWT_SECRET` and `ENCRYPTION_SECRET`) are securely managed in your deployment environment. See `docs/deployment` (to be created) for more detailed instructions.
