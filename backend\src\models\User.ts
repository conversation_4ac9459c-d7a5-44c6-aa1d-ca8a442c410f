import mongoose, { Schema, Document } from 'mongoose';

// Interface representing a document in MongoDB.
export interface IUser extends Document {
  email: string;
  passwordHash?: string;
  googleId?: string;
  subscriptionTier: 'free' | 'basic' | 'premium'; // Example tiers
  // --- Conceptual Subscription Fields (for future integration) ---
  stripeCustomerId?: string; // Example: Store Stripe Customer ID
  subscriptionId?: string;   // Example: Store subscription ID from payment provider
  subscriptionStatus?: 'active' | 'inactive' | 'canceled' | 'past_due'; // Example statuses
  subscriptionEndDate?: Date; // Example: Date when current subscription period ends
  // --- End Conceptual Fields ---
  lastUsedProfileId?: mongoose.Schema.Types.ObjectId; // Store the ID of the last used configuration profile
  createdAt: Date;
  updatedAt: Date;
}

// Schema corresponding to the document interface.
const UserSchema: Schema = new Schema(
  {
    email: {
      type: String,
      required: true,
      unique: true, // Ensure emails are unique
      lowercase: true, // Store emails in lowercase
      trim: true, // Remove leading/trailing whitespace
      match: [/.+\@.+\..+/, 'Please fill a valid email address'], // Basic email format validation
    },
    passwordHash: {
      type: String,
      required: false,
    },
    googleId: {
      type: String,
      unique: true,
      sparse: true,
    },
    subscriptionTier: {
      type: String,
      enum: ['free', 'basic', 'premium'],
      default: 'free',
    },
    // --- Conceptual Subscription Fields ---
    stripeCustomerId: { type: String, unique: true, sparse: true }, // Unique, but allow nulls
    subscriptionId: { type: String },
    subscriptionStatus: { type: String, enum: ['active', 'inactive', 'canceled', 'past_due'] },
    subscriptionEndDate: { type: Date },
    // --- End Conceptual Fields ---
    lastUsedProfileId: { type: mongoose.Schema.Types.ObjectId, ref: 'ConfigurationProfile' }, // Reference to the last used profile
  },
  {
    timestamps: true, // Automatically add createdAt and updatedAt fields
  }
);

// Export the model.
const User = mongoose.model<IUser>('User', UserSchema);

export default User;
