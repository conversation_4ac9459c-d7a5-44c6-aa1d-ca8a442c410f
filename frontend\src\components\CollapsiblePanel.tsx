import React, { useState, useEffect, useRef } from 'react';

interface CollapsiblePanelProps {
  title: string;
  isVisible: boolean;
  isPinned: boolean;
  onTogglePin: () => void;
  onToggleVisibility: () => void;
  children: React.ReactNode;
  position: 'left' | 'right';
  width: string;
  minWidth: string;
  maxWidth: string;
  className?: string;
  initialWidth?: string;
  onResize?: (newWidth: number) => void;
  backgroundColor?: string;
  showPartialWhenCollapsed?: boolean;
  partialWidth?: string;
}

const CollapsiblePanel: React.FC<CollapsiblePanelProps> = ({
  title,
  isVisible,
  isPinned,
  onTogglePin,
  onToggleVisibility,
  children,
  position,
  width,
  minWidth,
  maxWidth,
  className = '',
  initialWidth,
  onResize,
  backgroundColor = position === 'left' ? 'bg-gray-50 dark:bg-gray-900' : 'bg-surface-light dark:bg-surface-dark',
  showPartialWhenCollapsed = false,
  partialWidth = '40px',
}) => {
  const [isHovering, setIsHovering] = useState(false);
  const [currentWidth, setCurrentWidth] = useState(initialWidth || width);
  const hoverTimeoutRef = useRef<number | null>(null);
  const panelRef = useRef<HTMLDivElement>(null);
  const resizingRef = useRef(false);
  const startXRef = useRef(0);
  const startWidthRef = useRef(0);

  // Handle hover detection with delay
  const handleMouseEnter = () => {
    if (hoverTimeoutRef.current) {
      window.clearTimeout(hoverTimeoutRef.current);
    }
    hoverTimeoutRef.current = window.setTimeout(() => {
      if (!isPinned && !isVisible) {
        onToggleVisibility();
      }
      setIsHovering(true);
    }, 300); // 300ms delay before showing
  };

  const handleMouseLeave = () => {
    if (hoverTimeoutRef.current) {
      window.clearTimeout(hoverTimeoutRef.current);
    }
    hoverTimeoutRef.current = window.setTimeout(() => {
      if (!isPinned && isVisible) {
        onToggleVisibility();
      }
      setIsHovering(false);
    }, 500); // 500ms delay before hiding
  };

  // Parse width values to numbers for calculations
  const parseWidth = (widthStr: string): number => {
    if (widthStr.endsWith('px')) {
      return parseInt(widthStr, 10);
    }
    if (widthStr.endsWith('%')) {
      const percentage = parseInt(widthStr, 10);
      return percentage / 100 * (panelRef.current?.parentElement?.clientWidth || 0);
    }
    return parseInt(widthStr, 10) || 300; // Default to 300px if parsing fails
  };

  // Convert numeric width to CSS value
  const formatWidth = (widthNum: number): string => {
    return `${widthNum}px`;
  };

  // Handle resize start
  const handleResizeStart = (e: React.MouseEvent) => {
    e.preventDefault();
    resizingRef.current = true;
    startXRef.current = e.clientX;
    startWidthRef.current = parseWidth(currentWidth);

    document.body.style.cursor = 'col-resize';
    document.addEventListener('mousemove', handleResizeMove);
    document.addEventListener('mouseup', handleResizeEnd);
  };

  // Handle resize move
  const handleResizeMove = (e: MouseEvent) => {
    if (!resizingRef.current) return;

    const deltaX = position === 'left' ? (e.clientX - startXRef.current) : (startXRef.current - e.clientX);
    const newWidth = startWidthRef.current + deltaX;

    const minWidthPx = parseWidth(minWidth);
    const maxWidthPx = parseWidth(maxWidth);

    const clampedWidth = Math.max(minWidthPx, Math.min(maxWidthPx, newWidth));

    setCurrentWidth(formatWidth(clampedWidth));
    if (onResize) {
      onResize(clampedWidth);
    }
  };

  // Handle resize end
  const handleResizeEnd = () => {
    resizingRef.current = false;
    document.body.style.cursor = '';
    document.removeEventListener('mousemove', handleResizeMove);
    document.removeEventListener('mouseup', handleResizeEnd);
  };

  // Clean up timeout and event listeners on unmount
  useEffect(() => {
    return () => {
      if (hoverTimeoutRef.current) {
        window.clearTimeout(hoverTimeoutRef.current);
      }
      document.removeEventListener('mousemove', handleResizeMove);
      document.removeEventListener('mouseup', handleResizeEnd);
    };
  }, []);

  // Handle click outside to close panel if not pinned
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        panelRef.current &&
        !panelRef.current.contains(event.target as Node) &&
        isVisible &&
        !isPinned
      ) {
        onToggleVisibility();
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isVisible, isPinned, onToggleVisibility]);

  // Determine position-specific styles
  const positionStyles = position === 'left'
    ? 'left-0 border-r border-gray-200 dark:border-gray-700'
    : 'right-0 border-l border-gray-200 dark:border-gray-700';

  // Tab styles for collapsed state
  const tabStyles = position === 'left'
    ? 'right-[-40px] rounded-r-md'
    : 'left-[-40px] rounded-l-md';

  return (
    <>
      {/* Collapsed tab that's always visible */}
      {!isVisible && (
        <button
          onClick={onToggleVisibility}
          className={`absolute top-20 w-12 h-48 bg-gray-100 dark:bg-gray-800
                     flex items-center justify-center hover:bg-gray-200
                     dark:hover:bg-gray-700 transition-colors z-10 shadow-lg rounded-md ${tabStyles}`}
          aria-label={`Expand ${title} panel`}
        >
          <span className="transform -rotate-90 whitespace-nowrap text-sm font-medium text-gray-600 dark:text-gray-300 flex items-center">
            {position === 'left' ? (
              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1 transform rotate-90" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M10.293 15.707a1 1 0 010-1.414L14.586 10l-4.293-4.293a1 1 0 111.414-1.414l5 5a1 1 0 010 1.414l-5 5a1 1 0 01-1.414 0z" clipRule="evenodd" />
              </svg>
            ) : (
              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 mr-1 transform -rotate-90" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M10.293 15.707a1 1 0 010-1.414L14.586 10l-4.293-4.293a1 1 0 111.414-1.414l5 5a1 1 0 010 1.414l-5 5a1 1 0 01-1.414 0z" clipRule="evenodd" />
              </svg>
            )}
            {title}
          </span>
        </button>
      )}

      {/* Main panel */}
      <div
        ref={panelRef}
        className={`h-full ${backgroundColor}
                   overflow-hidden flex flex-col transition-all duration-300 ease-in-out
                   shadow-lg rounded-lg ${positionStyles} ${className}`}
        style={{
          width: isVisible ? currentWidth : (showPartialWhenCollapsed ? partialWidth : '0'),
          minWidth: isVisible ? minWidth : (showPartialWhenCollapsed ? partialWidth : '0'),
          maxWidth: isVisible ? maxWidth : (showPartialWhenCollapsed ? partialWidth : '0'),
        }}
        onMouseEnter={handleMouseEnter}
        onMouseLeave={handleMouseLeave}
      >
        {!isVisible && showPartialWhenCollapsed && (
          <div className="h-full flex flex-col items-center justify-start pt-6 overflow-hidden bg-gradient-to-b from-gray-50 to-gray-100 dark:from-gray-800 dark:to-gray-850 rounded-lg">
            <button
              onClick={onToggleVisibility}
              className="p-2 rounded-full bg-white dark:bg-gray-700 hover:bg-gray-100 dark:hover:bg-gray-600 transition-colors mb-4 shadow-md"
              aria-label={`Expand ${title} panel`}
              title={`Expand ${title} panel`}
            >
              {position === 'left' ? (
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-primary dark:text-primary-light" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M10.293 15.707a1 1 0 010-1.414L14.586 10l-4.293-4.293a1 1 0 111.414-1.414l5 5a1 1 0 010 1.414l-5 5a1 1 0 01-1.414 0z" clipRule="evenodd" />
                </svg>
              ) : (
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-primary dark:text-primary-light" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M15.707 15.707a1 1 0 01-1.414 0l-5-5a1 1 0 010-1.414l5-5a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 010 1.414z" clipRule="evenodd" />
                </svg>
              )}
            </button>
            <div className="writing-vertical text-xs font-medium text-gray-600 dark:text-gray-300 transform rotate-180 tracking-wide">
              {title}
            </div>
          </div>
        )}
        {isVisible && (
          <>
            <div className="flex justify-between items-center p-4 border-b border-gray-200 dark:border-gray-700 bg-gradient-to-r from-gray-50 to-gray-100 dark:from-gray-800 dark:to-gray-850 rounded-t-lg">
              <h3 className="text-lg font-semibold text-primary-dark dark:text-primary-light flex items-center">
                {position === 'left' ? (
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                    <path d="M2 5a2 2 0 012-2h7a2 2 0 012 2v4a2 2 0 01-2 2H9l-3 3v-3H4a2 2 0 01-2-2V5z" />
                    <path d="M15 7v2a4 4 0 01-4 4H9.828l-1.766 1.767c.28.149.599.233.938.233h2l3 3v-3h2a2 2 0 002-2V9a2 2 0 00-2-2h-1z" />
                  </svg>
                ) : (
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M11.49 3.17c-.38-1.56-2.6-1.56-2.98 0a1.532 1.532 0 01-2.286.948c-1.372-.836-2.942.734-2.106 2.106.54.886.061 2.042-.947 2.287-1.561.379-1.561 2.6 0 2.978a1.532 1.532 0 01.947 2.287c-.836 1.372.734 2.942 2.106 2.106a1.532 1.532 0 012.287.947c.379 1.561 2.6 1.561 2.978 0a1.533 1.533 0 012.287-.947c1.372.836 2.942-.734 2.106-2.106a1.533 1.533 0 01.947-2.287c1.561-.379 1.561-2.6 0-2.978a1.532 1.532 0 01-.947-2.287c.836-1.372-.734-2.942-2.106-2.106a1.532 1.532 0 01-2.287-.947zM10 13a3 3 0 100-6 3 3 0 000 6z" clipRule="evenodd" />
                  </svg>
                )}
                {title}
              </h3>
              <div className="flex space-x-2">
                <button
                  onClick={onTogglePin}
                  className={`p-1.5 rounded-md transition-colors shadow-sm
                            ${isPinned
                              ? 'bg-primary-light/20 text-primary-dark dark:text-primary-light'
                              : 'text-gray-500 dark:text-gray-400 hover:bg-gray-200 dark:hover:bg-gray-700 bg-white dark:bg-gray-700'}`}
                  aria-label={isPinned ? 'Unpin panel' : 'Pin panel'}
                  title={isPinned ? 'Unpin panel' : 'Pin panel'}
                >
                  {/* Pin/Unpin Icon */}
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                    <path d="M5 5a2 2 0 012-2h6a2 2 0 012 2v2a2 2 0 01-2 2H7a2 2 0 01-2-2V5z" />
                    <path d="M5 13a2 2 0 012-2h6a2 2 0 012 2v2a2 2 0 01-2 2H7a2 2 0 01-2-2v-2z" />
                  </svg>
                </button>
                <button
                  onClick={onToggleVisibility}
                  className="p-1.5 rounded-md text-gray-500 dark:text-gray-400 hover:bg-gray-200 dark:hover:bg-gray-700 transition-colors shadow-sm bg-white dark:bg-gray-700"
                  aria-label={`Close ${title} panel`}
                  title={`Close ${title} panel`}
                >
                  {/* Close Icon */}
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                  </svg>
                </button>
              </div>
            </div>
            <div className="flex-grow overflow-y-auto p-5">
              {children}
            </div>

            {/* Resize handle */}
            <div
              className={`absolute top-0 ${position === 'left' ? 'right-0' : 'left-0'} w-1 h-full cursor-col-resize group z-10`}
              onMouseDown={handleResizeStart}
            >
              <div
                className="absolute top-0 bottom-0 w-4 bg-transparent hover:bg-primary-light/20 transition-colors flex items-center justify-center"
                style={{ [position === 'left' ? 'right' : 'left']: '-8px' }}
              >
                <div className="h-16 w-1 rounded-full bg-gray-300 dark:bg-gray-600 group-hover:bg-primary-light group-hover:dark:bg-primary transition-colors opacity-50 group-hover:opacity-100"></div>
              </div>
            </div>
          </>
        )}
      </div>
    </>
  );
};

export default CollapsiblePanel;
