import express from 'express';
import {
  createProfile,
  getProfiles,
  getProfileById,
  updateProfile,
  deleteProfile,
} from '../controllers/configController';
import { protect } from '../middleware/authMiddleware'; // Import protect middleware

const router = express.Router();

// Route to get all profiles (names/ids) and create a new profile for the logged-in user
router
  .route('/profiles')
  .get(protect, getProfiles) // Apply protect middleware
  .post(protect, createProfile); // Apply protect middleware

// Route to get, update, or delete a specific profile by ID for the logged-in user
router
  .route('/profiles/:id')
  .get(protect, getProfileById) // Apply protect middleware
  .put(protect, updateProfile) // Apply protect middleware
  .delete(protect, deleteProfile); // Apply protect middleware


export default router;
