import React from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { useConfigStore } from '../store/configStore';

interface ProtectedRouteProps {
  children: React.ReactNode;
}

const ProtectedRoute: React.FC<ProtectedRouteProps> = ({ children }) => {
  const isAuthenticated = useConfigStore((state) => state.isAuthenticated);
  // const authLoading = useConfigStore((state) => state.authLoading); // Check loading state - Removed as unused
  const location = useLocation();

  // Optional: Show a loading indicator while checking auth status initially
  // if (authLoading) {
  //   return <div>Loading...</div>; // Or a spinner component
  // }

  if (!isAuthenticated) {
    // Redirect them to the /login page, but save the current location they were
    // trying to go to when they were redirected. This allows us to send them
    // along to that page after they login, which is a nicer user experience
    // than dropping them off on the home page.
    return <Navigate to="/login" state={{ from: location }} replace />;
  }

  return <>{children}</>; // Render the children if authenticated
};

export default ProtectedRoute;
