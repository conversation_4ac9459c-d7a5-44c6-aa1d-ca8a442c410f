// ragHandler.ts - Core RAG processing logic

import fs from 'fs/promises';
import path from 'path';
// Import types only from transformers - we'll use dynamic import for the actual module
import type { PipelineType, FeatureExtractionPipeline } from '@xenova/transformers';
// Create a placeholder for env configuration that will be populated when the module is loaded
let transformersEnv: any = null;
import { Index } from 'faiss-node';
import pdf from 'pdf-parse';
import mammoth from 'mammoth';
import * as XLSX from 'xlsx';
import { parse as parseCsv } from 'csv-parse/sync';
import { parse as parseHtml } from 'node-html-parser';
import * as msgpack from 'msgpack-lite';
import Database from 'better-sqlite3';
import { RagError } from '../utils/errors'; // Assuming RagError is defined here

// --- Configuration ---
// We'll configure the env when the module is loaded dynamically
// Cache directory will be set relative to this file's directory for predictability

// --- Constants ---
const DEFAULT_EMBEDDING_MODEL: string = 'Xenova/all-MiniLM-L6-v2'; // Faster default, good quality
const DEFAULT_CHUNK_SIZE: number = 512; // Tokens/Chars, adjust based on model limits/preference
const DEFAULT_CHUNK_OVERLAP: number = 64;

// Model dimension mapping - each model produces vectors of a specific size
const MODEL_DIMENSIONS: Record<string, number> = {
    'Xenova/all-MiniLM-L6-v2': 384,
    'Xenova/all-mpnet-base-v2': 768,
    'Xenova/bge-large-en-v1.5': 1024,
    'Xenova/e5-large-v2': 1024,
    'Xenova/paraphrase-multilingual-MiniLM-L12-v2': 384,
    'Xenova/multi-qa-mpnet-base-dot-v1': 768
};

const DEFAULT_DIMENSION: number = MODEL_DIMENSIONS[DEFAULT_EMBEDDING_MODEL] || 384; // Default dimension based on model
const INDEX_FILE_NAME: string = 'faiss_index.bin'; // More standard extension
const METADATA_FILE_NAME: string = 'index_metadata.json'; // Keep JSON for backward compatibility
const METADATA_DB_FILE_NAME: string = 'metadata.sqlite'; // SQLite database for metadata
const EMBEDDINGS_CACHE_FILE_NAME: string = 'embeddings_cache.bin'; // Binary format for embeddings cache

// Define the absolute root path where knowledge bases are allowed to exist.
const ALLOWED_BASE_PATH = path.resolve(__dirname, '../..'); // Example: Project root, adjust as needed
const KNOWLEDGE_BASE_ROOT = path.resolve(ALLOWED_BASE_PATH, './knowledge_bases'); // Default KB location

/**
 * Resolves a user-provided path relative to the KNOWLEDGE_BASE_ROOT
 * and validates that it stays within the ALLOWED_BASE_PATH.
 * Throws an error if validation fails.
 * @param userPath - The path provided by the user.
 * @param checkExists - If true, checks if the resolved path exists.
 * @returns The absolute, validated path.
 */
const resolveAndValidatePath = async (userPath: string, checkExists: boolean = false): Promise<string> => {
    if (!userPath || typeof userPath !== 'string') {
        throw new Error('Invalid path provided.');
    }

    // Resolve the path relative to the KNOWLEDGE_BASE_ROOT. Handles ".", "..", etc.
    // This assumes user paths like 'my_kb' or 'group/my_kb' should be inside KNOWLEDGE_BASE_ROOT.
    const resolvedPath = path.resolve(KNOWLEDGE_BASE_ROOT, userPath);

    // SECURITY CHECK: Ensure the resolved path is still within the allowed base path.
    // This prevents traversal attacks like "../../../etc/passwd".
    if (!resolvedPath.startsWith(ALLOWED_BASE_PATH)) {
        console.warn(`Path traversal attempt blocked: UserPath='${userPath}', Resolved='${resolvedPath}', Base='${ALLOWED_BASE_PATH}'`);
        throw new Error('Access denied: Path is outside the allowed directory.');
    }

    // Optionally check if the path exists
    if (checkExists) {
        try {
            await fs.access(resolvedPath);
        } catch (error: any) {
            throw new Error(`Path '${userPath}' is not accessible: ${error.message}`);
        }
    }

    return resolvedPath;
};

// --- Interfaces ---
interface DocumentMetadata {
    source: string; // Relative path from KB root or unique identifier
    chunkIndex: number;
    createdAt: string; // ISO timestamp
    // Future: page number, section, etc.
}

interface DocumentChunk {
    content: string;
    metadata: DocumentMetadata;
}

export interface RetrievedChunk extends DocumentChunk { // Export interface
    similarity: number; // Similarity score (higher is better, normalized to 0-1)
    l2Distance?: number; // Original L2 distance from FAISS (lower is better)
}

interface RagHandlerOptions {
    initialKnowledgeBase?: string; // Relative path within KNOWLEDGE_BASE_ROOT
    embeddingModel?: string;
    chunkSize?: number;
    chunkOverlap?: number;
    dimension?: number; // Should match the embedding model
    chunkingStrategy?: string; // 'semantic' or 'fixed'
    retrievalNResults?: number; // Number of results to return from retrieval
    retrievalThreshold?: number; // Similarity threshold for retrieval
    useReranking?: boolean; // Whether to use reranking
    useQueryExpansion?: boolean; // Whether to use query expansion
    // storageType is managed internally based on path resolution now
}

// --- RAG Handler Class ---

export class RagHandler {
    private persistDirectory: string; // Absolute path to the current KB directory
    private storageType: 'local' | 'server'; // Determined by persistDirectory location
    private embeddingModelName: string;
    private chunkSize: number;
    private chunkOverlap: number;
    private dimension: number;
    private chunkingStrategy: string = 'semantic'; // Default to semantic chunking
    private retrievalNResults: number = 10; // Default to 5 results
    private retrievalThreshold: number = 0.1; // Default similarity threshold
    private useReranking: boolean = false; // Default to no reranking
    private useQueryExpansion: boolean = false; // Default to no query expansion

    private index: Index | null = null; // FAISS index
    private documents: DocumentChunk[] = []; // In-memory store of chunks
    private embedder: FeatureExtractionPipeline | null = null; // Specific pipeline type
    private isInitialized: boolean = false;
    private isInitializing: boolean = false; // Prevent concurrent initialization
    private _needsReindexing: boolean = false; // Flag to indicate if reindexing is needed

    private indexFilePath: string = ''; // Set during path update
    private metadataFilePath: string = ''; // Set during path update
    private metadataDbFilePath: string = ''; // SQLite database path
    private embeddingsCacheFilePath: string = ''; // Set during path update
    private embeddingsCache: Record<string, number[]> = {}; // Cache key: hash(content), value: embedding
    private db: Database.Database | null = null; // SQLite database instance

    constructor(options: RagHandlerOptions = {}) {
        // Ensure the model name has the 'Xenova/' prefix if it doesn't already
        let modelName = options.embeddingModel || DEFAULT_EMBEDDING_MODEL;
        if (!modelName.startsWith('Xenova/') && !modelName.includes('/')) {
            modelName = `Xenova/${modelName}`;
        }
        this.embeddingModelName = modelName;

        this.chunkSize = options.chunkSize || DEFAULT_CHUNK_SIZE;
        this.chunkOverlap = options.chunkOverlap || DEFAULT_CHUNK_OVERLAP;
        this.dimension = options.dimension || DEFAULT_DIMENSION;
        this.chunkingStrategy = options.chunkingStrategy || 'semantic';
        this.retrievalNResults = options.retrievalNResults || 10;
        this.retrievalThreshold = options.retrievalThreshold || 0.1;
        this.useReranking = options.useReranking || false;
        this.useQueryExpansion = options.useQueryExpansion || false;

        // Determine initial persist directory (absolute path)
        const initialKbRelative = options.initialKnowledgeBase || 'default_kb'; // Default KB name
        this.persistDirectory = path.resolve(KNOWLEDGE_BASE_ROOT, initialKbRelative); // Always resolve within root initially

        // Determine initial storage type based on resolved path
        this.storageType = this.persistDirectory.startsWith(KNOWLEDGE_BASE_ROOT) ? 'server' : 'local';

        this._updateFilePaths(); // Set initial file paths

        // Start initialization process asynchronously
        this._initialize().catch(err => {
            console.error("Initialization failed:", err instanceof Error ? err.message : String(err));
            // State remains uninitialized
        });
    }

    /** Updates internal file paths based on the current persistDirectory. */
    private _updateFilePaths(): void {
        this.indexFilePath = path.join(this.persistDirectory, INDEX_FILE_NAME);
        this.metadataFilePath = path.join(this.persistDirectory, METADATA_FILE_NAME);
        this.metadataDbFilePath = path.join(this.persistDirectory, METADATA_DB_FILE_NAME);
        this.embeddingsCacheFilePath = path.join(this.persistDirectory, EMBEDDINGS_CACHE_FILE_NAME);
    }

    /**
     * Ensures the handler is initialized before proceeding.
     * Attempts to initialize if not already initialized.
     * Throws RagError if initialization fails.
     */
    private async _ensureInitialized(): Promise<void> {
        // If already initialized, return immediately
        if (this.isInitialized) return;

        // If initialization is in progress, wait for it to complete
        if (this.isInitializing) {
            console.log("Waiting for ongoing initialization...");
            await new Promise<void>((resolve, reject) => {
                const interval = setInterval(() => {
                    if (this.isInitialized) { clearInterval(interval); resolve(); }
                    if (!this.isInitializing) { clearInterval(interval); reject(new RagError("Initialization failed.", "INIT_FAILED")); }
                }, 100);

                // Set a timeout to prevent infinite waiting
                setTimeout(() => {
                    clearInterval(interval);
                    reject(new RagError("Initialization timed out.", "INIT_TIMEOUT"));
                }, 30000); // 30 second timeout
            });

            // Double check after waiting
            if (this.isInitialized) return;
        }

        // If not initialized and not initializing, attempt to initialize
        try {
            console.log("RAG Handler not initialized. Attempting to initialize now...");
            await this._initialize();
            if (this.isInitialized) {
                console.log("Successfully initialized RAG Handler.");
                return;
            }
        } catch (error) {
            const message = error instanceof Error ? error.message : String(error);
            console.error("Failed to initialize RAG Handler:", message);
            throw new RagError(`RAG Handler initialization failed: ${message}`, "INIT_FAILED", error);
        }

        // If we get here, initialization was attempted but failed
        throw new RagError("RAG Handler is not initialized and initialization attempt failed.", "NOT_INITIALIZED");
    }

    /** Main initialization logic */
    private async _initialize(): Promise<void> {
        if (this.isInitialized || this.isInitializing) return;
        this.isInitializing = true;
        console.log(`Initializing RAG Handler for directory: ${this.persistDirectory}`);

        try {
            // Ensure root KB directory exists
            await fs.mkdir(KNOWLEDGE_BASE_ROOT, { recursive: true });
            // Ensure current persist directory exists
            await fs.mkdir(this.persistDirectory, { recursive: true });

            // Run embedder and index initialization concurrently
            await Promise.all([
                this._initializeEmbedder(),
                this._initializeIndexAndMetadata() // Combined loading logic
            ]);

            this.isInitialized = true;
            console.log(`RagHandler initialized successfully for ${this.persistDirectory}.`);

            // Check if we need to rebuild the index after initialization
            if (this._needsReindexing) {
                console.log("Rebuilding index after initialization due to dimension mismatch...");
                // Rebuild the index with existing documents
                await this._rebuildIndexWithExistingDocuments();
                this._needsReindexing = false;
            }

        } catch (err) {
            console.error(`Failed during RagHandler initialization for ${this.persistDirectory}:`, err);
            // Don't set isInitialized = true
            throw err; // Re-throw to signal failure
        } finally {
            this.isInitializing = false;
        }
    }

    /** Initializes the embedding model. */
    private async _initializeEmbedder(): Promise<void> {
        if (this.embedder) return; // Avoid re-init
        try {
            console.log(`Initializing embedding model: ${this.embeddingModelName}...`);

            // Dynamically import the transformers module
            const transformers = await import('@xenova/transformers');

            // Configure the environment
            transformersEnv = transformers.env;
            transformersEnv.allowLocalModels = true;
            transformersEnv.cacheDir = path.resolve(__dirname, '../../cache');

            // Use explicit type casting to handle the pipeline return type
            const embeddingPipeline = await transformers.pipeline('feature-extraction' as PipelineType, this.embeddingModelName, {
                quantized: true // Use quantized version for efficiency if available
            });

            // Cast the pipeline to the expected type
            this.embedder = embeddingPipeline as FeatureExtractionPipeline;
            console.log(`Embedding model ${this.embeddingModelName} initialized.`);
        } catch (error) {
            const message = error instanceof Error ? error.message : String(error);
            throw new RagError(`Failed to initialize embedding model: ${message}`, 'EMBEDDER_INIT_FAILED', error);
        }
    }

    /** Loads or initializes the FAISS index, metadata, and embeddings cache. */
    private async _initializeIndexAndMetadata(): Promise<void> {
        if (this.index) return; // Avoid re-init
        try {
            await fs.access(this.indexFilePath);
            await fs.access(this.metadataFilePath);

            console.log(`Loading existing FAISS index from ${this.indexFilePath}...`);
            this.index = Index.read(this.indexFilePath);

            console.log(`Loading metadata from ${this.metadataFilePath}...`);
            const metadataData = await fs.readFile(this.metadataFilePath, 'utf-8');
            const metadata = JSON.parse(metadataData);

            this.documents = metadata.chunks || [];
            const loadedDimension = metadata.dimension || null;
            const loadedModel = metadata.embedding_model || null;

            // Validation checks
            if (loadedDimension && loadedDimension !== this.dimension) {
                console.warn(`Dimension mismatch! Index: ${loadedDimension}, Config: ${this.dimension}. Re-initializing index.`);
                throw new Error('Dimension mismatch'); // Trigger re-initialization
            }
            if (loadedModel && loadedModel !== this.embeddingModelName) {
                console.warn(`Model mismatch! Index: ${loadedModel}, Config: ${this.embeddingModelName}. Re-initializing index.`);
                throw new Error('Model mismatch'); // Trigger re-initialization
            }

            // Additional check: verify the actual index dimension matches our expected dimension
            this._verifyIndexDimension();

            // If the index was recreated due to dimension mismatch, we need to rebuild it
            if (this.index && this.index.ntotal() === 0 && this.documents.length > 0) {
                console.log("Index was recreated due to dimension mismatch. Rebuilding index with existing documents...");
                // We'll rebuild the index with the documents we have, but we can't call reindexKnowledgeBase here
                // as it would cause a circular dependency. Instead, we'll flag it for later rebuilding.
                this._needsReindexing = true;
            }

            // Load embeddings cache
            try {
                await fs.access(this.embeddingsCacheFilePath);
                const fileExtension = path.extname(this.embeddingsCacheFilePath);

                if (fileExtension === '.bin') {
                    // Load binary format (MessagePack)
                    const binaryData = await fs.readFile(this.embeddingsCacheFilePath);
                    this.embeddingsCache = msgpack.decode(binaryData);
                    console.log(`Loaded binary embeddings cache with ${Object.keys(this.embeddingsCache).length} entries.`);
                } else {
                    // Fallback to JSON for backward compatibility
                    const cacheData = await fs.readFile(this.embeddingsCacheFilePath, 'utf-8');
                    this.embeddingsCache = JSON.parse(cacheData);
                    console.log(`Loaded JSON embeddings cache with ${Object.keys(this.embeddingsCache).length} entries.`);
                }
            } catch (error) {
                console.log('Embeddings cache not found or invalid. Starting fresh.', error instanceof Error ? error.message : '');
                this.embeddingsCache = {};
            }

            // Initialize SQLite database
            try {
                // Close existing connection if any
                if (this.db) {
                    this.db.close();
                    this.db = null;
                }

                // Initialize SQLite database
                this.db = new Database(this.metadataDbFilePath);

                // Check if the chunks table exists
                const tableExists = this.db.prepare("SELECT name FROM sqlite_master WHERE type='table' AND name='chunks'").get();

                if (!tableExists) {
                    // Create the chunks table if it doesn't exist
                    this.db.exec(`
                        CREATE TABLE chunks (
                            id INTEGER PRIMARY KEY,
                            content TEXT NOT NULL,
                            source TEXT NOT NULL,
                            chunk_index INTEGER NOT NULL,
                            created_at TEXT NOT NULL,
                            metadata TEXT
                        )
                    `);

                    // Import existing documents into SQLite
                    if (this.documents.length > 0) {
                        console.log(`Importing ${this.documents.length} existing document chunks into SQLite...`);
                        const insert = this.db.prepare('INSERT INTO chunks (content, source, chunk_index, created_at, metadata) VALUES (?, ?, ?, ?, ?)');

                        // Begin a transaction for faster inserts
                        const insertMany = this.db.transaction((chunks) => {
                            for (const chunk of chunks) {
                                const { content, metadata } = chunk;
                                const { source, chunkIndex, createdAt, ...otherMetadata } = metadata;
                                insert.run(
                                    content,
                                    source,
                                    chunkIndex,
                                    createdAt,
                                    JSON.stringify(otherMetadata)
                                );
                            }
                        });

                        insertMany(this.documents);
                        console.log(`Imported ${this.documents.length} document chunks into SQLite.`);
                    }
                } else {
                    // Count the number of chunks in the database
                    const countResult = this.db.prepare('SELECT COUNT(*) as count FROM chunks').get() as { count: number };
                    console.log(`SQLite database contains ${countResult.count} document chunks.`);
                }
            } catch (error) {
                console.error('Error initializing SQLite database:', error);
                // Continue without SQLite - we'll still have the JSON metadata as fallback
            }

            console.log(`FAISS index (${this.index.ntotal()} vectors) and ${this.documents.length} document chunks loaded.`);

        } catch (error) {
            console.log('Existing index/metadata not found or invalid. Initializing new index.', (error as Error).message);
            this.index = new Index(this.dimension); // Use FlatL2 index by default
            this.documents = [];
            this.embeddingsCache = {};
            console.log(`New FAISS index initialized with dimension ${this.dimension}.`);
            // No need to save here, will be saved after first add operation
        }
    }

    /** Saves the current index, metadata, and embeddings cache. */
    private async _saveIndex(): Promise<void> {
        await this._ensureInitialized(); // Ensure ready before saving
        if (!this.index) { // Should not happen if _ensureInitialized works, but belt-and-suspenders
             console.error("Cannot save: FAISS index is null.");
             return;
        }

        try {
            // Create directory if it somehow disappeared
             await fs.mkdir(this.persistDirectory, { recursive: true });

            // Save FAISS index
            console.log(`Saving FAISS index (${this.index.ntotal()} vectors) to ${this.indexFilePath}...`);
            this.index.write(this.indexFilePath);

            // Save metadata to JSON (for backward compatibility)
            console.log(`Saving ${this.documents.length} document chunks to ${this.metadataFilePath}...`);
            const metadata = {
                chunks: this.documents,
                dimension: this.dimension,
                embedding_model: this.embeddingModelName,
                version: '1.1', // Increment version if format changes
                createdAt: new Date().toISOString(),
            };
            await fs.writeFile(this.metadataFilePath, JSON.stringify(metadata, null, 2), 'utf-8');

            // Save metadata to SQLite
            if (this.db) {
                try {
                    console.log(`Saving ${this.documents.length} document chunks to SQLite database...`);

                    // Clear existing data
                    this.db.exec('DELETE FROM chunks');

                    // Insert new data
                    const insert = this.db.prepare('INSERT INTO chunks (content, source, chunk_index, created_at, metadata) VALUES (?, ?, ?, ?, ?)');

                    // Begin a transaction for faster inserts
                    const insertMany = this.db.transaction((chunks) => {
                        for (const chunk of chunks) {
                            const { content, metadata } = chunk;
                            const { source, chunkIndex, createdAt, ...otherMetadata } = metadata;
                            insert.run(
                                content,
                                source,
                                chunkIndex,
                                createdAt,
                                JSON.stringify(otherMetadata)
                            );
                        }
                    });

                    insertMany(this.documents);
                    console.log(`Saved ${this.documents.length} document chunks to SQLite database.`);
                } catch (error) {
                    console.error('Error saving to SQLite database:', error);
                    // Continue with JSON metadata as fallback
                }
            }

            // Save embeddings cache
            console.log(`Saving embeddings cache with ${Object.keys(this.embeddingsCache).length} entries to ${this.embeddingsCacheFilePath}...`);

            const fileExtension = path.extname(this.embeddingsCacheFilePath);
            if (fileExtension === '.bin') {
                // Save in binary format (MessagePack)
                const binaryData = msgpack.encode(this.embeddingsCache);
                await fs.writeFile(this.embeddingsCacheFilePath, binaryData);
            } else {
                // Fallback to JSON for backward compatibility
                await fs.writeFile(this.embeddingsCacheFilePath, JSON.stringify(this.embeddingsCache), 'utf-8');
            }

            console.log("Index, metadata, and cache saved successfully.");
        } catch (error) {
            console.error(`Error saving index/metadata/cache to ${this.persistDirectory}:`, error);
            // Decide if this should throw - probably not, but log it prominently
            // const errorMessage = error instanceof Error ? error.message : String(error);
            // throw new RagError(`Failed to save index data: ${errorMessage}`, 'SAVE_FAILED', error);
        }
    }

    // --- File Processing & Indexing ---

    /**
     * Adds a single file (given its absolute path) to the knowledge base.
     * Parses, chunks, embeds, and indexes the file content.
     * Determines the relative source path based on the current KB directory.
     * @param absoluteFilePath The absolute path to the file.
     */
    async addFile(absoluteFilePath: string): Promise<void> {
        await this._ensureInitialized();
        if (!this.embedder || !this.index) throw new RagError("Handler components missing.", "INTERNAL_ERROR");

        // Determine the source path relative to the current knowledge base directory
        const relativeSourcePath = path.relative(this.persistDirectory, absoluteFilePath).replace(/\\/g, '/');
        console.log(`Processing file: ${absoluteFilePath} (Source: ${relativeSourcePath})`);

        try {
            // Delete existing chunks for this source *before* processing the new version
            await this.deleteFile(relativeSourcePath, false); // Don't save index yet

            const fileContent = await this._parseFile(absoluteFilePath);
            if (!fileContent) {
                console.warn(`Could not parse or empty content for file: ${absoluteFilePath}`);
                return; // Skip empty or unparsable files
            }

            const chunks = this._chunkTextRecursive(fileContent, relativeSourcePath);
            if (!chunks || chunks.length === 0) {
                 console.warn(`No chunks generated for file: ${absoluteFilePath}`);
                 return;
            }

            console.log(`Generating embeddings for ${chunks.length} chunks from ${relativeSourcePath}...`);
            const embeddings = await this._generateEmbeddings(chunks.map(c => c.content));

            if (!embeddings || embeddings.length !== chunks.length) {
                 throw new RagError(`Embedding generation failed or mismatch for ${relativeSourcePath}`, 'EMBEDDING_FAILED');
            }

            // Store chunks and add embeddings to index
            const startIndex = this.documents.length; // FAISS index ID will correspond to this
            this.documents.push(...chunks);
            const flatEmbeddings = embeddings.reduce((acc, val) => acc.concat(Array.from(val)), [] as number[]);
            this.index.add(flatEmbeddings); // Add flattened vectors

            // Update chunk metadata with correct indices (relative to the start of this batch)
            for(let i = 0; i < chunks.length; i++) {
                chunks[i].metadata.chunkIndex = startIndex + i; // Use the actual index in the documents array
            }

            // Add chunks to SQLite database if available
            if (this.db) {
                try {
                    console.log(`Adding ${chunks.length} chunks to SQLite database...`);

                    // Insert new chunks
                    const insert = this.db.prepare('INSERT INTO chunks (content, source, chunk_index, created_at, metadata) VALUES (?, ?, ?, ?, ?)');

                    // Begin a transaction for faster inserts
                    const insertMany = this.db.transaction((newChunks) => {
                        for (const chunk of newChunks) {
                            const { content, metadata } = chunk;
                            const { source, chunkIndex, createdAt, ...otherMetadata } = metadata;
                            insert.run(
                                content,
                                source,
                                chunkIndex,
                                createdAt,
                                JSON.stringify(otherMetadata)
                            );
                        }
                    });

                    insertMany(chunks);
                    console.log(`Added ${chunks.length} chunks to SQLite database.`);
                } catch (error) {
                    console.error('Error adding chunks to SQLite database:', error);
                    // Continue without SQLite - we'll still have the JSON metadata as fallback
                }
            }

            console.log(`Added ${chunks.length} chunks (Indices ${startIndex}-${startIndex + chunks.length - 1}) from ${relativeSourcePath}. Index size: ${this.index.ntotal()}`);

            // Save index needed after adding new data
            await this._saveIndex();

        } catch (error) {
            if (error instanceof RagError) throw error; // Re-throw specific RagErrors
            const message = error instanceof Error ? error.message : String(error);
            console.error(`Failed to process file ${relativeSourcePath}:`, error);
            throw new RagError(`Failed to process file ${relativeSourcePath}: ${message}`, 'ADD_FILE_FAILED', error);
        }
    }

    /**
     * Deletes a file and its associated vectors from the knowledge base.
     * Uses the "rebuild index" strategy for safety.
     * @param relativeSourcePath The source path relative to the current KB directory.
     * @param saveAfterDelete Whether to save the index immediately after deletion.
     * @returns True if deletion occurred, false if source not found. Throws on error.
     */
    async deleteFile(relativeSourcePath: string, saveAfterDelete: boolean = true): Promise<boolean> {
        await this._ensureInitialized();
        if (!this.index) throw new RagError("Index not available for deletion.", "INTERNAL_ERROR");

        console.log(`Attempting to delete file source: ${relativeSourcePath}`);

        const initialDocCount = this.documents.length;
        const docsToKeep = this.documents.filter(doc => doc.metadata.source !== relativeSourcePath);
        const numToDelete = initialDocCount - docsToKeep.length;

        if (numToDelete === 0) {
            console.log(`File source "${relativeSourcePath}" not found in documents.`);
            return false; // Source not found
        }

        console.log(`Found ${numToDelete} chunks to delete for source "${relativeSourcePath}".`);
        this.documents = docsToKeep; // Update the documents array first

        // Delete from SQLite database if available
        if (this.db) {
            try {
                console.log(`Deleting chunks for source "${relativeSourcePath}" from SQLite database...`);
                const deleteStmt = this.db.prepare('DELETE FROM chunks WHERE source = ?');
                const result = deleteStmt.run(relativeSourcePath);
                console.log(`Deleted ${result.changes} chunks from SQLite database.`);
            } catch (error) {
                console.error('Error deleting from SQLite database:', error);
                // Continue without SQLite - we'll still have the JSON metadata as fallback
            }
        }

        try {
            // Rebuild the index with remaining documents
            console.log("Rebuilding FAISS index after deletion...");
            this.index = new Index(this.dimension); // Create a new index instance

            if (this.documents.length > 0) {
                console.log(`Generating embeddings for remaining ${this.documents.length} chunks...`);
                const remainingEmbeddings = await this._generateEmbeddings(this.documents.map(d => d.content));

                if (!remainingEmbeddings || remainingEmbeddings.length !== this.documents.length) {
                    throw new RagError("Embedding generation failed during index rebuild after deletion.", 'REBUILD_EMBEDDING_FAILED');
                }
                // Add each embedding vector to the index individually
                for (let i = 0; i < remainingEmbeddings.length; i++) {
                    const vector = Array.from(remainingEmbeddings[i]);
                    this.index.add(vector);
                }
                console.log(`Rebuilt index with ${this.index.ntotal()} vectors.`);
            } else {
                console.log("Index is now empty after deletion.");
            }

            // Clean embeddings cache (remove entries for deleted source) - Optional but good practice
             // Note: Cache keys are content hashes, harder to link back to source directly.
             // A full cache rebuild or more sophisticated tracking might be needed for precise cache cleaning.
             // For now, we accept potentially stale cache entries after deletion.

            if (saveAfterDelete) {
                await this._saveIndex();
            }
            return true; // Deletion successful

        } catch (error) {
             // If rebuild fails, the state might be inconsistent. Attempt to restore previous state.
            console.error(`Critical error during index rebuild after deleting "${relativeSourcePath}":`, error);

            // Mark as uninitialized but don't clear documents - we'll try to recover
            this.isInitialized = false;
            this.isInitializing = false;
            this.index = null;

            // Try to reinitialize immediately to recover
            try {
                console.log("Attempting to recover by reinitializing the RAG handler...");
                await this._initialize();
                console.log("Successfully recovered from error.");

                if (saveAfterDelete) {
                    await this._saveIndex();
                }
                return true; // Deletion and recovery successful
            } catch (recoveryError) {
                // If recovery fails, we're in a bad state
                console.error("Failed to recover from error:", recoveryError);
                this.documents = []; // Now clear documents as last resort

                if (error instanceof RagError) throw error;
                const message = error instanceof Error ? error.message : String(error);
                throw new RagError(`Error rebuilding index after deleting "${relativeSourcePath}": ${message}`, 'DELETE_REBUILD_FAILED', error);
            }
        }
    }

    /**
     * Parses the content of a file based on its extension.
     * @param absoluteFilePath The absolute path to the file.
     * @returns The extracted text content or null if parsing fails or type is unsupported.
     */
    private async _parseFile(absoluteFilePath: string): Promise<string | null> {
        const extension = path.extname(absoluteFilePath).toLowerCase();
        const filename = path.basename(absoluteFilePath);
        console.log(`Parsing ${filename} (Type: ${extension})`);
        try {
            const buffer = await fs.readFile(absoluteFilePath);

            switch (extension) {
                // Docs
                case '.pdf':
                    const pdfData = await pdf(buffer);
                    return pdfData.text;
                case '.docx':
                    const docxResult = await mammoth.extractRawText({ buffer });
                    return docxResult.value;
                case '.txt':
                case '.md':
                    return buffer.toString('utf-8');

                // Spreadsheets
                case '.xlsx':
                case '.xls':
                    const workbook = XLSX.read(buffer, { type: 'buffer' });
                    let xlsText = `File: ${filename}\n\n`;
                    workbook.SheetNames.forEach(sheetName => {
                        xlsText += `--- Sheet: ${sheetName} ---\n`;
                        const worksheet = workbook.Sheets[sheetName];
                        const data: any[][] = XLSX.utils.sheet_to_json(worksheet, { header: 1, defval: "" }); // Use defval for empty cells
                        if (data.length > 0) {
                             // Simple row-by-row representation
                             data.forEach((row) => {
                                 xlsText += row.map(cell => `${cell}`).join('\t') + '\n'; // Tab separated
                             });
                        }
                        xlsText += '\n';
                    });
                    return xlsText;
                case '.csv':
                    const csvRecords: any[][] = parseCsv(buffer, { columns: false, skip_empty_lines: true });
                    let csvText = `File: ${filename}\n\n`;
                    csvRecords.forEach((row) => {
                        csvText += row.map(cell => `${cell}`).join(',') + '\n'; // Comma separated
                    });
                    return csvText;

                // Web
                case '.html':
                case '.htm':
                    const htmlContent = buffer.toString('utf-8');
                    const root = parseHtml(htmlContent);
                    // Attempt to get meaningful text, remove script/style
                    root.querySelectorAll('script, style').forEach(el => el.remove());
                    // Add title if present
                    const title = root.querySelector('title')?.text;
                    return `Title: ${title || 'N/A'}\n\n${root.textContent || root.rawText}`.trim();

                // Code & Text-based
                case '.ts': case '.js': case '.py': case '.java': case '.c': case '.cpp':
                case '.cs': case '.php': case '.rb': case '.json': case '.xml':
                case '.yaml': case '.yml': case '.ini': case '.config': case '.sql':
                    return `File: ${filename}\nLanguage: ${extension.substring(1)}\n\n${buffer.toString('utf-8')}`;

                default:
                    console.warn(`Unsupported file type: ${extension} for file ${filename}`);
                    return null;
            }
        } catch (error) {
            const message = error instanceof Error ? error.message : String(error);
            console.error(`Error parsing file ${filename}:`, message);
            return null; // Return null on parsing error
        }
    }

    /**
     * Splits text into chunks based on the selected chunking strategy.
     * @param text The input text.
     * @param relativeSourcePath Identifier for the source document (relative path).
     * @returns An array of DocumentChunk.
     */
    private _chunkTextRecursive(text: string, relativeSourcePath: string): DocumentChunk[] {
        // Use instance properties instead of constants
        const chunkSize = this.chunkSize;
        const chunkOverlap = this.chunkOverlap;
        const timestamp = new Date().toISOString();

        // Choose chunking strategy based on configuration
        if (this.chunkingStrategy === 'fixed') {
            return this._fixedSizeChunking(text, relativeSourcePath, chunkSize, chunkOverlap, timestamp);
        } else {
            // Default to semantic chunking
            return this._semanticChunking(text, relativeSourcePath, chunkSize, chunkOverlap, timestamp);
        }
    }

    /**
     * Splits text into fixed-size chunks with overlap.
     * @param text The input text.
     * @param relativeSourcePath Identifier for the source document.
     * @param chunkSize The size of each chunk.
     * @param chunkOverlap The overlap between chunks.
     * @param timestamp The creation timestamp.
     * @returns An array of DocumentChunk.
     */
    private _fixedSizeChunking(text: string, relativeSourcePath: string, chunkSize: number, chunkOverlap: number, timestamp: string): DocumentChunk[] {
        const finalChunks: DocumentChunk[] = [];

        // Simple fixed-size chunking with overlap
        for (let i = 0; i < text.length; i += chunkSize - chunkOverlap) {
            // Don't go past the end of the text
            const end = Math.min(i + chunkSize, text.length);
            const chunk = text.slice(i, end);

            if (chunk.trim()) { // Skip empty chunks
                finalChunks.push({
                    content: chunk,
                    metadata: {
                        source: relativeSourcePath,
                        chunkIndex: -1, // Placeholder, will be updated after adding to documents array
                        createdAt: timestamp,
                    }
                });
            }
        }

        return finalChunks;
    }

    /**
     * Splits text recursively, aiming for chunks near the target size.
     * Prioritizes splitting on larger semantic boundaries.
     * @param text The input text.
     * @param relativeSourcePath Identifier for the source document.
     * @param chunkSize The target size of each chunk.
     * @param chunkOverlap The overlap between chunks.
     * @param timestamp The creation timestamp.
     * @returns An array of DocumentChunk.
     */
    private _semanticChunking(text: string, relativeSourcePath: string, chunkSize: number, chunkOverlap: number, timestamp: string): DocumentChunk[] {
        const separators = ["\n\n\n", "\n\n", "\n", ". ", "! ", "? ", "; ", ": ", " ", ""]; // Prioritize larger breaks
        const finalChunks: DocumentChunk[] = [];

        function split(textToSplit: string, currentSeparators: string[]): string[] {
            if (textToSplit.length <= chunkSize || currentSeparators.length === 0) {
                return textToSplit.trim() ? [textToSplit.trim()] : [];
            }

            const separator = currentSeparators[0];
            const nextSeparators = currentSeparators.slice(1);
            let chunks: string[] = [];

            if (separator === "") { // Character split as last resort
                for (let i = 0; i < textToSplit.length; i += chunkSize) {
                    chunks.push(textToSplit.slice(i, i + chunkSize));
                }
            } else {
                const potentialSplits = textToSplit.split(separator);
                let currentChunk = "";
                for (const part of potentialSplits) {
                    const potentialChunk = currentChunk ? currentChunk + separator + part : part;
                    if (potentialChunk.length > chunkSize && currentChunk) {
                        chunks.push(currentChunk); // Current chunk is good
                        currentChunk = part;      // Start new chunk
                    } else {
                        currentChunk = potentialChunk; // Add to current chunk
                    }
                }
                if (currentChunk) chunks.push(currentChunk); // Add the last part
            }

            // Recursively split chunks that are still too large
            const finalSplits: string[] = [];
            for (const chunk of chunks) {
                if (chunk.length > chunkSize) {
                    finalSplits.push(...split(chunk, nextSeparators)); // Recurse
                } else if (chunk.trim()) {
                    finalSplits.push(chunk.trim());
                }
            }
            return finalSplits;
        }

        const initialSplits = split(text, separators);

        // Add chunks with overlap
        for (let i = 0; i < initialSplits.length; i++) {
            const currentContent = initialSplits[i];
            let combinedContent = currentContent;

            if (i > 0 && chunkOverlap > 0) {
                const prevContent = initialSplits[i - 1];
                // Get last N characters/tokens of previous chunk for overlap
                const overlapText = prevContent.slice(-chunkOverlap);
                combinedContent = overlapText + " " + currentContent; // Simple space separator for overlap
            }

            finalChunks.push({
                content: combinedContent,
                metadata: {
                    source: relativeSourcePath,
                    chunkIndex: -1, // Placeholder, will be updated after adding to documents array
                    createdAt: timestamp,
                }
            });
        }

        return finalChunks;
    }

    /**
     * Generates embeddings for an array of text chunks using the initialized embedder and cache.
     * @param texts Array of text strings.
     * @returns A Promise resolving to an array of embeddings (Float32Array). Throws on error.
     */
    private async _generateEmbeddings(texts: string[]): Promise<Float32Array[]> {
        if (!this.embedder) throw new RagError("Embedder not initialized.", 'EMBEDDER_NOT_READY');

        const embeddingsMap: Map<number, Float32Array> = new Map(); // Map index to embedding
        const textsToEmbed: string[] = [];
        const originalIndices: number[] = []; // Track original index of texts needing embedding

        // Check cache first
        for (let i = 0; i < texts.length; i++) {
            const text = texts[i];
            const cacheKey = this._hashText(text); // Simple hash for cache key

            if (this.embeddingsCache[cacheKey]) {
                embeddingsMap.set(i, new Float32Array(this.embeddingsCache[cacheKey]));
            } else {
                textsToEmbed.push(text);
                originalIndices.push(i);
            }
        }

        // If all embeddings were found in cache
        if (textsToEmbed.length === 0) {
            console.log(`All ${texts.length} embeddings retrieved from cache.`);
            return texts.map((_, i) => embeddingsMap.get(i)!); // Return in original order
        }

        // Generate embeddings for texts not in cache
        console.log(`Generating embeddings for ${textsToEmbed.length} of ${texts.length} texts (cache miss)...`);
        try {
            // Use the embedder pipeline
            const output = await this.embedder(textsToEmbed, { pooling: 'mean', normalize: true });

            // Handle Tensor object structure from @xenova/transformers v2
             if (output && typeof output === 'object' && 'data' in output && 'dims' in output && output.data instanceof Float32Array) {
                const tensorData = output.data as Float32Array;
                const dims = output.dims as number[];
                const batchSize = dims[0];
                const embSize = dims[1];

                if (batchSize !== textsToEmbed.length) {
                     console.warn(`Embedding batch size mismatch: expected ${textsToEmbed.length}, got ${batchSize}`);
                     // Attempt to proceed if embSize is correct, otherwise fail
                     if(dims.length < 2 || embSize !== this.dimension) {
                        throw new RagError(`Embedding dimension mismatch or invalid batch size. Expected ${this.dimension}`, 'EMBEDDING_DIM_MISMATCH');
                     }
                }
                 if (embSize !== this.dimension) {
                     throw new RagError(`Embedding dimension mismatch: expected ${this.dimension}, model produced ${embSize}`, 'EMBEDDING_DIM_MISMATCH');
                 }

                 // Extract embeddings and update cache
                 for (let i = 0; i < batchSize; i++) {
                     const start = i * embSize;
                     const end = start + embSize;
                     const embedding = tensorData.slice(start, end);
                     const originalIndex = originalIndices[i];
                     embeddingsMap.set(originalIndex, embedding);

                     // Add to cache
                     const cacheKey = this._hashText(textsToEmbed[i]);
                     this.embeddingsCache[cacheKey] = Array.from(embedding); // Store as plain array in JSON cache
                 }

                 console.log(`Generated and cached ${batchSize} new embeddings.`);

             } else {
                 console.error("Unrecognized or invalid embedding output format:", output);
                 throw new RagError('Unrecognized embedding output format', 'EMBEDDING_FORMAT_ERROR');
             }

            // Reconstruct the final embeddings array in the original order
            const finalEmbeddings: Float32Array[] = [];
            for (let i = 0; i < texts.length; i++) {
                const emb = embeddingsMap.get(i);
                if (!emb) {
                     // This should not happen if logic is correct
                    throw new RagError(`Internal error: Embedding missing for index ${i}`, 'INTERNAL_ERROR');
                }
                finalEmbeddings.push(emb);
            }
            return finalEmbeddings;

        } catch (error) {
            if (error instanceof RagError) throw error;
            const message = error instanceof Error ? error.message : String(error);
            console.error("Error generating embeddings:", error);
            throw new RagError(`Error generating embeddings: ${message}`, 'EMBEDDING_GENERATION_FAILED', error);
        }
    }

    /** Creates a simple hash of a text string. */
    private _hashText(text: string): string {
        let hash = 0;
        for (let i = 0; i < text.length; i++) {
            hash = Math.imul(31, hash) + text.charCodeAt(i) | 0;
        }
        return `h_${hash}`; // Prefix to ensure it's a valid object key
    }

    // --- Retrieval ---

    /**
     * Retrieves relevant chunks from the knowledge base based on a query.
     * @param query The query string.
     * @param nResults The number of results to return (overrides this.retrievalNResults if provided).
     * @param isCancelled Optional function to check for cancellation.
     * @returns A Promise resolving to an array of RetrievedChunk. Throws on error.
     */
    async getRelevantChunks(query: string, nResults?: number, isCancelled: () => boolean = () => false): Promise<RetrievedChunk[]> {
        await this._ensureInitialized();
        if (!this.embedder || !this.index) throw new RagError("Handler components missing for retrieval.", "INTERNAL_ERROR");
        if (this.index.ntotal() === 0) {
            console.log('Index is empty. No chunks to retrieve.');
            return [];
        }
        if (isCancelled()) { console.log("[RAG] Retrieval cancelled before query embedding."); return []; }

        // Use provided nResults or fall back to the instance property
        const numResults = nResults || this.retrievalNResults;

        try {
            // Apply query expansion if enabled
            let expandedQuery = query;
            if (this.useQueryExpansion) {
                // Simple query expansion for now - in a real implementation, this would use an LLM
                // to generate a more comprehensive query
                console.log("Query expansion enabled. Expanding query...");
                // This is a placeholder - in a real implementation, you would use a more sophisticated approach
                expandedQuery = query; // For now, just use the original query
            }

            console.log(`Generating query embedding for: "${expandedQuery.substring(0, 50)}..."`);
            const queryEmbeddings = await this._generateEmbeddings([expandedQuery]);
            if (isCancelled()) { console.log("[RAG] Retrieval cancelled after query embedding."); return []; }

            if (!queryEmbeddings || queryEmbeddings.length === 0) {
                throw new RagError("Failed to generate query embedding.", 'QUERY_EMBEDDING_FAILED');
            }
            const queryVector = Array.from(queryEmbeddings[0]); // Convert Float32Array to number[] for FAISS

            // Validate query vector dimension matches index dimension
            if (queryVector.length !== this.dimension) {
                console.error(`Query vector dimension mismatch: got ${queryVector.length}, expected ${this.dimension}`);
                throw new RagError(
                    `Dimension mismatch between query vector (${queryVector.length}) and index (${this.dimension}). ` +
                    `This usually happens when changing embedding models. Try reindexing the knowledge base.`,
                    'DIMENSION_MISMATCH'
                );
            }

            // Determine how many results to retrieve
            // If reranking is enabled, retrieve more results to allow for better reranking
            const retrievalCount = this.useReranking ? Math.max(numResults * 3, 10) : numResults;
            const k = Math.min(retrievalCount, this.index.ntotal()); // Adjust k if index is smaller

            console.log(`Searching index with ${this.index.ntotal()} vectors for top ${k} results...`);
            if (isCancelled()) { console.log("[RAG] Retrieval cancelled before index search."); return []; }

            try {
                const searchResult = this.index.search(queryVector, k);
                if (isCancelled()) { console.log("[RAG] Retrieval cancelled after index search."); return []; }

                let retrievedChunks: RetrievedChunk[] = [];
                for (let i = 0; i < searchResult.labels.length; i++) {
                    const docIndex = searchResult.labels[i]; // This is the index in the FAISS index, corresponding to this.documents array index
                    const distance = searchResult.distances[i]; // L2 distance

                    if (docIndex >= 0 && docIndex < this.documents.length) {
                        retrievedChunks.push({
                            ...this.documents[docIndex],
                            similarity: distance // Initially store the L2 distance (will be converted later)
                        });
                    } else {
                        console.warn(`Invalid index ${docIndex} returned from FAISS search (max: ${this.documents.length - 1}). Skipping.`);
                    }
                }

                // Apply similarity threshold filtering
                // FAISS returns L2 distances (lower is better), but we need to convert to similarity scores (higher is better)
                // for consistent filtering logic
                if (this.retrievalThreshold > 0) {
                    // First, normalize the L2 distances to a 0-1 similarity score (1 being most similar)
                    // We'll use a simple exponential decay function: similarity = exp(-distance)
                    retrievedChunks.forEach(chunk => {
                        // Convert L2 distance to similarity score (0-1 range, higher is better)
                        const similarityScore = Math.exp(-chunk.similarity);
                        // Store both values
                        chunk.l2Distance = chunk.similarity; // Keep the original L2 distance
                        chunk.similarity = similarityScore; // Replace with normalized similarity score
                    });

                    const originalCount = retrievedChunks.length;
                    // Now filter using the similarity score (higher is better)
                    retrievedChunks = retrievedChunks.filter(chunk => chunk.similarity >= this.retrievalThreshold);
                    if (retrievedChunks.length < originalCount) {
                        console.log(`Filtered out ${originalCount - retrievedChunks.length} chunks with similarity < ${this.retrievalThreshold}`);
                    }
                }

                // Apply reranking if enabled
                if (this.useReranking && retrievedChunks.length > numResults) {
                    console.log("Reranking enabled. Reranking results...");
                    // This is a placeholder - in a real implementation, you would use a more sophisticated reranking approach
                    // For now, just use the original ranking but limit to numResults
                    retrievedChunks = retrievedChunks.slice(0, numResults);
                }

                console.log(`Retrieved ${retrievedChunks.length} relevant chunks.`);
                return retrievedChunks;
            } catch (searchError) {
                // Handle FAISS search errors specifically
                const message = searchError instanceof Error ? searchError.message : String(searchError);
                console.error("FAISS search error:", searchError);
                throw new RagError(`FAISS search failed: ${message}. This may be due to a dimension mismatch.`, 'FAISS_SEARCH_FAILED', searchError);
            }

        } catch (error) {
            if (error instanceof RagError) throw error;
            const message = error instanceof Error ? error.message : String(error);
            console.error("Error during chunk retrieval:", error);
            throw new RagError(`Chunk retrieval failed: ${message}`, 'RETRIEVAL_FAILED', error);
        }
    }

    // --- Utility & Management ---

    /** Returns a list of unique source identifiers (relative paths) currently indexed. */
    public async getUniqueSources(): Promise<string[]> {
        await this._ensureInitialized(); // Ensure documents are loaded
        const uniqueSources = [...new Set(this.documents.map(doc => doc.metadata.source))];
        return uniqueSources.sort();
    }

    /** Gets the absolute path of the current knowledge base directory. */
    public getKnowledgeBasePath(): string {
        return this.persistDirectory;
    }

     /** Gets the relative path of the current knowledge base directory from the root. */
     public getCurrentKbRelativePath(): string {
        return path.relative(KNOWLEDGE_BASE_ROOT, this.persistDirectory) || '.';
    }

    /** Gets the current storage type ('local' or 'server'). */
    public getStorageType(): 'local' | 'server' {
        return this.storageType;
    }

    /**
     * Verifies that the loaded FAISS index has the expected dimension.
     * If not, it will recreate the index with the correct dimension.
     * @private
     */
    private _verifyIndexDimension(): void {
        if (!this.index) return;

        try {
            // Create a test vector with the expected dimension
            const testVector = new Array(this.dimension).fill(0.1);

            // Try to search with this vector - this will throw if dimensions don't match
            this.index.search(testVector, 1);
            console.log(`FAISS index dimension verified: ${this.dimension}`);
        } catch (error) {
            console.warn(`FAISS index dimension verification failed. Recreating index with dimension ${this.dimension}.`);
            console.error(error);

            // Recreate the index with the correct dimension
            this.index = new Index(this.dimension);

            // Flag that we need to reindex all documents
            if (this.documents.length > 0) {
                console.log(`Need to reindex ${this.documents.length} documents with the new index dimension.`);
                // We'll handle reindexing in the calling method
                this._needsReindexing = true;
            }
        }
    }

    /**
     * Rebuilds the FAISS index with existing documents after a dimension change.
     * This is a simplified version of reindexKnowledgeBase that doesn't reload files.
     * @private
     */
    private async _rebuildIndexWithExistingDocuments(): Promise<void> {
        if (!this.index || this.documents.length === 0) return;

        console.log(`Rebuilding index with ${this.documents.length} existing documents...`);

        try {
            // Clear the index but keep the documents
            this.index = new Index(this.dimension);

            // Re-generate embeddings for all documents and add them to the index
            const contents = this.documents.map(doc => doc.content);
            const embeddings = await this._generateEmbeddings(contents);

            if (embeddings.length !== contents.length) {
                throw new RagError(`Embedding generation failed: expected ${contents.length} embeddings, got ${embeddings.length}`, 'EMBEDDING_COUNT_MISMATCH');
            }

            // Add embeddings to the index
            const flatEmbeddings = embeddings.reduce((acc, val) => acc.concat(Array.from(val)), [] as number[]);
            this.index.add(flatEmbeddings);

            console.log(`Successfully rebuilt index with ${this.documents.length} documents.`);

            // Save the updated index
            await this._saveIndex();
        } catch (error) {
            const message = error instanceof Error ? error.message : String(error);
            console.error(`Error rebuilding index: ${message}`, error);
            throw new RagError(`Failed to rebuild index after dimension change: ${message}`, 'REBUILD_INDEX_FAILED', error);
        }
    }

    /** Clears all documents and resets the index for the *current* knowledge base. */
    public async clearKnowledgeBase(): Promise<boolean> {
        await this._ensureInitialized();
        console.log(`Clearing current knowledge base: ${this.persistDirectory}`);
        try {
            this.index = new Index(this.dimension); // Create new empty index
            this.documents = [];
            this.embeddingsCache = {}; // Clear cache as well

            await this._saveIndex(); // Save the empty state
            console.log("Knowledge base cleared successfully.");
            return true;
        } catch (error) {
            const message = error instanceof Error ? error.message : String(error);
            console.error("Error clearing knowledge base:", error);
            throw new RagError(`Failed to clear knowledge base: ${message}`, 'CLEAR_KB_FAILED', error);
        }
    }

    /**
     * Re-indexes all source files currently associated with the loaded documents.
     * This is useful if parsing/chunking logic changes.
     */
    public async reindexKnowledgeBase(): Promise<{ successCount: number, failureCount: number, results: Record<string, boolean>, knowledgeBasePath: string }> {
        await this._ensureInitialized();
        console.log(`Starting re-index for knowledge base: ${this.persistDirectory}`);

        const currentSources = await this.getUniqueSources();
        if (currentSources.length === 0) {
            console.log("No sources found to re-index.");
            return { successCount: 0, failureCount: 0, results: {}, knowledgeBasePath: this.persistDirectory };
        }

        console.log(`Found ${currentSources.length} sources to re-index.`);

        // Clear existing data BEFORE re-adding
        await this.clearKnowledgeBase();

        // Re-add each source file
        const results: Record<string, boolean> = {};
        let successCount = 0;
        let failureCount = 0;

        for (const relativeSourcePath of currentSources) {
            const absoluteFilePath = path.resolve(this.persistDirectory, relativeSourcePath); // Resolve source path
            try {
                await this.addFile(absoluteFilePath); // addFile now handles relative path calculation and deletion
                results[relativeSourcePath] = true;
                successCount++;
            } catch (error) {
                console.error(`Error re-indexing file ${relativeSourcePath}:`, error instanceof Error ? error.message : String(error));
                results[relativeSourcePath] = false;
                failureCount++;
            }
        }

        console.log(`Re-indexing complete. Success: ${successCount}, Failed: ${failureCount}`);
        // Final save is handled by the last addFile call
        return { successCount, failureCount, results, knowledgeBasePath: this.persistDirectory };
    }

    /**
     * Lists all knowledge base directories found within KNOWLEDGE_BASE_ROOT.
     * @param storageType Ignored, as listing is always relative to KNOWLEDGE_BASE_ROOT.
     * @returns A promise resolving to an array of relative knowledge base directory paths.
     */
    public async listKnowledgeBases(storageType?: string): Promise<string[]> { // storageType is unused now
        try {
            await fs.access(KNOWLEDGE_BASE_ROOT); // Check if root exists
            const entries = await fs.readdir(KNOWLEDGE_BASE_ROOT, { withFileTypes: true });
            const kbDirs = entries
                .filter(entry => entry.isDirectory())
                .map(entry => entry.name); // Return just the name (relative path)
            return kbDirs.sort();
        } catch (error: any) {
            if (error.code === 'ENOENT') {
                console.log(`Knowledge base root directory ${KNOWLEDGE_BASE_ROOT} not found.`);
                return []; // Return empty list if root doesn't exist
            }
            console.error('Error listing knowledge bases:', error);
            throw new RagError(`Failed to list knowledge bases: ${error.message}`, 'LIST_KB_FAILED', error);
        }
    }

    /**
     * Creates a new knowledge base directory within the KNOWLEDGE_BASE_ROOT.
     * @param kbName The desired name for the new knowledge base (will be sanitized).
     * @param storageType Ignored, creation is always within KNOWLEDGE_BASE_ROOT.
     * @returns A promise resolving to the relative path of the created KB or null if failed/exists.
     */
    public async createKnowledgeBase(kbName: string, storageType?: string): Promise<string | null> {
        try {
            // Basic sanitization (replace spaces, remove invalid chars)
            const sanitizedName = kbName.trim().replace(/\s+/g, '_').replace(/[^a-zA-Z0-9_-]/g, '');
            if (!sanitizedName) {
                throw new Error("Invalid or empty knowledge base name after sanitization.");
            }

            // Resolve and validate path - creation only allowed within KNOWLEDGE_BASE_ROOT
            const newKbPath = await resolveAndValidatePath(sanitizedName); // Uses helper

            // Check if it already exists
            try {
                await fs.access(newKbPath);
                console.warn(`Knowledge base '${sanitizedName}' already exists at ${newKbPath}.`);
                return null; // Indicate already exists
            } catch { /* Doesn't exist, proceed */ }

            // Create the directory
            await fs.mkdir(newKbPath, { recursive: true });
            console.log(`Created new knowledge base: ${newKbPath}`);
            return sanitizedName; // Return the relative path/name
        } catch (error) {
            console.error(`Error creating knowledge base '${kbName}':`, error);
            // Don't throw here, return null to indicate failure in controller
            return null;
            // throw new RagError(`Failed to create KB '${kbName}': ${message}`, 'CREATE_KB_FAILED', error);
        }
    }

    /**
     * Removes a knowledge base directory and its contents.
     * @param absoluteKbPath The absolute, validated path to the knowledge base directory.
     * @param storageType Ignored, path must be absolute and validated.
     * @returns A promise resolving to true on success. Throws on error.
     */
    public async removeKnowledgeBase(absoluteKbPath: string, storageType?: string): Promise<boolean> {
         // Path validation MUST happen in the caller (controller using resolveAndValidatePath)
         // Double check it's within the allowed base path for safety
         if (!absoluteKbPath.startsWith(ALLOWED_BASE_PATH) || absoluteKbPath === ALLOWED_BASE_PATH || absoluteKbPath === KNOWLEDGE_BASE_ROOT) {
              throw new RagError(`Attempted to remove invalid or restricted path: ${absoluteKbPath}`, 'REMOVE_KB_INVALID_PATH');
         }
         console.log(`Attempting to remove knowledge base directory: ${absoluteKbPath}`);
         try {
            // Check if it's the currently active one
            if (this.persistDirectory === absoluteKbPath) {
                 throw new RagError("Cannot remove the currently active knowledge base. Switch to another KB first.", "REMOVE_ACTIVE_KB");
            }
            // Perform recursive removal
            await fs.rm(absoluteKbPath, { recursive: true, force: true }); // Use force to ignore non-existent is okay
            console.log(`Successfully removed knowledge base directory: ${absoluteKbPath}`);
            return true;
         } catch (error: any) {
            const message = error.message || String(error);
            console.error(`Error removing knowledge base directory ${absoluteKbPath}:`, error);
            throw new RagError(`Failed to remove KB directory: ${message}`, 'REMOVE_KB_FAILED', error);
         }
    }

    /**
     * Updates the RAG handler configuration with new settings.
     * This will reinitialize the embedding model if it has changed.
     * @param options Configuration options to update
     * @returns A promise resolving to true on success
     */
    public async updateConfiguration(options: Partial<RagHandlerOptions>): Promise<boolean> {
        try {
            // Save current state if initialized
            if (this.isInitialized) {
                await this._saveIndex();
            }

            let needsReinitialization = false;

            // Update embedding model if provided and different
            if (options.embeddingModel && options.embeddingModel !== this.embeddingModelName) {
                // Ensure the model name has the 'Xenova/' prefix if it doesn't already
                let newModelName = options.embeddingModel;
                if (!newModelName.startsWith('Xenova/') && !newModelName.includes('/')) {
                    newModelName = `Xenova/${newModelName}`;
                }

                console.log(`Changing embedding model from ${this.embeddingModelName} to ${newModelName}`);
                this.embeddingModelName = newModelName;

                // Automatically set the correct dimension for the model
                const newDimension = MODEL_DIMENSIONS[newModelName] || DEFAULT_DIMENSION;
                if (newDimension !== this.dimension) {
                    console.log(`Automatically updating dimension from ${this.dimension} to ${newDimension} to match model`);
                    this.dimension = newDimension;
                }

                // Clear the embedder to force reinitialization
                this.embedder = null;
                needsReinitialization = true;
            }

            // Update dimension if explicitly provided (overrides automatic dimension)
            else if (options.dimension && options.dimension !== this.dimension) {
                console.log(`Changing embedding dimension from ${this.dimension} to ${options.dimension}`);
                this.dimension = options.dimension;
                needsReinitialization = true;
            }

            // Update chunking settings if provided
            if (options.chunkSize && options.chunkSize !== this.chunkSize) {
                console.log(`Changing chunk size from ${this.chunkSize} to ${options.chunkSize}`);
                this.chunkSize = options.chunkSize;
            }

            if (options.chunkOverlap && options.chunkOverlap !== this.chunkOverlap) {
                console.log(`Changing chunk overlap from ${this.chunkOverlap} to ${options.chunkOverlap}`);
                this.chunkOverlap = options.chunkOverlap;
            }

            // Update chunking strategy if provided
            if (options.chunkingStrategy && options.chunkingStrategy !== this.chunkingStrategy) {
                console.log(`Changing chunking strategy from ${this.chunkingStrategy} to ${options.chunkingStrategy}`);
                this.chunkingStrategy = options.chunkingStrategy;
            }

            // Update retrieval settings if provided
            if (options.retrievalNResults && options.retrievalNResults !== this.retrievalNResults) {
                console.log(`Changing retrieval results count from ${this.retrievalNResults} to ${options.retrievalNResults}`);
                this.retrievalNResults = options.retrievalNResults;
            }

            if (options.retrievalThreshold !== undefined && options.retrievalThreshold !== this.retrievalThreshold) {
                console.log(`Changing retrieval threshold from ${this.retrievalThreshold} to ${options.retrievalThreshold}`);
                this.retrievalThreshold = options.retrievalThreshold;
            }

            // Update advanced features if provided
            if (options.useReranking !== undefined && options.useReranking !== this.useReranking) {
                console.log(`${options.useReranking ? 'Enabling' : 'Disabling'} reranking`);
                this.useReranking = options.useReranking;
            }

            if (options.useQueryExpansion !== undefined && options.useQueryExpansion !== this.useQueryExpansion) {
                console.log(`${options.useQueryExpansion ? 'Enabling' : 'Disabling'} query expansion`);
                this.useQueryExpansion = options.useQueryExpansion;
            }

            // If model or dimension changed, we need to reinitialize
            if (needsReinitialization) {
                console.log("Configuration changes require reinitialization...");

                // Clear existing index and create a new one with the updated dimension
                if (this.index) {
                    console.log(`Recreating FAISS index with new dimension: ${this.dimension}`);
                    this.index = new Index(this.dimension);
                }

                this.isInitialized = false;
                this.isInitializing = false;
                await this._initialize();

                // After initialization, check if we need to reindex
                if (this.documents.length > 0) {
                    console.log("Model dimension changed - need to reindex all documents with new model...");
                    await this.reindexKnowledgeBase();
                }
            }

            return true;
        } catch (error) {
            const message = error instanceof Error ? error.message : String(error);
            console.error(`Error updating RAG configuration:`, error);
            throw new RagError(`Failed to update RAG configuration: ${message}`, 'UPDATE_CONFIG_FAILED', error);
        }
    }

    /**
     * Changes the current knowledge base directory. Handles saving current state and loading new state.
     * @param userPath The relative path (within KNOWLEDGE_BASE_ROOT) or absolute path (validated) to the new KB.
     * @param storageType Must match the type implied by the userPath ('local' outside root, 'server' inside root).
     * @returns A promise resolving to true on success. Throws on error.
     */
    public async setKnowledgeBasePath(userPath: string, storageType: 'local' | 'server'): Promise<boolean> {
        let absoluteKbPath: string;
        try {
            // Validate and resolve path based on storage type expectation
            if (storageType === 'server') {
                absoluteKbPath = await resolveAndValidatePath(userPath); // Resolves relative to KB root
            } else { // 'local' storage
                 // For local, resolve normally but still check it's within ALLOWED_BASE_PATH
                 absoluteKbPath = path.resolve(userPath); // Resolve absolute path directly
                 if (!absoluteKbPath.startsWith(ALLOWED_BASE_PATH)) {
                      throw new Error('Access denied: Local path is outside the allowed base directory.');
                 }
                 // Extra check: prevent setting local path *inside* the server's KB root? Optional.
                 // if (absoluteKbPath.startsWith(KNOWLEDGE_BASE_ROOT)) {
                 //    throw new Error("Local path cannot be inside the server's knowledge base root.");
                 // }
            }
        } catch(validationError: any) {
             throw new RagError(`Invalid knowledge base path specified: ${validationError.message}`, 'INVALID_PATH', validationError);
        }


        // If we're already using this directory, do nothing
        if (this.persistDirectory === absoluteKbPath) {
            console.log(`Already using knowledge base at ${absoluteKbPath}`);
            // Ensure storage type matches just in case
             this.storageType = storageType;
            return true;
        }

        console.log(`Attempting to switch knowledge base to: ${absoluteKbPath} (Type: ${storageType})`);
        try {
            // Save current state before switching (if initialized)
            if (this.isInitialized) {
                 await this._saveIndex();
            }

            // Reset state before loading new KB
            this.isInitialized = false; // Mark as not ready during switch
            this.isInitializing = false; // Reset init flag
            this.index = null;
            this.documents = [];
            // Keep the embedder loaded if it's the same model? For now, we reload.
            this.embedder = null;
            this.embeddingsCache = {};

            // Update directory path and storage type
            this.persistDirectory = absoluteKbPath;
            this.storageType = storageType;
            this._updateFilePaths();

            // Re-initialize with the new directory
            // This handles directory creation, loading index/meta/cache, loading embedder
            await this._initialize();

            console.log(`Successfully switched knowledge base to ${this.persistDirectory}`);
            return true;
        } catch (error) {
            const message = error instanceof Error ? error.message : String(error);
            console.error(`Error switching knowledge base path to ${absoluteKbPath}:`, error);
             // Attempt to revert?? Difficult to do reliably. Mark as uninitialized.
             this.isInitialized = false;
             this.persistDirectory = ''; // Clear path as state is uncertain
             this.index = null;
             this.documents = [];
            throw new RagError(`Failed to switch knowledge base: ${message}`, 'SET_KB_FAILED', error);
        }
    }

    /**
     * Processes all supported files in a directory and adds them to the current knowledge base.
     * @param absoluteDirectoryPath The absolute, validated path to the directory.
     * @param recursive Whether to process subdirectories.
     * @param storageType Ignored, path must be absolute and validated by caller.
     * @returns A promise resolving to an object with processing results. Throws on error.
     */
    public async addDirectory(absoluteDirectoryPath: string, recursive: boolean = true, storageType?: string): Promise<{ successCount: number, failureCount: number, results: Record<string, boolean> }> {
        await this._ensureInitialized();
        // Path validation MUST happen in the caller (controller using resolveAndValidatePath)

        console.log(`Processing directory: ${absoluteDirectoryPath} (Recursive: ${recursive})`);
        const results: Record<string, boolean> = {};
        let successCount = 0;
        let failureCount = 0;
        const supportedExtensions = [
            '.pdf', '.docx', '.txt', '.md', '.xlsx', '.xls', '.csv', '.ts', '.js',
            '.py', '.java', '.c', '.cpp', '.cs', '.php', '.rb', '.html', '.htm',
            '.json', '.xml', '.yaml', '.yml', '.ini', '.config', '.sql' // Added more text types
        ];

        // Internal function to avoid repeating logic
        const processEntry = async (entryPath: string) => {
             try {
                 const stats = await fs.stat(entryPath);
                 if (stats.isDirectory() && recursive) {
                     await processDirectoryInternal(entryPath); // Recurse
                 } else if (stats.isFile()) {
                     const ext = path.extname(entryPath).toLowerCase();
                     const relativePath = path.relative(this.persistDirectory, entryPath).replace(/\\/g, '/');
                     if (supportedExtensions.includes(ext)) {
                         try {
                             // Pass absolute path to addFile
                             await this.addFile(entryPath);
                             results[relativePath] = true; // Use relative path for results key
                             successCount++;
                         } catch (fileError) {
                             console.error(`Error processing file ${relativePath}:`, fileError instanceof Error ? fileError.message : String(fileError));
                             results[relativePath] = false;
                             failureCount++;
                         }
                     } else {
                         // console.log(`Skipping unsupported file type: ${relativePath}`); // Reduce noise
                         // results[relativePath] = false; // Optionally track skipped files
                     }
                 }
             } catch (statError: any) {
                 console.error(`Error accessing entry ${entryPath}:`, statError.message);
                 // Can't determine type, count as failure?
                 const relativePath = path.relative(this.persistDirectory, entryPath).replace(/\\/g, '/');
                 results[relativePath] = false;
                 failureCount++;
             }
        };

        const processDirectoryInternal = async (dirPath: string) => {
            try {
                const entries = await fs.readdir(dirPath);
                // Process entries concurrently (adjust concurrency if needed)
                await Promise.all(entries.map(entry => processEntry(path.join(dirPath, entry))));
            } catch (readError: any) {
                 console.error(`Error reading directory ${dirPath}:`, readError.message);
                 // Treat as failure for the whole directory? Or just log?
                 failureCount++; // Count directory read error as a failure point
            }
        };

        try {
            await processDirectoryInternal(absoluteDirectoryPath);
            console.log(`Directory processing finished for ${absoluteDirectoryPath}. Success: ${successCount}, Failed: ${failureCount}`);

            // Final save after processing all files in the batch
            if (successCount > 0) { // Only save if something was potentially added
                await this._saveIndex();
            }

            return { successCount, failureCount, results };

        } catch (error) {
            const message = error instanceof Error ? error.message : String(error);
            console.error(`General error processing directory ${absoluteDirectoryPath}:`, error);
            throw new RagError(`Failed to process directory: ${message}`, 'ADD_DIR_FAILED', error);
        }
    }

} // End of RagHandler class

// Optional: Export a singleton instance if needed throughout the application
// export const ragHandlerInstance = new RagHandler();