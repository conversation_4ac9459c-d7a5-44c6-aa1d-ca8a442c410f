# Server configuration
PORT=3000
NODE_ENV=development

# Frontend URL allowed by CORS
# For local development, use your frontend dev server address (e.g., http://localhost:5173)
# For production, set this to your deployed frontend domain (e.g., https://maiachat.onrender.com)
FRONTEND_URL=http://localhost:5173

# MongoDB connection string
MONGODB_URI=mongodb://localhost:27017/maia-web

# JWT settings
JWT_SECRET=YOUR_REALLY_STRONG_JWT_SECRET_KEY
JWT_EXPIRATION=7d

# Encryption Secret (for API keys stored in DB - MUST be 32 characters)
ENCRYPTION_SECRET=YOUR_REALLY_STRONG_API_KEY_ENCRYPTION_SECRET

# Google OAuth Credentials (Get from Google Cloud Console)
GOOGLE_CLIENT_ID=YOUR_GOOGLE_CLIENT_ID
GOOGLE_CLIENT_SECRET=YOUR_GOOGLE_CLIENT_SECRET

# LLM API Keys (Add as needed)
OPENAI_API_KEY=your_openai_api_key_here
ANTHROPIC_API_KEY=your_anthropic_api_key_here
GROQ_API_KEY=your_groq_api_key_here
GOOGLE_API_KEY=your_google_api_key_here
DEEPSEEK_API_KEY=your_deepseek_api_key_here

# Search Provider API Keys & IDs
SERPER_API_KEY=your_serper_api_key_here
# GOOGLE_API_KEY is used above for both GenAI and Custom Search
GOOGLE_SEARCH_ENGINE_ID=your_google_custom_search_engine_id_here

# Real-time Data API Keys (Optional)
POLYGON_API_KEY=your_polygon_api_key_here
OPENWEATHER_API_KEY=your_openweathermap_api_key_here

# Local LLM Server Base URLs (Optional - if running locally and NOT using Docker host.docker.internal)
# OLLAMA_BASE_URL=http://localhost:11434
# LMSTUDIO_BASE_URL=http://localhost:1234/v1

# Local LLM Timeout Settings (in milliseconds)
OLLAMA_REQUEST_TIMEOUT=60000  # 60 seconds for Ollama model loading
LMSTUDIO_REQUEST_TIMEOUT=30000  # 30 seconds for LM Studio
