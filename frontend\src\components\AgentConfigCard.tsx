import React, { useState, useEffect, useCallback, useMemo } from 'react';
import { useConfigStore } from '../store/configStore';
import Modal from './Modal'; // Import the Modal component
import { ArrowPathIcon } from '@heroicons/react/24/outline'; // Import spinner icon

// Import the base URL from config is no longer needed here as we use apiClient
// which already has the base URL configured

// Interface for model data
interface ModelData {
  id: string;
  name: string;
  contextWindow?: number;
  maxOutputTokens?: number;
}

// Function to normalize model data from different formats
const normalizeModelData = (data: any[]): ModelData[] => {
  return data.map(item => {
    // If item is a string, create a ModelData object with id and name set to the string
    if (typeof item === 'string') {
      return { id: item, name: item };
    }
    // If item is already a ModelData object, return it as is
    if (typeof item === 'object' && item !== null) {
      return {
        id: item.id || item.name || '',
        name: item.name || item.id || '',
        contextWindow: item.contextWindow,
        maxOutputTokens: item.maxOutputTokens
      };
    }
    // Fallback for unexpected data
    return { id: String(item), name: String(item) };
  });
};

interface AgentConfigCardProps {
  agentNumber: number;
  providers: string[]; // Add providers prop
  isLoading: boolean; // Renamed from providersLoading for clarity from parent
}

const AgentConfigCard: React.FC<AgentConfigCardProps> = ({
  agentNumber,
  providers,
  isLoading: providersLoading // Use the passed prop name
}) => {
  // Get agent-specific config and update function from store
  const agentIndex = agentNumber - 1; // Convert agentNumber (1-based) to index (0-based)
  // Select necessary state and actions from the store
  const {
    agentConfig,
    userManagedModels, // Get the new state structure
    updateAgentConfig,
    addUserModel, // Use new action
    removeUserModel, // Use new action
    setLastSelectedModel, // Use new action
    saveCurrentProfile,
    currentProfileId,
    setGlobalError, // Keep for potential errors in modal/saving
  } = useConfigStore((state) => ({
    agentConfig: state.agentConfigurations[agentIndex],
    userManagedModels: state.userManagedModels, // Select the new state
    updateAgentConfig: state.updateAgentConfig,
    addUserModel: state.addUserModel, // Select new action
    removeUserModel: state.removeUserModel, // Select new action
    setLastSelectedModel: state.setLastSelectedModel, // Select new action
    saveCurrentProfile: state.saveCurrentProfile,
    currentProfileId: state.currentProfileId,
    setGlobalError: state.setGlobalError,
  }));

  // Local state for model fetching and UI
  const [apiModels, setApiModels] = useState<ModelData[]>([]); // Store only models fetched from API
  const [modelsLoading, setModelsLoading] = useState<boolean>(false);
  const [modelsError, setModelsError] = useState<string | null>(null);
  const [isAddModelModalOpen, setIsAddModelModalOpen] = useState<boolean>(false);
  const [isManageModelsModalOpen, setIsManageModelsModalOpen] = useState<boolean>(false); // State for manage modal
  const [newModelName, setNewModelName] = useState<string>('');

  // Get the current provider from the agent's config
  const currentProvider = agentConfig?.provider;
  // Get user management data for the current provider
  const providerManagedData = currentProvider ? userManagedModels[currentProvider] : undefined;
  const addedModels = providerManagedData?.added || [];
  const removedModels = providerManagedData?.removed || [];
  const lastSelectedModel = providerManagedData?.lastSelected;

  // Fetch API models when provider changes
  useEffect(() => {
    const provider = currentProvider;
    if (!provider) {
      setApiModels([]);
      setModelsError(null);
      return;
    }

    const fetchApiModels = async () => {
      setModelsLoading(true);
      setModelsError(null);
      setApiModels([]); // Clear previous API models

      // Import apiClient to ensure auth token is included in all requests
      const apiClient = await import('../services/authApiService').then(module => module.default);

      try {
        console.log(`Fetching API models for ${provider}...`);

        // Determine if this is a local provider
        const isLocalProvider = provider.toLowerCase() === 'ollama' || provider.toLowerCase() === 'lm studio';
        const endpoint = isLocalProvider
          ? `/api/llm/local-models/${encodeURIComponent(provider)}`
          : `/api/llm/models/${encodeURIComponent(provider)}`;

        // Special handling for OpenRouter - we need to fetch and include its API key
        if (provider.toLowerCase() === 'openrouter') {
          try {
            console.log('Fetching API keys for OpenRouter...');
            const apiKeysResponse = await apiClient.get(`/api/apikeys`);

            if (apiKeysResponse.status === 200) {
              const apiKeys = apiKeysResponse.data;
              console.log('API keys fetched successfully:', apiKeys.length);

              // Find the OpenRouter API key
              const openRouterKeyEntry = apiKeys.find((key: any) => key.serviceName === 'OPENROUTER_API_KEY');

              if (openRouterKeyEntry) {
                console.log('Found OpenRouter API key entry, fetching actual key...');
                // Now fetch the actual key value
                const keyResponse = await apiClient.get(`/api/llm/key/OPENROUTER_API_KEY`);
                if (keyResponse.status === 200) {
                  const keyData = keyResponse.data;
                  if (keyData.key) {
                    // The backend will use this key for the OpenRouter API call
                    console.log('Successfully retrieved OpenRouter API key');
                  } else {
                    console.warn('OpenRouter API key is empty or undefined');
                  }
                } else {
                  console.error('Failed to fetch OpenRouter API key:', keyResponse.status);
                }
              } else {
                console.warn('OpenRouter API key not found in API Keys tab');
              }
            } else {
              console.error('Failed to fetch API keys:', apiKeysResponse.status);
            }
          } catch (keyError) {
            console.error('Error fetching API keys for OpenRouter:', keyError);
          }
        }

        // Use apiClient for all requests to ensure auth token is included
        const response = await apiClient.get(endpoint);
        const rawData = response.data;

        if (!Array.isArray(rawData)) throw new Error('Invalid data format received for models');

        // Normalize the model data to ensure consistent format
        const normalizedModels = normalizeModelData(rawData);
        setApiModels(normalizedModels);
      } catch (error: any) {
        console.error(`Failed to fetch API models for ${provider}:`, error);
        setModelsError(error.response?.data?.message || error.message || 'Failed to load models.');
        setApiModels([]); // Ensure empty on error
      } finally {
        setModelsLoading(false);
      }
    };

    fetchApiModels();
  }, [currentProvider]);

  // Calculate the final list of models to display in the dropdown
  const displayModels = useMemo(() => {
    // Convert string models to ModelData objects
    const addedModelObjects = addedModels.map(model => ({ id: model, name: model }));

    // Create a map of model IDs to ModelData objects
    const modelMap = new Map<string, ModelData>();

    // Add API models to the map
    apiModels.forEach(model => modelMap.set(model.id, model));

    // Add user-added models to the map
    addedModelObjects.forEach(model => modelMap.set(model.id, model));

    // Remove models that are in the removedModels list
    removedModels.forEach(modelId => modelMap.delete(modelId));

    // Convert the map values to an array and sort by name
    return Array.from(modelMap.values()).sort((a, b) => a.name.localeCompare(b.name));
  }, [apiModels, addedModels, removedModels]);

  // Effect to handle model selection persistence and validation
  useEffect(() => {
    // Skip if no provider is selected
    if (!currentProvider) return;

    // Skip if models are still loading
    if (modelsLoading) return;

    // Skip if no display models are available
    if (displayModels.length === 0) return;

    const currentAgentModel = agentConfig?.model;
    let modelToUpdate: string | undefined = undefined; // Use undefined to signify no update needed initially

    // CASE 1: Component just mounted or provider changed, and model is empty but we have a lastSelectedModel
    if ((!currentAgentModel || currentAgentModel === '') && lastSelectedModel) {
      // Only update if the lastSelectedModel is valid in the current display models
      if (displayModels.some(model => model.id === lastSelectedModel)) {
        console.log(`Agent ${agentNumber}: Empty model detected with lastSelected '${lastSelectedModel}' available`);
        modelToUpdate = lastSelectedModel;
      }
    }
    // CASE 2: Models are loaded, validate the current model against available models
    else {
      // Check if the current model is valid within the display list
      const isCurrentModelValid = currentAgentModel && displayModels.some(model => model.id === currentAgentModel);

      // Only intervene if the current model is NOT valid and not empty
      if (!isCurrentModelValid && currentAgentModel) {
        console.log(`Agent ${agentNumber}: Current model '${currentAgentModel}' is not valid in available models`);

        // Try using the last selected model for the provider
        if (lastSelectedModel && displayModels.some(model => model.id === lastSelectedModel)) {
          modelToUpdate = lastSelectedModel;
          console.log(`Agent ${agentNumber}: Using lastSelected model '${lastSelectedModel}'`);
        } else {
          // If even the last selected is invalid or doesn't exist, clear the model
          modelToUpdate = '';
          console.log(`Agent ${agentNumber}: No valid model found, clearing selection`);
        }
      }
    }

    // Update the agent's config *only* if we determined an update is needed
    if (modelToUpdate !== undefined && modelToUpdate !== currentAgentModel) {
      console.log(`Agent ${agentNumber}: Updating model from '${currentAgentModel}' to '${modelToUpdate}'`);
      // Use a setTimeout to break the potential update cycle
      setTimeout(() => {
        updateAgentConfig(agentIndex, { model: modelToUpdate as string });
      }, 0);
    }

  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [currentProvider, displayModels, lastSelectedModel]); // Remove agentConfig?.model from dependencies

  // Debounced update for instructions textarea
  const debounce = <F extends (...args: any[]) => any>(func: F, waitFor: number) => {
    let timeoutId: ReturnType<typeof setTimeout> | null = null;
    return (...args: Parameters<F>): Promise<ReturnType<F>> =>
      new Promise((resolve) => {
        if (timeoutId) {
          clearTimeout(timeoutId);
        }
        timeoutId = setTimeout(() => resolve(func(...args)), waitFor);
      });
  };

  // eslint-disable-next-line react-hooks/exhaustive-deps
  const debouncedUpdateInstructions = useCallback(
    debounce((instructions: string) => {
      updateAgentConfig(agentIndex, { instructions });
    }, 500),
    [agentIndex, updateAgentConfig]
  );

  // --- Handlers ---

  const handleProviderChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    updateAgentConfig(agentIndex, { provider: e.target.value, model: '' }); // Reset model on provider change
    // The useEffect hook will handle selecting the default/lastSelected model
  };

  const handleModelChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const selectedValue = e.target.value;

    // Update the agent config - this will also update lastSelectedModel in the store
    // due to our enhanced updateAgentConfig implementation
    updateAgentConfig(agentIndex, { model: selectedValue });

    console.log(`Agent ${agentNumber}: Model changed to '${selectedValue}' for provider '${currentProvider}'`);

    // Trigger profile save immediately if a profile is loaded
    if (currentProfileId) {
      saveCurrentProfile().catch(err => console.error("Auto-save failed after model change:", err));
    }
  };

  const handleInstructionsChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    debouncedUpdateInstructions(e.target.value);
  };

  // Handler for opening the Add Model modal
  const handleOpenAddModelModal = () => {
      setNewModelName(''); // Clear previous input
      setIsAddModelModalOpen(true);
  };

  // Handler for confirming the Add Model modal
  const handleConfirmAddModel = async () => {
      if (currentProvider && newModelName.trim()) {
          const modelToAdd = newModelName.trim();
          addUserModel(currentProvider, modelToAdd);
          // Select the newly added model
          updateAgentConfig(agentIndex, { model: modelToAdd });
          setLastSelectedModel(currentProvider, modelToAdd); // Also set as last selected

          setIsAddModelModalOpen(false); // Close modal

          // Trigger profile save immediately if a profile is loaded
          if (currentProfileId) {
              console.log(`Triggering profile save after adding model ${modelToAdd}`);
              try {
                  await saveCurrentProfile();
                  console.log(`Profile saved successfully after adding ${modelToAdd}.`);
              } catch (error: any) {
                  console.error(`Failed to save profile after adding model ${modelToAdd}:`, error);
                  setGlobalError(`Failed to auto-save profile after adding model: ${error.message}`);
              }
          } else {
              console.warn("No profile loaded. Model addition will be saved when the profile is next saved.");
          }
      } else if (!currentProvider) {
          setGlobalError("Please select a provider before adding a model.");
      }
  };

  // Handler for removing a model from the Manage Models modal
   // const handleRemoveModelFromManageList = async (provider: string, modelName: string) => { ... } // Keeping the commented out section for reference if needed, removing the active unused one


  // Defensive check
  if (!agentConfig) {
    return <div className="p-4 border rounded-lg mb-4 bg-red-100 dark:bg-red-900 text-red-700 dark:text-red-200">Error: Agent configuration not found for Agent {agentNumber}.</div>;
  }

  return (
    <div className="border border-gray-300 dark:border-gray-600 rounded-lg p-4 mb-4 bg-surface-light dark:bg-surface-dark shadow-sm">
      <h4 className="text-md font-semibold mb-3 text-primary dark:text-primary-light">
        Agent {agentNumber}
      </h4>

      {/* Provider Dropdown */}
      <div className="mb-3">
        <label htmlFor={`agent-${agentNumber}-provider`} className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
          Provider:
        </label>
        <select
          id={`agent-${agentNumber}-provider`}
          name={`agent-${agentNumber}-provider`}
          value={agentConfig.provider}
          onChange={handleProviderChange}
          className="w-full p-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-white disabled:opacity-50"
          disabled={providersLoading || providers.length === 0}
        >
          {/* Updated default option text */}
          <option value="">{providersLoading ? 'Loading Providers...' : '-- Select Provider --'}</option>
          {!providersLoading && providers.map((provider) => (
            <option key={provider} value={provider}>
              {provider}
            </option>
          ))}
        </select>
      </div>

      {/* Model Dropdown */}
      <div className="mb-1"> {/* Reduced margin */}
        <label htmlFor={`agent-${agentNumber}-model`} className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
          Model:
        </label>
        {/* Add spinner next to label when loading */}
        {modelsLoading && <ArrowPathIcon className="h-4 w-4 animate-spin text-gray-500 dark:text-gray-400 inline-block ml-2" />}
        <select
          id={`agent-${agentNumber}-model`}
          name={`agent-${agentNumber}-model`}
          value={agentConfig.model || ''} // Bind to store value, fallback to empty string
          onChange={handleModelChange}
          className="w-full p-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-white disabled:opacity-50"
          disabled={!currentProvider} // Only disable if no provider selected
        >
          <option value="" disabled={modelsLoading}> {/* Keep option selectable even when loading, but show indicator */}
            {!currentProvider
              ? '-- Select Provider First --'
              : modelsLoading
              ? 'Loading Models...' // Keep text, spinner will be next to label
              : modelsError
              ? 'Error Loading Models'
              : displayModels.length === 0 && !modelsError
              ? '-- No Models Found --'
              : '-- Select Model --'}
          </option>
          {/* Render model options from the calculated display list */}
          {!modelsLoading && !modelsError && displayModels.map((model) => {
            const isLastSelected = lastSelectedModel === model.id;
            // const isAddedByUser = addedModels.includes(model.id); // Not needed for display now
            return (
              <option key={model.id} value={model.id}>
                 {isLastSelected ? '⭐ ' : ''}{model.name}
              </option>
            );
          })}
        </select>
        {modelsError && <p className="text-xs text-red-600 dark:text-red-400 mt-1">{modelsError}</p>}
      </div>

       {/* Add/Manage Model Buttons */}
       {currentProvider && (
           <div className="mb-3 flex justify-end space-x-2"> {/* Use flex for side-by-side buttons */}
               <button
                   type="button"
                   onClick={() => setIsManageModelsModalOpen(true)} // Open manage modal
                   className="text-xs px-2 py-1 bg-gray-200 dark:bg-gray-600 text-gray-700 dark:text-gray-200 rounded hover:bg-gray-300 dark:hover:bg-gray-500"
                   disabled={!currentProvider || displayModels.length === 0}
                   title="Manage models for this provider"
               >
                   Manage Models...
               </button>
               <button
                   type="button"
                   onClick={handleOpenAddModelModal}
                   className="text-xs px-2 py-1 bg-blue-100 dark:bg-blue-800 text-blue-700 dark:text-blue-200 rounded hover:bg-blue-200 dark:hover:bg-blue-700"
                   disabled={!currentProvider}
                   title="Add a new model name for this provider"
               >
                   + Add Model
               </button>
           </div>
       )}

      {/* Per-Agent Internet Toggle */}
      <div className="mb-3 flex items-center mt-2"> {/* Added margin-top */}
        <input
          type="checkbox"
          id={`agent-${agentNumber}-internet`}
          name={`agent-${agentNumber}-internet`}
          checked={agentConfig.internetEnabled ?? true} // Default to true if undefined
          onChange={(e) => updateAgentConfig(agentIndex, { internetEnabled: e.target.checked })}
          className="h-4 w-4 text-primary focus:ring-primary border-gray-300 dark:border-gray-600 rounded dark:bg-gray-700 dark:checked:bg-primary"
        />
        <label htmlFor={`agent-${agentNumber}-internet`} className="ml-2 block text-sm text-gray-700 dark:text-gray-300">
          Enable Internet Access for this Agent
        </label>
      </div>

      {/* LLM Parameters */}
      <div className="grid grid-cols-2 gap-x-4 gap-y-3 mt-4 border-t border-gray-200 dark:border-gray-700 pt-3">
        {/* Temperature */}
        <div>
          <label htmlFor={`agent-${agentNumber}-temp`} className="block text-xs font-medium text-gray-600 dark:text-gray-400">
            Temperature ({agentConfig.temperature?.toFixed(2) ?? '1.00'})
          </label>
          <input
            type="range"
            id={`agent-${agentNumber}-temp`}
            min="0"
            max="2"
            step="0.01"
            value={agentConfig.temperature ?? 1.0}
            onChange={(e) => updateAgentConfig(agentIndex, { temperature: parseFloat(e.target.value) })}
            className="w-full h-2 bg-gray-200 dark:bg-gray-600 rounded-lg appearance-none cursor-pointer range-sm"
          />
        </div>

        {/* Max Tokens */}
        <div>
          <label htmlFor={`agent-${agentNumber}-maxTokens`} className="block text-xs font-medium text-gray-600 dark:text-gray-400">
            Max Tokens
          </label>
          <input
            type="number"
            id={`agent-${agentNumber}-maxTokens`}
            min="1"
            step="1"
            value={agentConfig.maxTokens ?? ''} // Use empty string if undefined for placeholder
            onChange={(e) => updateAgentConfig(agentIndex, { maxTokens: e.target.value ? parseInt(e.target.value, 10) : undefined })}
            placeholder="Default (e.g., 4096)"
            className="w-full p-1 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-white text-xs"
          />
        </div>

        {/* Top P */}
        <div>
          <label htmlFor={`agent-${agentNumber}-topP`} className="block text-xs font-medium text-gray-600 dark:text-gray-400">
            Top P ({agentConfig.topP?.toFixed(2) ?? '1.00'})
          </label>
          <input
            type="range"
            id={`agent-${agentNumber}-topP`}
            min="0"
            max="1"
            step="0.01"
            value={agentConfig.topP ?? 1.0}
            onChange={(e) => updateAgentConfig(agentIndex, { topP: parseFloat(e.target.value) })}
            className="w-full h-2 bg-gray-200 dark:bg-gray-600 rounded-lg appearance-none cursor-pointer range-sm"
          />
        </div>

        {/* Presence Penalty */}
        <div>
          <label htmlFor={`agent-${agentNumber}-presence`} className="block text-xs font-medium text-gray-600 dark:text-gray-400">
            Presence Penalty ({agentConfig.presencePenalty?.toFixed(2) ?? '0.00'})
          </label>
          <input
            type="range"
            id={`agent-${agentNumber}-presence`}
            min="-2"
            max="2"
            step="0.01"
            value={agentConfig.presencePenalty ?? 0.0}
            onChange={(e) => updateAgentConfig(agentIndex, { presencePenalty: parseFloat(e.target.value) })}
            className="w-full h-2 bg-gray-200 dark:bg-gray-600 rounded-lg appearance-none cursor-pointer range-sm"
          />
        </div>

        {/* Frequency Penalty */}
         <div>
          <label htmlFor={`agent-${agentNumber}-frequency`} className="block text-xs font-medium text-gray-600 dark:text-gray-400">
            Frequency Penalty ({agentConfig.frequencyPenalty?.toFixed(2) ?? '0.00'})
          </label>
          <input
            type="range"
            id={`agent-${agentNumber}-frequency`}
            min="-2"
            max="2"
            step="0.01"
            value={agentConfig.frequencyPenalty ?? 0.0}
            onChange={(e) => updateAgentConfig(agentIndex, { frequencyPenalty: parseFloat(e.target.value) })}
          className="w-full h-2 bg-gray-200 dark:bg-gray-600 rounded-lg appearance-none cursor-pointer range-sm"
          />
        {/* OpenRouter Advanced Parameters */}
        {currentProvider && currentProvider.toLowerCase() === 'openrouter' && (
          <div className="col-span-2 mt-4 p-5">
            <h4 className="text-sm font-semibold text-blue-800 dark:text-blue-200 mb-4 flex items-center">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M11.49 3.17c-.38-1.56-2.6-1.56-2.98 0a1.532 1.532 0 01-2.286.948c-1.372-.836-2.942.734-2.106 2.106.54.886.061 2.042-.947 2.287-1.561.379-1.561 2.6 0 2.978a1.532 1.532 0 01.947 2.287c-.836 1.372.734 2.942 2.106 2.106a1.532 1.532 0 012.287.947c.379 1.561 2.6 1.561 2.978 0a1.533 1.533 0 012.287-.947c1.372.836 2.942-.734 2.106-2.106a1.533 1.533 0 01.947-2.287c1.561-.379 1.561-2.6 0-2.978a1.532 1.532 0 01-.947-2.287c.836-1.372-.734-2.942-2.106-2.106a1.532 1.532 0 01-2.287-.947zM10 13a3 3 0 100-6 3 3 0 000 6z" clipRule="evenodd" />
              </svg>
            </h4>
            <div className="grid grid-cols-2 gap-x-6 gap-y-3 w-96">
              {/* Top K */}
              <div className="w-full">
                <label htmlFor={`agent-${agentNumber}-topK`} className="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">Top K</label>
                <input
                  id={`agent-${agentNumber}-topK`}
                  type="number"
                  min="0"
                  step="1"
                  value={agentConfig.topK ?? 0}
                  onChange={e => updateAgentConfig(agentIndex, { topK: parseInt(e.target.value) })}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm bg-white dark:bg-gray-800 focus:ring-blue-500 focus:border-blue-500 transition-colors"
                />
              </div>
              {/* Repetition Penalty */}
              <div className="w-full">
                <label htmlFor={`agent-${agentNumber}-repetition-penalty`} className="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">Repetition Penalty</label>
                <input
                  id={`agent-${agentNumber}-repetition-penalty`}
                  type="number"
                  min="0"
                  max="2"
                  step="0.01"
                  value={agentConfig.repetitionPenalty ?? 1.0}
                  onChange={e => updateAgentConfig(agentIndex, { repetitionPenalty: parseFloat(e.target.value) })}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm bg-white dark:bg-gray-800 focus:ring-blue-500 focus:border-blue-500 transition-colors"
                />
              </div>
              {/* Min P */}
              <div className="w-full">
                <label htmlFor={`agent-${agentNumber}-minP`} className="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">Min P</label>
                <input
                  id={`agent-${agentNumber}-minP`}
                  type="number"
                  min="0"
                  max="1"
                  step="0.01"
                  value={agentConfig.minP ?? 0.0}
                  onChange={e => updateAgentConfig(agentIndex, { minP: parseFloat(e.target.value) })}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm bg-white dark:bg-gray-800 focus:ring-blue-500 focus:border-blue-500 transition-colors"
                />
              </div>
              {/* Top A */}
              <div className="w-full">
                <label htmlFor={`agent-${agentNumber}-topA`} className="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">Top A</label>
                <input
                  id={`agent-${agentNumber}-topA`}
                  type="number"
                  min="0"
                  max="1"
                  step="0.01"
                  value={agentConfig.topA ?? 0.0}
                  onChange={e => updateAgentConfig(agentIndex, { topA: parseFloat(e.target.value) })}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm bg-white dark:bg-gray-800 focus:ring-blue-500 focus:border-blue-500 transition-colors"
                />
              </div>
              {/* Seed */}
              <div className="w-full">
                <label htmlFor={`agent-${agentNumber}-seed`} className="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">Seed</label>
                <input
                  id={`agent-${agentNumber}-seed`}
                  type="number"
                  step="1"
                  value={agentConfig.seed ?? ''}
                  onChange={e => updateAgentConfig(agentIndex, { seed: e.target.value ? parseInt(e.target.value) : undefined })}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm bg-white dark:bg-gray-800 focus:ring-blue-500 focus:border-blue-500 transition-colors"
                />
              </div>
              {/* Top Logprobs */}
              <div className="w-full">
                <label htmlFor={`agent-${agentNumber}-top-logprobs`} className="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">Top Logprobs</label>
                <input
                  id={`agent-${agentNumber}-top-logprobs`}
                  type="number"
                  min="0"
                  max="20"
                  step="1"
                  value={agentConfig.topLogprobs ?? ''}
                  onChange={e => updateAgentConfig(agentIndex, { topLogprobs: e.target.value ? parseInt(e.target.value) : undefined })}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm bg-white dark:bg-gray-800 focus:ring-blue-500 focus:border-blue-500 transition-colors"
                />
              </div>
              {/* Logprobs */}
              <div className="flex items-center mt-5 w-full">
                <input
                  id={`agent-${agentNumber}-logprobs`}
                  type="checkbox"
                  checked={!!agentConfig.logprobs}
                  onChange={e => updateAgentConfig(agentIndex, { logprobs: e.target.checked })}
                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded transition-colors mr-2"
                />
                <label htmlFor={`agent-${agentNumber}-logprobs`} className="text-xs font-medium text-gray-700 dark:text-gray-300">Logprobs</label>
              </div>
              {/* Structured Outputs */}
              <div className="flex items-center mt-5 w-full">
                <input
                  id={`agent-${agentNumber}-structured-outputs`}
                  type="checkbox"
                  checked={!!agentConfig.structuredOutputs}
                  onChange={e => updateAgentConfig(agentIndex, { structuredOutputs: e.target.checked })}
                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded transition-colors mr-2"
                />
                <label htmlFor={`agent-${agentNumber}-structured-outputs`} className="text-xs font-medium text-gray-700 dark:text-gray-300">Structured Outputs</label>
              </div>
              {/* Logit Bias (JSON) */}
              <div className="col-span-2 w-full">
                <label htmlFor={`agent-${agentNumber}-logit-bias`} className="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">Logit Bias (JSON)</label>
                <input
                  id={`agent-${agentNumber}-logit-bias`}
                  type="text"
                  value={agentConfig.logitBias ? JSON.stringify(agentConfig.logitBias) : ''}
                  onChange={e => {
                    let val = {};
                    try { val = JSON.parse(e.target.value); } catch {}
                    updateAgentConfig(agentIndex, { logitBias: val });
                  }}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm bg-white dark:bg-gray-800 focus:ring-blue-500 focus:border-blue-500 transition-colors font-mono text-xs"
                  placeholder='e.g. {"123": -100, "456": 100}'
                />
              </div>
              {/* Response Format (JSON) */}
              <div className="col-span-2 w-full">
                <label htmlFor={`agent-${agentNumber}-response-format`} className="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">Response Format (JSON)</label>
                <input
                  id={`agent-${agentNumber}-response-format`}
                  type="text"
                  value={agentConfig.responseFormat ? JSON.stringify(agentConfig.responseFormat) : ''}
                  onChange={e => {
                    let val = {};
                    try { val = JSON.parse(e.target.value); } catch {}
                    updateAgentConfig(agentIndex, { responseFormat: val });
                  }}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm bg-white dark:bg-gray-800 focus:ring-blue-500 focus:border-blue-500 transition-colors font-mono text-xs"
                  placeholder='e.g. {"type": "json_object"}'
                />
              </div>
              {/* Stop (comma-separated tokens) */}
              <div className="col-span-2 w-full">
                <label htmlFor={`agent-${agentNumber}-stop`} className="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">Stop (comma-separated tokens)</label>
                <input
                  id={`agent-${agentNumber}-stop`}
                  type="text"
                  value={Array.isArray(agentConfig.stop) ? agentConfig.stop.join(',') : ''}
                  onChange={e => updateAgentConfig(agentIndex, { stop: e.target.value.split(',').map(s => s.trim()).filter(Boolean) })}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm bg-white dark:bg-gray-800 focus:ring-blue-500 focus:border-blue-500 transition-colors font-mono text-xs"
                  placeholder='e.g. <|end|>,<|stop|>'
                />
              </div>
              {/* Tool Choice (JSON) */}
              <div className="col-span-2 w-full">
                <label htmlFor={`agent-${agentNumber}-tool-choice`} className="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">Tool Choice (JSON)</label>
                <input
                  id={`agent-${agentNumber}-tool-choice`}
                  type="text"
                  value={agentConfig.toolChoice ? JSON.stringify(agentConfig.toolChoice) : ''}
                  onChange={e => {
                    let val = {};
                    try { val = JSON.parse(e.target.value); } catch {}
                    updateAgentConfig(agentIndex, { toolChoice: val });
                  }}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm bg-white dark:bg-gray-800 focus:ring-blue-500 focus:border-blue-500 transition-colors font-mono text-xs"
                  placeholder='e.g. {"type": "function", "function": {"name": "my_function"}}'
                />
              </div>
              {/* Max Price (JSON) */}
              <div className="col-span-2 w-full">
                <label htmlFor={`agent-${agentNumber}-max-price`} className="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">Max Price (JSON)</label>
                <input
                  id={`agent-${agentNumber}-max-price`}
                  type="text"
                  value={agentConfig.maxPrice ? JSON.stringify(agentConfig.maxPrice) : ''}
                  onChange={e => {
                    let val = {};
                    try { val = JSON.parse(e.target.value); } catch {}
                    updateAgentConfig(agentIndex, { maxPrice: val });
                  }}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm bg-white dark:bg-gray-800 focus:ring-blue-500 focus:border-blue-500 transition-colors font-mono text-xs"
                  placeholder='e.g. {"prompt": 1, "completion": 2}'
                />
              </div>
            </div>
          </div>
        )}
        </div>
      </div>

      {/* Instructions Textarea */}
      <div className="mt-4">
        <div className="flex justify-between items-center mb-1">
          <label htmlFor={`agent-${agentNumber}-instructions`} className="block text-sm font-medium text-gray-700 dark:text-gray-300">
            Instructions:
          </label>
          <div className="flex items-center">
            <input
              type="checkbox"
              id={`agent-${agentNumber}-use-default-instructions`}
              checked={agentConfig.useDefaultInstructions === true}
              onChange={(e) => updateAgentConfig(agentIndex, { useDefaultInstructions: e.target.checked })}
              className="h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded"
            />
            <label htmlFor={`agent-${agentNumber}-use-default-instructions`} className="ml-2 block text-xs text-gray-600 dark:text-gray-400">
              Use default instructions
            </label>
          </div>
        </div>
        <textarea
          id={`agent-${agentNumber}-instructions`}
          name={`agent-${agentNumber}-instructions`}
          rows={4}
          defaultValue={agentConfig.instructions} // Use defaultValue for debounced updates
          onChange={handleInstructionsChange}
          disabled={agentConfig.useDefaultInstructions === true}
          className={`w-full p-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-white resize-vertical ${agentConfig.useDefaultInstructions === true ? 'opacity-50 cursor-not-allowed' : ''}`}
          placeholder={agentConfig.useDefaultInstructions === true ? 'Using default instructions from system...' : `Enter specific instructions for Agent ${agentNumber}...`}
        ></textarea>
        {agentConfig.useDefaultInstructions === true && agentConfig.instructions && (
          <p className="text-xs text-amber-600 dark:text-amber-400 mt-1">
            Note: Custom instructions are saved but not being used while "Use default instructions" is checked.
          </p>
        )}
      </div>

       {/* Add Model Modal */}
       <Modal
           isOpen={isAddModelModalOpen}
           onClose={() => setIsAddModelModalOpen(false)}
           title={`Add Model for ${currentProvider || 'Provider'}`}
       >
           <div className="p-4">
               <label htmlFor="new-model-name" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                   New Model Name:
               </label>
               <input
                   type="text"
                   id="new-model-name"
                   value={newModelName}
                   onChange={(e) => setNewModelName(e.target.value)}
                   className="w-full p-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:ring-primary focus:border-primary dark:bg-gray-700 dark:text-white"
                   placeholder="e.g., my-custom-finetune-v1"
               />
               <div className="mt-4 flex justify-end space-x-2">
                   <button
                       type="button"
                       onClick={() => setIsAddModelModalOpen(false)}
                       className="px-4 py-2 bg-gray-200 dark:bg-gray-600 text-gray-800 dark:text-gray-200 rounded hover:bg-gray-300 dark:hover:bg-gray-500"
                   >
                       Cancel
                   </button>
                   <button
                       type="button"
                       onClick={handleConfirmAddModel}
                       className="px-4 py-2 bg-primary dark:bg-primary-dark text-white rounded hover:bg-primary-dark dark:hover:bg-primary-light disabled:opacity-50"
                       disabled={!newModelName.trim()}
                   >
                       Add Model
                   </button>
               </div>
           </div>
       </Modal>

       {/* Manage Models Modal */}
       <Modal
           isOpen={isManageModelsModalOpen}
           onClose={() => setIsManageModelsModalOpen(false)}
           title={`Manage Models for ${currentProvider || 'Provider'}`}
       >
           <div className="p-4 max-h-[60vh] overflow-y-auto"> {/* Limit height and add scroll */}
               {displayModels.length > 0 ? (
                   <ul className="space-y-2">
                       {displayModels.map((model) => (
                           <li key={model.id} className="flex justify-between items-center group bg-gray-50 dark:bg-gray-700 p-2 rounded">
                               <span className="text-sm text-gray-800 dark:text-gray-200 truncate pr-2 flex-grow">
                                   {lastSelectedModel === model.id ? '⭐ ' : ''}{model.name}
                                   {addedModels.includes(model.id) && <span className="text-xs text-gray-500 dark:text-gray-400 ml-1">(User Added)</span>} {/* Indicator */}
                               </span>
                               <button
                                   type="button"
                                   onClick={async () => { // Make async to await save
                                       if (currentProvider) {
                                           console.log(`Attempting to remove model: ${model.id} for provider: ${currentProvider}`);
                                           removeUserModel(currentProvider, model.id);
                                           // If the currently selected model was the one removed, clear it
                                           if (agentConfig?.provider === currentProvider && agentConfig?.model === model.id) {
                                               updateAgentConfig(agentIndex, { model: '' });
                                               // Also clear last selected if it was the removed one
                                               setLastSelectedModel(currentProvider, '');
                                           }
                                           // Trigger save and handle potential errors
                                           if (currentProfileId) {
                                               try {
                                                   await saveCurrentProfile();
                                                   console.log(`Profile saved after removing model ${model}.`);
                                                   // Optionally provide success feedback specific to this action if needed
                                               } catch (err: any) {
                                                   console.error(`Failed to auto-save profile after removing model ${model}:`, err);
                                                   setGlobalError(`Failed to auto-save profile after removing model: ${err.message}`);
                                                   // Re-add the model visually if save failed? Or rely on global error.
                                                   // For simplicity, rely on global error for now.
                                               }
                                           } else {
                                               console.warn("No profile loaded. Model removal will be saved when the profile is next saved.");
                                           }
                                           // Modal remains open for multiple removals unless closed manually
                                       }
                                   }}
                                   className="text-xs text-red-600 hover:text-red-800 dark:text-red-400 dark:hover:text-red-300 font-medium px-2 py-1 rounded bg-red-100 dark:bg-red-900/50 dark:hover:bg-red-800/60 opacity-80 group-hover:opacity-100 transition-opacity"
                                   title={`Hide model "${model}" from list`} // Clarify action
                               >
                                   Hide {/* Change text to 'Hide' as it adds to removed list */}
                               </button>
                           </li>
                       ))}
                   </ul>
               ) : (
                   <p className="text-sm text-gray-500 dark:text-gray-400">No models to manage for this provider.</p>
               )}
               <div className="mt-4 flex justify-end">
                   <button
                       type="button"
                       onClick={() => setIsManageModelsModalOpen(false)}
                       className="px-4 py-2 bg-gray-200 dark:bg-gray-600 text-gray-800 dark:text-gray-200 rounded hover:bg-gray-300 dark:hover:bg-gray-500"
                   >
                       Close
                   </button>
               </div>
           </div>
       </Modal>
    </div>
  );
};

export default AgentConfigCard;
