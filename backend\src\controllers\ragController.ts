import { Request, Response } from 'express';
import path from 'path';
import fs from 'fs/promises';
import { ragHandlerInstance } from '../services/agentOrchestrator'; // Adjust path if needed
import { Dirent } from 'fs';

// --- Configuration ---
// Define the absolute root path where knowledge bases are allowed to exist.
// Resolve relative to the project root (adjust '../..' as needed based on file location)
const ALLOWED_BASE_PATH = path.resolve(__dirname, '../..'); // Example: Project root
const KNOWLEDGE_BASE_ROOT = path.resolve(ALLOWED_BASE_PATH, './knowledge_bases'); // Default KB location within base path
const DEFAULT_KB_PATH = path.join(KNOWLEDGE_BASE_ROOT, 'default_kb'); // Example default KB name/path

// Ensure the root knowledge base directory and default KB exist on startup
(async () => {
    try {
        // Create the root knowledge base directory
        await fs.mkdir(KNOWLEDGE_BASE_ROOT, { recursive: true });
        console.log(`Ensured Knowledge Base root directory exists: ${KNOWLEDGE_BASE_ROOT}`);

        // Also ensure default knowledge base exists
        await fs.mkdir(DEFAULT_KB_PATH, { recursive: true });
        console.log(`Ensured default Knowledge Base directory exists: ${DEFAULT_KB_PATH}`);

        // Create empty index files if they don't exist
        const faissIndexPath = path.join(DEFAULT_KB_PATH, 'faiss_index.bin');
        const embeddingsCachePath = path.join(DEFAULT_KB_PATH, 'embeddings_cache.bin');
        const metadataJsonPath = path.join(DEFAULT_KB_PATH, 'index_metadata.json');

        try {
            await fs.access(faissIndexPath);
        } catch (e) {
            // Create empty FAISS index file
            await fs.writeFile(faissIndexPath, '');
            console.log(`Created empty FAISS index file: ${faissIndexPath}`);
        }

        try {
            await fs.access(embeddingsCachePath);
        } catch (e) {
            // Create empty embeddings cache file
            await fs.writeFile(embeddingsCachePath, '');
            console.log(`Created empty embeddings cache file: ${embeddingsCachePath}`);
        }

        try {
            await fs.access(metadataJsonPath);
        } catch (e) {
            // Create empty metadata JSON file
            await fs.writeFile(metadataJsonPath, '{}');
            console.log(`Created empty metadata JSON file: ${metadataJsonPath}`);
        }
    } catch (error) {
        console.error(`FATAL: Could not create Knowledge Base directories: ${KNOWLEDGE_BASE_ROOT}`, error);
        process.exit(1);
    }
})();

// --- Helper Function for Secure Path Handling ---
/**
 * Resolves a user-provided path relative to the KNOWLEDGE_BASE_ROOT
 * and validates that it stays within the ALLOWED_BASE_PATH.
 * Throws an error if validation fails.
 * @param userPath - The path provided by the user.
 * @param checkExists - If true, checks if the resolved path exists.
 * @returns The absolute, validated path.
 */
const resolveAndValidatePath = async (userPath: string, checkExists: boolean = false): Promise<string> => {
    if (!userPath || typeof userPath !== 'string') {
        throw new Error('Invalid path provided.');
    }

    // Resolve the path relative to the KNOWLEDGE_BASE_ROOT. Handles ".", "..", etc.
    // This assumes user paths like 'my_kb' or 'group/my_kb' should be inside KNOWLEDGE_BASE_ROOT.
    // If users can provide absolute paths, the logic needs adjustment and stricter validation.
    const resolvedPath = path.resolve(KNOWLEDGE_BASE_ROOT, userPath);

    // SECURITY CHECK: Ensure the resolved path is still within the allowed base path.
    // This prevents traversal attacks like "../../../etc/passwd".
    if (!resolvedPath.startsWith(ALLOWED_BASE_PATH)) {
        console.warn(`Path traversal attempt blocked: UserPath='${userPath}', Resolved='${resolvedPath}', Base='${ALLOWED_BASE_PATH}'`);
        throw new Error('Access denied: Path is outside the allowed directory.');
    }

    if (checkExists) {
        try {
            await fs.access(resolvedPath);
        } catch (error) {
            throw new Error(`Path does not exist or is not accessible: ${resolvedPath}`);
        }
    }

    return resolvedPath;
};


// --- Controller Functions ---

// @desc    Upload a file for RAG indexing
// @route   POST /api/rag/upload
// @access  Public (adjust later with auth)
export const uploadFileForRag = async (req: Request, res: Response): Promise<void> => {
    // req.files is an array when using upload.array()
    if (!req.files || (req.files as Express.Multer.File[]).length === 0) {
        res.status(400).json({ message: 'No files uploaded.' });
        return;
    }

    const files = req.files as Express.Multer.File[];
    const results = [];
    let overallSuccess = true;

    console.log(`Received ${files.length} files for RAG processing.`);

    for (const uploadedFile of files) {
        const tempFilePath = uploadedFile.path;
        console.log(`Processing uploaded file: ${uploadedFile.originalname} (temp: ${tempFilePath})`);
        try {
            // Process the file using RagHandler
            await ragHandlerInstance.addFile(tempFilePath); // addFile now handles cleanup internally on success

            results.push({
                filename: uploadedFile.originalname,
                success: true,
                message: `File '${uploadedFile.originalname}' processed successfully.`
            });
            // **No manual unlink here** - addFile should handle it or move the file

        } catch (error: any) {
            console.error(`Error processing uploaded file ${uploadedFile.originalname}:`, error);
            // Attempt cleanup on error *if file still exists*
            try { await fs.unlink(tempFilePath); } catch (unlinkError: any) { /* Ignore if already gone */ }

            overallSuccess = false;
            results.push({
                filename: uploadedFile.originalname,
                success: false,
                message: error.message || 'Server error processing file.'
            });
        }
    } // End loop through files

    // Send consolidated response
    res.status(overallSuccess ? 200 : 207) // 207 Multi-Status if some failed
       .json({
           message: `Processed ${files.length} files.`,
           results: results
       });
};

// @desc    List unique source filenames from the indexed documents
// @route   GET /api/rag/files
// @access  Public (adjust later with auth)
export const listIndexedFiles = async (req: Request, res: Response): Promise<void> => {
    try {
        // Assumes the handler returns sources relative to the current KB
        const uniqueSources = await ragHandlerInstance.getUniqueSources();
        res.status(200).json({ files: uniqueSources });
    } catch (error: any) {
        console.error('Error listing indexed files:', error);
        res.status(500).json({ message: 'Server error listing files.', error: error.message });
    }
};

// @desc    Delete an indexed file and its associated chunks/vectors
// @route   DELETE /api/rag/files/:filename
// @access  Public (adjust later with auth)
export const deleteIndexedFile = async (req: Request, res: Response): Promise<void> => {
    // Decode filename from URL parameter to handle special characters/paths
    const filenameParam = req.params.filename;
    if (!filenameParam) {
        res.status(400).json({ message: 'Filename parameter is required.' });
        return;
    }

    let decodedFilename: string;
    try {
        decodedFilename = decodeURIComponent(filenameParam);
    } catch (e) {
        res.status(400).json({ message: 'Invalid filename encoding in URL.' });
        return;
    }

    // SECURITY: While `deleteFile` should handle internal logic safely,
    // avoid passing potentially malicious decoded strings directly if possible.
    // `deleteFile` should ideally work with relative paths within the KB.

    console.log(`Request received to delete file: ${decodedFilename}`);
    try {
        // Call the deletion logic in RagHandler
        const success = await ragHandlerInstance.deleteFile(decodedFilename); // Assumes handler interprets filename relative to KB

        if (success) {
            res.status(200).json({ message: `File '${decodedFilename}' and its data successfully removed from the knowledge base.` });
        } else {
            // If deletion wasn't successful (e.g., file not found by handler)
            res.status(404).json({ message: `File '${decodedFilename}' not found in the current knowledge base or deletion failed.` });
        }

    } catch (error: any) {
        console.error(`Error deleting indexed file ${decodedFilename}:`, error);
        res.status(500).json({ message: 'Server error deleting file.', error: error.message });
    }
};

// @desc    Get current knowledge base path and storage type
// @route   GET /api/rag/kb-path
// @access  Public (adjust later with auth)
export const getKnowledgeBasePath = async (req: Request, res: Response): Promise<void> => {
    try {
        const kbPath = ragHandlerInstance.getKnowledgeBasePath();
        const storageType = ragHandlerInstance.getStorageType(); // Assuming handler has this method
        // Return path relative to KNOWLEDGE_BASE_ROOT for user display consistency
        const relativePath = path.relative(KNOWLEDGE_BASE_ROOT, kbPath) || '.'; // Use '.' if it's the root itself
        res.status(200).json({ path: relativePath, storageType });
    } catch (error: any) {
        console.error('Error getting knowledge base path:', error);
        res.status(500).json({ message: 'Server error getting knowledge base path.', error: error.message });
    }
};

// @desc    Set knowledge base path
// @route   POST /api/rag/kb-path
// @access  Public (adjust later with auth)
export const setKnowledgeBasePath = async (req: Request, res: Response): Promise<void> => {
    const { path: userPath, storageType = 'local' } = req.body; // Default storage type if not provided

    if (!userPath) {
        res.status(400).json({ message: 'Path parameter is required.' });
        return;
    }
    if (storageType !== 'local' && storageType !== 'server') {
        res.status(400).json({ message: 'Invalid storageType. Must be "local" or "server".' });
        return;
    }

    try {
        // Resolve and validate the user-provided path securely
        // This resolves relative to KNOWLEDGE_BASE_ROOT and checks bounds
        const absoluteKbPath = await resolveAndValidatePath(userPath);

        // Ensure the target directory exists, create if not (safely within bounds)
        try {
            const stats = await fs.stat(absoluteKbPath);
            if (!stats.isDirectory()) {
                res.status(400).json({ message: `'${userPath}' resolves to an existing file, not a directory.` });
                return;
            }
        } catch (error: any) {
            if (error.code === 'ENOENT') { // Directory doesn't exist
                console.log(`Directory does not exist, creating: ${absoluteKbPath}`);
                await fs.mkdir(absoluteKbPath, { recursive: true });
            } else {
                throw error; // Re-throw other fs.stat errors
            }
        }

        // Set the path in the handler (pass the absolute, validated path)
        const success = await ragHandlerInstance.setKnowledgeBasePath(absoluteKbPath, storageType);

        if (success) {
             // Return path relative to KNOWLEDGE_BASE_ROOT for consistency
            const relativePath = path.relative(KNOWLEDGE_BASE_ROOT, absoluteKbPath) || '.';
            res.status(200).json({
                message: `Knowledge base path successfully set to '${userPath}'.`,
                path: relativePath, // Return the relative path user expects
                storageType
            });
        } else {
            // This case might indicate an internal handler issue if validation passed
            res.status(500).json({ message: `Handler failed to set knowledge base path to '${userPath}'.` });
        }
    } catch (error: any) {
        console.error(`Error setting knowledge base path to ${userPath}:`, error);
        // Distinguish validation errors from server errors
        if (error.message.startsWith('Access denied') || error.message.startsWith('Invalid path')) {
            res.status(403).json({ message: error.message });
        } else if (error.message.includes('not accessible')) {
            res.status(404).json({ message: error.message });
        } else {
            res.status(500).json({ message: 'Server error setting knowledge base path.', error: error.message });
        }
    }
};

// @desc    List available knowledge bases
// @route   GET /api/rag/kb-list
// @access  Public (adjust later with auth)
export const listKnowledgeBases = async (req: Request, res: Response): Promise<void> => {
    const storageType = req.query.storageType === 'server' ? 'server' : 'local'; // Default to local

    try {
        // Pass storageType if handler's listing logic depends on it
        const kbList = await ragHandlerInstance.listKnowledgeBases(storageType);
         // Ensure paths are relative to KNOWLEDGE_BASE_ROOT
        const relativeKbList = kbList.map(kbPath => path.relative(KNOWLEDGE_BASE_ROOT, kbPath) || '.');
        res.status(200).json({ knowledgeBases: relativeKbList });
    } catch (error: any) {
        console.error('Error listing knowledge bases:', error);
        res.status(500).json({ message: 'Server error listing knowledge bases.', error: error.message });
    }
};

// @desc    Create a new knowledge base
// @route   POST /api/rag/kb-create
// @access  Public (adjust later with auth)
export const createKnowledgeBase = async (req: Request, res: Response): Promise<void> => {
    const { name: kbName, storageType = 'local' } = req.body;

    if (!kbName) {
        res.status(400).json({ message: 'Name parameter is required.' });
        return;
    }
    if (storageType !== 'local' && storageType !== 'server') {
        res.status(400).json({ message: 'Invalid storageType. Must be "local" or "server".' });
        return;
    }

    // SECURITY: The handler's createKnowledgeBase method MUST perform validation
    // to ensure kbName doesn't contain traversal chars and resolves safely within KNOWLEDGE_BASE_ROOT.
    console.log(`Request to create knowledge base: ${kbName} (Type: ${storageType})`);
    try {
        // First, ensure the knowledge base root directory exists
        try {
            await fs.mkdir(KNOWLEDGE_BASE_ROOT, { recursive: true });
            console.log(`Ensured Knowledge Base root directory exists: ${KNOWLEDGE_BASE_ROOT}`);
        } catch (mkdirError) {
            console.error(`Error creating Knowledge Base root directory: ${KNOWLEDGE_BASE_ROOT}`, mkdirError);
            throw mkdirError; // Re-throw to be caught by the outer try/catch
        }

        // Resolve the path for the new knowledge base
        const newKbPath = path.resolve(KNOWLEDGE_BASE_ROOT, kbName);

        // Check if it already exists
        try {
            const stats = await fs.stat(newKbPath);
            if (stats.isDirectory()) {
                // Directory already exists, return success
                res.status(200).json({
                    message: `Knowledge base '${kbName}' already exists.`,
                    name: kbName,
                    storageType
                });
                return;
            }
        } catch (statError: any) {
            // If the error is ENOENT, the directory doesn't exist, which is what we want
            if (statError.code !== 'ENOENT') {
                throw statError; // Re-throw unexpected errors
            }
        }

        // Create the directory
        await fs.mkdir(newKbPath, { recursive: true });
        console.log(`Created knowledge base directory: ${newKbPath}`);

        // Create empty index files
        const faissIndexPath = path.join(newKbPath, 'faiss_index.bin');
        const embeddingsCachePath = path.join(newKbPath, 'embeddings_cache.bin');
        const metadataJsonPath = path.join(newKbPath, 'index_metadata.json');
        const sqlitePath = path.join(newKbPath, 'metadata.sqlite');

        try {
            await fs.writeFile(faissIndexPath, '');
            await fs.writeFile(embeddingsCachePath, '');
            await fs.writeFile(metadataJsonPath, '{}');
            // SQLite file will be created by the handler when needed
            console.log(`Created empty index files in ${newKbPath}`);
        } catch (fileError) {
            console.error(`Error creating index files in ${newKbPath}:`, fileError);
            throw fileError;
        }

        // Now use the handler to initialize the knowledge base
        const success = await ragHandlerInstance.createKnowledgeBase(kbName, storageType);

        if (success) {
            res.status(201).json({ // 201 Created
                message: `Knowledge base '${kbName}' successfully created.`,
                name: kbName, // Return the name provided
                storageType
            });
        } else {
            // Could be "already exists" or other failure
            res.status(409).json({ message: `Failed to create knowledge base '${kbName}'. It may already exist or the name is invalid.` }); // 409 Conflict
        }
    } catch (error: any) {
        console.error(`Error creating knowledge base '${kbName}':`, error);
        // Check for specific validation errors from the handler if possible
        if (error.message?.startsWith('Access denied') || error.message?.startsWith('Invalid name')) {
            res.status(400).json({ message: error.message });
        } else {
            res.status(500).json({ message: 'Server error creating knowledge base.', error: error.message });
        }
    }
};

// @desc    Remove a knowledge base
// @route   DELETE /api/rag/kb-remove
// @access  Public (adjust later with auth)
export const removeKnowledgeBase = async (req: Request, res: Response): Promise<void> => {
    const { path: userPath, storageType = 'local' } = req.body;

    if (!userPath) {
        res.status(400).json({ message: 'Path parameter is required.' });
        return;
    }
     if (storageType !== 'local' && storageType !== 'server') {
        res.status(400).json({ message: 'Invalid storageType. Must be "local" or "server".' });
        return;
    }

    console.log(`Request received to remove knowledge base: ${userPath}`);
    try {
        // Resolve and validate the path FIRST before doing anything else
        const absoluteKbPath = await resolveAndValidatePath(userPath, true); // Check existence

        // Prevent deleting the root KB directory itself (optional safeguard)
        if (absoluteKbPath === KNOWLEDGE_BASE_ROOT) {
             res.status(400).json({ message: 'Cannot remove the root knowledge base directory.' });
             return;
        }

        // If the current knowledge base is being removed, attempt to switch to the default FIRST
        const currentKbPath = ragHandlerInstance.getKnowledgeBasePath();
        if (currentKbPath === absoluteKbPath) {
            console.log(`Removing active KB. Attempting to switch to default: ${DEFAULT_KB_PATH}`);
            try {
                 // Ensure default exists or can be created before switching
                 await fs.mkdir(DEFAULT_KB_PATH, { recursive: true });
                 await ragHandlerInstance.setKnowledgeBasePath(DEFAULT_KB_PATH, storageType);
            } catch (switchError: any) {
                 console.error(`Failed to switch to default KB ${DEFAULT_KB_PATH} before removing ${absoluteKbPath}:`, switchError);
                 // Decide whether to proceed with removal or stop
                 // For now, we stop to avoid leaving the handler in a bad state
                 res.status(500).json({ message: 'Failed to switch to default knowledge base before removal. Aborting removal.', error: switchError.message });
                 return;
            }
        }

        // Delegate the actual removal (FS operation + internal cleanup) to the handler
        const success = await ragHandlerInstance.removeKnowledgeBase(absoluteKbPath, storageType);

        if (success) {
            res.status(200).json({
                message: `Knowledge base '${userPath}' successfully removed.`,
                path: userPath // Return the user-provided path for confirmation
            });
        } else {
            // Handler indicated failure (might be permissions, concurrent access, etc.)
             res.status(500).json({ message: `Handler failed to remove knowledge base '${userPath}'. Check server logs.` });
        }

    } catch (error: any) {
        console.error(`Error removing knowledge base ${userPath}:`, error);
        if (error.message.startsWith('Access denied') || error.message.startsWith('Invalid path')) {
            res.status(403).json({ message: error.message });
        } else if (error.message.includes('not accessible') || error.message.includes('Path does not exist')) {
             res.status(404).json({ message: `Knowledge base '${userPath}' not found or not accessible.` });
        } else {
            res.status(500).json({ message: 'Server error removing knowledge base.', error: error.message });
        }
    }
};

// @desc    Re-index all files in the current knowledge base
// @route   POST /api/rag/reindex
// @access  Public (adjust later with auth)
export const reindexKnowledgeBase = async (req: Request, res: Response): Promise<void> => {
    console.log("Request received to re-index current knowledge base.");
    try {
        // Let the handler manage getting the current path and performing re-index
        const results = await ragHandlerInstance.reindexKnowledgeBase();

        const successCount = results.successCount ?? 0; // Use nullish coalescing
        const failureCount = results.failureCount ?? 0;

        res.status(200).json({
            message: `Re-indexing complete. Processed ${successCount} files, failed ${failureCount} files.`,
            knowledgeBasePath: path.relative(KNOWLEDGE_BASE_ROOT, results.knowledgeBasePath) || '.', // Return relative path
            successCount,
            failureCount,
            results: results.results // Include detailed results if provided by handler
        });
    } catch (error: any) {
        console.error('Error re-indexing knowledge base:', error);
        res.status(500).json({ message: 'Server error re-indexing knowledge base.', error: error.message });
    }
};

// @desc    List directories within a specified path (relative to KB root)
// @route   GET /api/rag/list-directories
// @access  Public (adjust later with auth)
export const listDirectories = async (req: Request, res: Response): Promise<void> => {
    const { path: userDirPath = '.' } = req.query; // Default to current KB root '.'
    const storageType = req.query.storageType === 'server' ? 'server' : 'local';

    if (typeof userDirPath !== 'string') {
        res.status(400).json({ message: 'Path parameter must be a string.' });
        return;
    }

    try {
        // Resolve and validate the path relative to KNOWLEDGE_BASE_ROOT
        const absoluteDirPath = await resolveAndValidatePath(userDirPath, true); // Check existence

        // Check if it's actually a directory
        const stats = await fs.stat(absoluteDirPath);
        if (!stats.isDirectory()) {
            res.status(400).json({ message: `'${userDirPath}' is not a directory.` });
            return;
        }

        // Delegate listing to handler if logic depends on storageType or needs more complexity
        // Otherwise, perform basic FS listing here (as it was before)
        const entries = await fs.readdir(absoluteDirPath, { withFileTypes: true });
        const directories = entries
            .filter(entry => entry.isDirectory())
            .map(entry => {
                const absoluteEntryPath = path.join(absoluteDirPath, entry.name);
                // Return path relative to the KNOWLEDGE_BASE_ROOT
                const relativePath = path.relative(KNOWLEDGE_BASE_ROOT, absoluteEntryPath).replace(/\\/g, '/');
                return {
                    name: entry.name,
                    path: relativePath
                };
            });

        res.status(200).json({
            // Return the path relative to KNOWLEDGE_BASE_ROOT for currentPath too
            currentPath: path.relative(KNOWLEDGE_BASE_ROOT, absoluteDirPath).replace(/\\/g, '/') || '.',
            directories
        });
    } catch (error: any) {
        console.error(`Error listing directories in '${userDirPath}':`, error);
         if (error.message.startsWith('Access denied') || error.message.startsWith('Invalid path')) {
            res.status(403).json({ message: error.message });
        } else if (error.message.includes('not accessible') || error.message.includes('Path does not exist')) {
             res.status(404).json({ message: `Directory '${userDirPath}' not found or not accessible.` });
        } else {
            res.status(500).json({ message: 'Server error listing directories.', error: error.message });
        }
    }
};

// @desc    Upload and process files from a specified directory
// @route   POST /api/rag/upload-directory
// @access  Public (adjust later with auth)
export const uploadDirectoryForRag = async (req: Request, res: Response): Promise<void> => {
    const { directoryPath: userDirPath, recursive = true, storageType = 'local' } = req.body;

    if (!userDirPath) {
        res.status(400).json({ message: 'directoryPath parameter is required.' });
        return;
    }
    if (storageType !== 'local' && storageType !== 'server') {
        res.status(400).json({ message: 'Invalid storageType. Must be "local" or "server".' });
        return;
    }

    console.log(`Request to process directory: ${userDirPath} (Recursive: ${recursive}, Type: ${storageType})`);
    try {
        // Resolve and validate path relative to KB root
        const absoluteDirPath = await resolveAndValidatePath(userDirPath, true); // Check existence

        // Ensure it's a directory
        const stats = await fs.stat(absoluteDirPath);
        if (!stats.isDirectory()) {
             res.status(400).json({ message: `'${userDirPath}' is not a directory.` });
            return;
        }

        // Process the directory using the handler (pass absolute path)
        // Assumes handler's addDirectory returns { successCount: number, failureCount: number, results: object }
        const results = await ragHandlerInstance.addDirectory(absoluteDirPath, recursive, storageType);

        const successCount = results.successCount ?? 0;
        const failureCount = results.failureCount ?? 0;

        res.status(200).json({
            message: `Directory processing complete. Successfully processed ${successCount} files, failed ${failureCount} files.`,
            directoryPath: userDirPath, // Return user-provided path
            successCount,
            failureCount,
            storageType,
            results: results.results // Include details if provided
        });
    } catch (error: any) {
        console.error(`Error processing directory '${userDirPath}':`, error);
        if (error.message.startsWith('Access denied') || error.message.startsWith('Invalid path')) {
            res.status(403).json({ message: error.message });
        } else if (error.message.includes('not accessible') || error.message.includes('Path does not exist')) {
             res.status(404).json({ message: `Directory '${userDirPath}' not found or not accessible.` });
        } else {
            res.status(500).json({ message: 'Server error processing directory.', error: error.message });
        }
    }
};

// @desc    List both files and directories within a specified path (relative to KB root)
// @route   GET /api/rag/list-path-contents
// @access  Public (adjust later with auth)
export const listPathContents = async (req: Request, res: Response): Promise<void> => {
    const { path: userDirPath = '.' } = req.query; // Default to current KB root '.'
    const storageType = req.query.storageType === 'server' ? 'server' : 'local';

    if (typeof userDirPath !== 'string') {
        res.status(400).json({ message: 'Path parameter must be a string.' });
        return;
    }

    console.log(`Listing path contents for: '${userDirPath}'`);

    try {
        // Normalize path to handle './' and '.' consistently
        const normalizedPath = userDirPath === './' ? '.' : userDirPath;

        // Ensure the knowledge_bases directory exists
        try {
            await fs.mkdir(KNOWLEDGE_BASE_ROOT, { recursive: true });
        } catch (mkdirError) {
            console.error(`Error ensuring knowledge_bases directory exists: ${mkdirError}`);
            // Continue anyway, as the error might be that it already exists
        }

        // Resolve and validate the path relative to KNOWLEDGE_BASE_ROOT
        const absoluteDirPath = await resolveAndValidatePath(normalizedPath, false); // Don't check existence yet

        // Check if the directory exists and create it if it doesn't
        try {
            const stats = await fs.stat(absoluteDirPath);
            if (!stats.isDirectory()) {
                res.status(400).json({ message: `'${userDirPath}' is not a directory.` });
                return;
            }
        } catch (statError: any) {
            if (statError.code === 'ENOENT') {
                // Directory doesn't exist, create it
                console.log(`Directory doesn't exist, creating: ${absoluteDirPath}`);
                try {
                    await fs.mkdir(absoluteDirPath, { recursive: true });
                } catch (mkdirError) {
                    console.error(`Error creating directory: ${mkdirError}`);
                    res.status(500).json({ message: `Failed to create directory '${userDirPath}'.` });
                    return;
                }
            } else {
                // Some other error occurred
                throw statError;
            }
        }

        // Read directory contents
        const entries = await fs.readdir(absoluteDirPath, { withFileTypes: true });

        // Process both files and directories
        const items = entries.map(entry => {
            const absoluteEntryPath = path.join(absoluteDirPath, entry.name);
            // Return path relative to the KNOWLEDGE_BASE_ROOT
            const relativePath = path.relative(KNOWLEDGE_BASE_ROOT, absoluteEntryPath).replace(/\\/g, '/');
            return {
                name: entry.name,
                path: relativePath,
                type: entry.isDirectory() ? 'directory' : 'file'
            };
        });

        res.status(200).json({
            // Return the path relative to KNOWLEDGE_BASE_ROOT for currentPath
            currentPath: path.relative(KNOWLEDGE_BASE_ROOT, absoluteDirPath).replace(/\\/g, '/') || '.',
            items: items
        });
    } catch (error: any) {
        console.error(`Error listing path contents in '${userDirPath}':`, error);
        if (error.message.startsWith('Access denied') || error.message.startsWith('Invalid path')) {
            res.status(403).json({ message: error.message });
        } else if (error.message.includes('not accessible') || error.message.includes('Path does not exist')) {
            res.status(404).json({ message: `Directory '${userDirPath}' not found or not accessible.` });
        } else {
            res.status(500).json({ message: 'Server error listing path contents.', error: error.message });
        }
    }
};

// @desc    Create a new directory at the specified path (relative to KB root)
// @route   POST /api/rag/create-directory
// @access  Public (adjust later with auth)
export const createDirectory = async (req: Request, res: Response): Promise<void> => {
    const { path: dirPath } = req.body;

    if (!dirPath || typeof dirPath !== 'string') {
        res.status(400).json({ message: 'Path parameter is required and must be a string.' });
        return;
    }

    try {
        // Resolve and validate the path relative to KNOWLEDGE_BASE_ROOT
        // Don't check if it exists since we're creating it
        const absoluteDirPath = await resolveAndValidatePath(dirPath, false);

        // Check if the directory already exists
        try {
            const stats = await fs.stat(absoluteDirPath);
            if (stats.isDirectory()) {
                res.status(409).json({ message: `Directory '${dirPath}' already exists.` });
                return;
            } else {
                res.status(400).json({ message: `Path '${dirPath}' exists but is not a directory.` });
                return;
            }
        } catch (statError: any) {
            // If the error is ENOENT, the directory doesn't exist, which is what we want
            if (statError.code !== 'ENOENT') {
                throw statError; // Re-throw unexpected errors
            }
        }

        // Create the directory
        await fs.mkdir(absoluteDirPath, { recursive: true });

        // Return success response
        res.status(201).json({
            message: `Directory '${dirPath}' created successfully.`,
            path: path.relative(KNOWLEDGE_BASE_ROOT, absoluteDirPath).replace(/\\/g, '/') || '.'
        });
    } catch (error: any) {
        console.error(`Error creating directory '${dirPath}':`, error);
        if (error.message.startsWith('Access denied') || error.message.startsWith('Invalid path')) {
            res.status(403).json({ message: error.message });
        } else {
            res.status(500).json({ message: 'Server error creating directory.', error: error.message });
        }
    }
};

// @desc    Update RAG handler settings
// @route   POST /api/rag/update-settings
// @access  Public (adjust later with auth)
export const updateRagSettings = async (req: Request, res: Response): Promise<void> => {
    const {
        embeddingModel,
        chunkSize,
        chunkOverlap,
        dimension,
        chunkingStrategy,
        retrievalNResults,
        retrievalThreshold,
        useReranking,
        useQueryExpansion
    } = req.body;

    // Validate required parameters
    if (!embeddingModel) {
        res.status(400).json({ message: 'embeddingModel parameter is required.' });
        return;
    }

    try {
        // Update the RAG handler configuration
        const success = await ragHandlerInstance.updateConfiguration({
            embeddingModel,
            chunkSize,
            chunkOverlap,
            dimension,
            chunkingStrategy,
            retrievalNResults,
            retrievalThreshold,
            useReranking,
            useQueryExpansion
        });

        if (success) {
            res.status(200).json({
                message: `RAG settings successfully updated. Embedding model set to ${embeddingModel}.`,
                embeddingModel,
                chunkSize,
                chunkOverlap,
                dimension,
                chunkingStrategy,
                retrievalNResults,
                retrievalThreshold,
                useReranking,
                useQueryExpansion
            });
        } else {
            res.status(500).json({ message: 'Failed to update RAG settings.' });
        }
    } catch (error: any) {
        console.error('Error updating RAG settings:', error);
        res.status(500).json({
            message: 'Server error updating RAG settings.',
            error: error.message,
            code: error.code || 'UNKNOWN_ERROR'
        });
    }
};