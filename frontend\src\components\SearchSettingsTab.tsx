import React, { useState } from 'react';
import InternetSettingsTab from './InternetSettingsTab';
import SearchAgentSettingsTab from './SearchAgentSettingsTab';

/**
 * Component that combines Internet Settings and Search Agent Settings in a tabbed interface
 */
const SearchSettingsTab: React.FC = () => {
  const [activeTab, setActiveTab] = useState(0);

  return (
    <div className="p-1">
      <h3 className="text-lg font-semibold mb-4 text-gray-800 dark:text-gray-200">Search Settings</h3>

      <div className="flex border-b border-gray-200 dark:border-gray-700 mb-4">
        <button
          className={`py-2 px-4 font-medium text-sm ${activeTab === 0 ? 'border-b-2 border-primary text-primary dark:text-primary-light dark:border-primary-light' : 'text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300'}`}
          onClick={() => setActiveTab(0)}
        >
          Internet Search
        </button>
        <button
          className={`py-2 px-4 font-medium text-sm ${activeTab === 1 ? 'border-b-2 border-primary text-primary dark:text-primary-light dark:border-primary-light' : 'text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300'}`}
          onClick={() => setActiveTab(1)}
        >
          Search Agent
        </button>
      </div>

      {activeTab === 0 && <InternetSettingsTab />}
      {activeTab === 1 && <SearchAgentSettingsTab />}
    </div>
  );
};

export default SearchSettingsTab;
