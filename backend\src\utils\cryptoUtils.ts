import crypto from 'crypto';

// Ensure ENCRYPTION_SECRET is set in your .env file
// It should be a strong, random string (e.g., 32 bytes / 256 bits)
// Example generation: node -e "console.log(crypto.randomBytes(32).toString('hex'))"
const ENCRYPTION_KEY = process.env.ENCRYPTION_SECRET;
const IV_LENGTH = 16; // For AES, this is always 16

if (!ENCRYPTION_KEY || ENCRYPTION_KEY.length !== 64) { // 32 bytes = 64 hex characters
  console.error('FATAL ERROR: ENCRYPTION_SECRET environment variable is missing or not a 64-character hex string (32 bytes).');
  // In a real app, you might throw an error or exit, but logging is safer during development setup
  // throw new Error('ENCRYPTION_SECRET environment variable is missing or not a 64-character hex string (32 bytes).');
  // For now, let's log and potentially allow startup, but encryption will fail.
}

// Ensure the key is exactly 32 bytes
const keyBuffer = Buffer.from(ENCRYPTION_KEY || crypto.randomBytes(32).toString('hex'), 'hex'); // Use random fallback ONLY if missing, but log error above

export function encrypt(text: string): string | null {
  if (!ENCRYPTION_KEY || keyBuffer.length !== 32) {
      console.error("Encryption failed: Invalid or missing ENCRYPTION_SECRET.");
      return null; // Indicate failure
  }
  try {
    const iv = crypto.randomBytes(IV_LENGTH);
    const cipher = crypto.createCipheriv('aes-256-cbc', keyBuffer, iv);
    let encrypted = cipher.update(text);
    encrypted = Buffer.concat([encrypted, cipher.final()]);
    // Prepend the IV to the encrypted data for use during decryption
    return iv.toString('hex') + ':' + encrypted.toString('hex');
  } catch (error) {
    console.error("Encryption error:", error);
    return null; // Indicate failure
  }
}

export function decrypt(text: string): string | null {
   if (!ENCRYPTION_KEY || keyBuffer.length !== 32) {
      console.error("Decryption failed: Invalid or missing ENCRYPTION_SECRET.");
      return null; // Indicate failure
  }
  try {
    const textParts = text.split(':');
    if (textParts.length !== 2) {
        console.error("Decryption failed: Invalid format (missing IV separator).");
        return null;
    }
    const iv = Buffer.from(textParts[0], 'hex');
     if (iv.length !== IV_LENGTH) {
        console.error(`Decryption failed: Invalid IV length. Expected ${IV_LENGTH}, got ${iv.length}`);
        return null;
    }
    const encryptedText = Buffer.from(textParts[1], 'hex');
    const decipher = crypto.createDecipheriv('aes-256-cbc', keyBuffer, iv);
    let decrypted = decipher.update(encryptedText);
    decrypted = Buffer.concat([decrypted, decipher.final()]);
    return decrypted.toString();
  } catch (error) {
    console.error("Decryption error:", error);
    // Avoid leaking details about why decryption failed (e.g., bad key, bad padding)
    return null; // Indicate failure
  }
}
