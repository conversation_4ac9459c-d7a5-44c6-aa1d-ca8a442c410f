import React from 'react';
import { Link } from 'react-router-dom';

const TermsOfServicePage: React.FC = () => {
  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        {/* Header */}
        <div className="text-center mb-8">
          <Link 
            to="/" 
            className="inline-flex items-center text-primary hover:text-primary-dark mb-4"
          >
            <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 19l-7-7m0 0l7-7m-7 7h18" />
            </svg>
            Back to MAIAChat
          </Link>
          <h1 className="text-4xl font-bold text-gray-900 dark:text-white">
            Terms of Service
          </h1>
          <p className="text-gray-600 dark:text-gray-400 mt-2">
            Last Updated: August 3, 2025
          </p>
        </div>

        {/* Content */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-8">
          <div className="prose prose-lg dark:prose-invert max-w-none">
            
            <h2>1. Acceptance of Terms</h2>
            <p>
              By accessing and using MAIAChat ("the Service"), you accept and agree to be bound by the terms and provision of this agreement. If you do not agree to abide by the above, please do not use this service.
            </p>

            <h2>2. Description of Service</h2>
            <p>MAIAChat is a multi-agent AI assistant web application that provides:</p>
            <ul>
              <li>AI-powered conversations with multiple agents</li>
              <li>Document processing and retrieval (RAG)</li>
              <li>Internet search capabilities</li>
              <li>Image generation and analysis</li>
              <li>Configuration management for AI models</li>
            </ul>

            <h2>3. User Accounts</h2>
            <h3>3.1 Registration</h3>
            <ul>
              <li>You must provide accurate and complete information when creating an account</li>
              <li>You are responsible for maintaining the confidentiality of your account credentials</li>
              <li>You are responsible for all activities that occur under your account</li>
            </ul>

            <h3>3.2 Account Security</h3>
            <ul>
              <li>You must notify us immediately of any unauthorized use of your account</li>
              <li>We reserve the right to suspend or terminate accounts that violate these terms</li>
            </ul>

            <h2>4. Acceptable Use</h2>
            <h3>4.1 Permitted Uses</h3>
            <ul>
              <li>Personal and commercial use in compliance with applicable laws</li>
              <li>Educational and research purposes</li>
              <li>Content creation and analysis</li>
            </ul>

            <h3>4.2 Prohibited Uses</h3>
            <p>You may not use the Service to:</p>
            <ul>
              <li>Generate illegal, harmful, or offensive content</li>
              <li>Violate any applicable laws or regulations</li>
              <li>Infringe on intellectual property rights</li>
              <li>Attempt to gain unauthorized access to our systems</li>
              <li>Distribute malware or engage in malicious activities</li>
              <li>Harass, abuse, or harm others</li>
              <li>Generate content that promotes violence, discrimination, or hate speech</li>
            </ul>

            <h2>5. Content and Data</h2>
            <h3>5.1 User Content</h3>
            <ul>
              <li>You retain ownership of content you submit to the Service</li>
              <li>You grant us a license to use your content to provide the Service</li>
              <li>You are responsible for ensuring you have rights to any content you submit</li>
            </ul>

            <h3>5.2 Generated Content</h3>
            <ul>
              <li>AI-generated content is provided "as is" without warranties</li>
              <li>You are responsible for reviewing and validating AI-generated content</li>
              <li>We do not claim ownership of AI-generated content</li>
            </ul>

            <h2>6. API Keys and Third-Party Services</h2>
            <h3>6.1 Your Responsibility</h3>
            <ul>
              <li>You are responsible for obtaining and managing your own API keys</li>
              <li>You must comply with third-party service terms (OpenAI, Anthropic, Google, etc.)</li>
              <li>You are responsible for any costs incurred from third-party API usage</li>
            </ul>

            <h2>7. Disclaimers and Limitations</h2>
            <h3>7.1 Service Disclaimer</h3>
            <p className="font-semibold">
              THE SERVICE IS PROVIDED "AS IS" WITHOUT WARRANTIES OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE, AND NON-INFRINGEMENT.
            </p>

            <h3>7.2 AI Content Disclaimer</h3>
            <ul>
              <li>AI-generated content may be inaccurate, biased, or inappropriate</li>
              <li>Always verify important information from authoritative sources</li>
              <li>We are not responsible for decisions made based on AI-generated content</li>
            </ul>

            <h2>8. Contact Information</h2>
            <p>If you have any questions about these Terms of Service, please contact us at:</p>
            <ul>
              <li>Email: <a href="mailto:<EMAIL>" className="text-primary hover:text-primary-dark"><EMAIL></a></li>
            </ul>

            <h2>9. Changes to Terms</h2>
            <p>
              We reserve the right to modify these terms at any time. We will notify users of significant changes via email or through the Service. Continued use of the Service after changes constitutes acceptance of the new terms.
            </p>

          </div>
        </div>

        {/* Footer */}
        <div className="text-center mt-8">
          <Link 
            to="/privacy-policy" 
            className="text-primary hover:text-primary-dark mr-6"
          >
            Privacy Policy
          </Link>
          <Link 
            to="/" 
            className="text-primary hover:text-primary-dark"
          >
            Back to MAIAChat
          </Link>
        </div>
      </div>
    </div>
  );
};

export default TermsOfServicePage;
