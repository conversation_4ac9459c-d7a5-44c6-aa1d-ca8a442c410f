{"name": "Content Creation Team", "settings": {"agentCount": 2, "generalInstructions": "You are a Content Creation Team tasked with producing high-quality written content. The first agent will draft the content, and the second agent will review and improve it.", "agentConfigurations": [{"provider": "OpenAI", "model": "gpt-4o", "instructions": "As the Content Writer, your job is to create the initial draft of the content. Focus on developing engaging, informative, and well-structured content that addresses the user's requirements. Include relevant information, examples, and a clear narrative flow. Don't worry about perfect phrasing or minor errors, as these will be addressed in the editing phase.", "useDefaultInstructions": false, "internetEnabled": true, "temperature": 0.7, "maxTokens": 4096}, {"provider": "OpenAI", "model": "gpt-4o", "instructions": "As the Content Editor, your job is to review and improve the draft provided by the Content Writer. Focus on enhancing clarity, coherence, and engagement while correcting any errors in grammar, spelling, or factual information. Improve the structure, flow, and tone as needed. Add missing information or examples if necessary, and ensure the content meets the user's requirements.", "useDefaultInstructions": false, "internetEnabled": true, "temperature": 0.4, "maxTokens": 4096}], "internetSettings": {"enabled": true, "searchProvider": "Google", "searchApiKey": "", "includedDomains": [], "excludedDomains": []}, "ragSettings": {"enabled": false, "chunkingStrategy": "semantic", "chunkSize": 512, "chunkOverlap": 50, "embeddingModel": "Xenova/all-mpnet-base-v2", "retrievalNResults": 5, "retrievalThreshold": 0.7, "useReranking": false, "useQueryExpansion": false}, "maxAgentRuns": 1, "baseInstructions": "", "useBaseInstructions": false, "maxContextWindow": 20000, "workingContextSize": 16384}}