version: '3.8'

services:
  backend:
    build:
      context: .
      dockerfile: Dockerfile.backend
      # Build args can be passed here if needed for production
      # args:
      #   SOME_BUILD_ARG: value
    # image: your-dockerhub-username/maia-backend:latest # Optional: Use a pre-built image
    restart: always
    environment:
      # --- PRODUCTION Environment Variables ---
      # DO NOT commit sensitive keys directly here. Use secrets management.
      NODE_ENV: production
      PORT: 3000 # Or your desired production port
      MONGO_URI: mongodb://mongo:27017/maia_db # Example using Docker network alias
      JWT_SECRET: ${JWT_SECRET} # Load from host environment or secrets
      ENCRYPTION_SECRET: ${ENCRYPTION_SECRET} # Load from host environment or secrets
      FRONTEND_URL: ${FRONTEND_URL:-http://localhost} # Your production frontend URL
      # Add Production API Keys (load from host env or secrets)
      SERPER_API_KEY: ${SERPER_API_KEY}
      GOOGLE_API_KEY: ${GOOGLE_API_KEY}
      GOOGLE_SEARCH_ENGINE_ID: ${GOOGLE_SEARCH_ENGINE_ID}
      OPENAI_API_KEY: ${OPENAI_API_KEY}
      ANTHROPIC_API_KEY: ${ANTHROPIC_API_KEY}
      # ... other keys ...
      # Local model URLs and timeouts (if connecting to a separate prod instance)
      # OLLAMA_BASE_URL: ${OLLAMA_BASE_URL}
      # LMSTUDIO_BASE_URL: ${LMSTUDIO_BASE_URL}
      OLLAMA_REQUEST_TIMEOUT: ${OLLAMA_REQUEST_TIMEOUT:-60000} # Timeout for Ollama model loading (default: 60 seconds)
      LMSTUDIO_REQUEST_TIMEOUT: ${LMSTUDIO_REQUEST_TIMEOUT:-30000} # Timeout for LM Studio model loading (default: 30 seconds)
    depends_on:
      - mongo
    networks:
      - maia_network

  frontend:
    build:
      context: .
      dockerfile: Dockerfile.frontend
      # Pass production build args if needed (e.g., API base URL if not using proxy)
      # args:
      #   VITE_API_BASE_URL: /api # Example if using Nginx proxy within compose
      #   VITE_SOCKET_SERVER_URL: "" # Example if using Nginx proxy within compose
    # image: your-dockerhub-username/maia-frontend:latest # Optional: Use a pre-built image
    restart: always
    ports:
      - "80:80" # Map host port 80 to container port 80 (Nginx default)
    networks:
      - maia_network
    # depends_on: # Frontend doesn't strictly depend on backend start, Nginx handles routing
    #   - backend

  mongo:
    image: mongo:latest
    restart: always
    volumes:
      - mongo_data:/data/db # Persist database data
    networks:
      - maia_network

networks:
  maia_network:
    driver: bridge

volumes:
  mongo_data:
    driver: local
