// Test script for embeddings
import path from 'path';

// We'll import transformers dynamically and configure it later

const DEFAULT_EMBEDDING_MODEL = 'Xenova/all-mpnet-base-v2';

async function testEmbeddings() {
  try {
    // Dynamically import the transformers module
    const transformers = await import('@xenova/transformers');

    // Configure the environment
    transformers.env.allowLocalModels = true;
    transformers.env.cacheDir = path.resolve('./cache');

    console.log(`Initializing embedding model: ${DEFAULT_EMBEDDING_MODEL}...`);
    const embedder = await transformers.pipeline('feature-extraction', DEFAULT_EMBEDDING_MODEL, {
      quantized: true
    });
    console.log('Embedding model initialized successfully.');

    // Test with a simple text
    const texts = ['This is a test sentence for embeddings.'];
    console.log(`Generating embeddings for: "${texts[0]}"`);

    const output = await embedder(texts, { pooling: 'mean', normalize: true });

    // Log detailed information about the output
    console.log(`Output type: ${typeof output}, constructor: ${output?.constructor?.name}`);
    console.log('Output keys:', Object.keys(output));

    if ('data' in output) {
      console.log(`Output.data type: ${typeof output.data}, constructor: ${output.data?.constructor?.name}`);
      console.log(`Output.data length: ${output.data.length}`);

      if ('dims' in output) {
        console.log(`Output dimensions:`, output.dims);
      } else if (output && typeof output === 'object' && 'shape' in output) {
        console.log(`Output shape:`, (output as any).shape);
      }
    }

    // Try to extract the embedding data
    let embeddingData;

    if (Array.isArray(output)) {
      console.log('Output is an array');
      embeddingData = output[0];
    } else if (output && typeof output === 'object' && 'data' in output) {
      console.log('Output is an object with data property');
      embeddingData = output.data;
    }

    if (embeddingData) {
      console.log(`Embedding data type: ${typeof embeddingData}`);
      console.log(`Embedding data length: ${embeddingData.length || 'unknown'}`);

      // Print first few values
      if (Array.isArray(embeddingData) || embeddingData instanceof Float32Array) {
        console.log('First 5 values:', embeddingData.slice(0, 5));
      }
    }

    console.log('Test completed successfully');
  } catch (error) {
    console.error('Error testing embeddings:', error);
  }
}

// Run the test
testEmbeddings();
