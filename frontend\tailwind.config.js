/** @type {import('tailwindcss').Config} */
export default {
  content: [
    "./index.html",
    "./src/**/*.{js,ts,jsx,tsx}",
  ],
  darkMode: 'class', // Enable class-based dark mode
  theme: {
    extend: {
      colors: {
        primary: {
          light: '#6366f1', // Indigo-500
          DEFAULT: '#4f46e5', // Indigo-600
          dark: '#4338ca', // Indigo-700
        },
        secondary: {
          light: '#a855f7', // Purple-500
          DEFAULT: '#9333ea', // Purple-600
          dark: '#7e22ce', // Purple-700
        },
        background: {
          light: '#ffffff',
          dark: '#1f2937', // Gray-800
        },
        surface: {
          light: '#f9fafb', // Gray-50
          dark: '#111827', // Gray-900
        },
        text: {
          light: '#111827', // Gray-900
          dark: '#f9fafb', // Gray-50
        },
        gray: {
          850: '#1a202e', // Custom dark gray between gray-800 and gray-900
        },
      },
      fontFamily: {
        sans: ['Inter', 'ui-sans-serif', 'system-ui', 'sans-serif'],
      },
    },
  },
  plugins: [],
}
