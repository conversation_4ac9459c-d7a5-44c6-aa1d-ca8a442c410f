import React, { useState, useRef, useEffect } from 'react';

interface ResizableColumnProps {
  children: React.ReactNode;
  initialWidth: string;
  minWidth: string;
  maxWidth: string;
  onResize?: (newWidth: number) => void;
  className?: string;
  resizeFrom?: 'left' | 'right';
  backgroundColor?: string;
  title?: string;
}

const ResizableColumn: React.FC<ResizableColumnProps> = ({
  children,
  initialWidth,
  minWidth,
  maxWidth,
  onResize,
  className = '',
  resizeFrom = 'right',
  backgroundColor = 'bg-white dark:bg-gray-800',
  title,
}) => {
  const [width, setWidth] = useState(initialWidth);
  const columnRef = useRef<HTMLDivElement>(null);
  const resizingRef = useRef(false);
  const startXRef = useRef(0);
  const startWidthRef = useRef(0);

  // Convert CSS width values to numbers for calculations
  const parseWidth = (widthStr: string): number => {
    if (widthStr.endsWith('px')) {
      return parseInt(widthStr, 10);
    }
    if (widthStr.endsWith('%')) {
      const percentage = parseInt(widthStr, 10);
      return percentage / 100 * (columnRef.current?.parentElement?.clientWidth || 0);
    }
    return parseInt(widthStr, 10) || 300; // Default to 300px if parsing fails
  };

  // Convert numeric width to CSS value
  const formatWidth = (widthNum: number): string => {
    return `${widthNum}px`;
  };

  // Handle mouse down on resize handle
  const handleResizeStart = (e: React.MouseEvent) => {
    e.preventDefault();
    resizingRef.current = true;
    startXRef.current = e.clientX;
    startWidthRef.current = columnRef.current?.clientWidth || parseWidth(width);

    document.body.style.cursor = 'col-resize';
    document.addEventListener('mousemove', handleResizeMove);
    document.addEventListener('mouseup', handleResizeEnd);
  };

  // Handle mouse move during resize
  const handleResizeMove = (e: MouseEvent) => {
    if (!resizingRef.current) return;

    const deltaX = e.clientX - startXRef.current;
    const newWidth = resizeFrom === 'right'
      ? startWidthRef.current + deltaX
      : startWidthRef.current - deltaX;

    const minWidthPx = parseWidth(minWidth);
    const maxWidthPx = parseWidth(maxWidth);

    const clampedWidth = Math.max(minWidthPx, Math.min(maxWidthPx, newWidth));

    setWidth(formatWidth(clampedWidth));
    if (onResize) {
      onResize(clampedWidth);
    }
  };

  // Handle mouse up to end resize
  const handleResizeEnd = () => {
    resizingRef.current = false;
    document.body.style.cursor = '';
    document.removeEventListener('mousemove', handleResizeMove);
    document.removeEventListener('mouseup', handleResizeEnd);
  };

  // Clean up event listeners on unmount
  useEffect(() => {
    return () => {
      document.removeEventListener('mousemove', handleResizeMove);
      document.removeEventListener('mouseup', handleResizeEnd);
    };
  }, []);

  return (
    <div
      ref={columnRef}
      className={`relative h-full ${backgroundColor} ${className}`}
      style={{ width, minWidth, maxWidth }}
    >
      {title && (
        <div className="p-4 border-b border-gray-200 dark:border-gray-700">
          <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-200">{title}</h3>
        </div>
      )}
      {children}

      {/* Resize handle */}
      <div
        className={`absolute top-0 ${resizeFrom === 'right' ? 'right-0' : 'left-0'} w-1 h-full cursor-col-resize group z-10`}
        onMouseDown={handleResizeStart}
      >
        <div className="absolute top-0 bottom-0 w-4 bg-transparent hover:bg-primary-light/20 transition-colors"
             style={{ [resizeFrom === 'right' ? 'right' : 'left']: '-8px' }} />
      </div>
    </div>
  );
};

export default ResizableColumn;
