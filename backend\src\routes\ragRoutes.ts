import express, { Request, Response, NextFunction } from 'express'; // Added Response, NextFunction types
import multer from 'multer';
import path from 'path';
import fs from 'fs';
import {
    uploadFileForRag,
    listIndexedFiles,
    deleteIndexedFile,
    getKnowledgeBasePath,
    setKnowledgeBasePath,
    listKnowledgeBases,
    createKnowledgeBase,
    removeKnowledgeBase,
    uploadDirectoryForRag,
    reindexKnowledgeBase,
    listDirectories,
    listPathContents,
    createDirectory,
    updateRagSettings
} from '../controllers/ragController'; // Assuming these imports are correct

const router = express.Router();

// --- Multer Configuration ---

// Define storage location relative to this file's directory for reliability
// Place the 'uploads' directory sibling to your 'routes' or 'src' directory, adjust path as needed.
// Example: If routes is in 'src/routes', and you want uploads in 'project_root/uploads'
const uploadDir = path.resolve(__dirname, '../../uploads/rag_temp'); // Adjust '../..' based on your structure

// Ensure the upload directory exists
try {
    if (!fs.existsSync(uploadDir)) {
        fs.mkdirSync(uploadDir, { recursive: true });
        console.log(`Created temporary upload directory: ${uploadDir}`);
    }
} catch (err) {
    console.error(`FATAL: Failed to create upload directory ${uploadDir}. Check permissions.`, err);
    // Depending on the app's needs, you might want to exit or throw a more specific error
    process.exit(1); // Exit if the temp dir is critical and cannot be created
}

// NOTE: Files in 'uploadDir' are temporary. The 'uploadFileForRag' controller
// MUST move or delete the file from 'req.file.path' after processing to prevent accumulation.

const storage = multer.diskStorage({
    destination: function (req, file, cb) {
        cb(null, uploadDir); // Save files to the temp directory
    },
    filename: function (req, file, cb) {
        // Use a unique filename to avoid collisions and keep original name part
        const uniqueSuffix = Date.now() + '-' + Math.round(Math.random() * 1E9);
        // Basic sanitization of original filename part (optional but recommended)
        const safeOriginalName = file.originalname.replace(/[^a-zA-Z0-9_.-]/g, '_');
        cb(null, uniqueSuffix + '-' + safeOriginalName);
    }
});

// Define file filter
const fileFilter = (req: Request, file: Express.Multer.File, cb: multer.FileFilterCallback) => {
    const allowedTypes = [
        // Document formats
        '.pdf', '.docx', '.txt', '.md', '.ppt', '.pptx', // Added presentation types
        // Spreadsheet formats
        '.xlsx', '.xls', '.csv',
        // Code formats
        '.ts', '.js', '.py', '.java', '.c', '.cpp', '.cs', '.php', '.rb',
        // Web formats
        '.html', '.htm',
        // Other text-based formats
        '.json', '.xml', '.yaml', '.yml', '.ini', '.config', '.sql'
    ];
    const ext = path.extname(file.originalname).toLowerCase();
    if (allowedTypes.includes(ext)) {
        cb(null, true); // Accept file
    } else {
        // Reject file - Sending an error here is okay, but requires proper error handling middleware
        // Alternatively, use cb(null, false) and check req.fileValidationError in controller/middleware
        cb(new Error(`Invalid file type: ${ext}. Allowed types: ${allowedTypes.join(', ')}`));
    }
};


const upload = multer({
    storage: storage,
    fileFilter: fileFilter,
    limits: {
        fileSize: 1024 * 1024 * 100 // 100MB file size limit (adjust as needed)
    }
});
// --- End Multer Configuration ---


// --- RAG Routes ---
// Apply authentication middleware to all routes in this router
import { protect } from '../middleware/authMiddleware';
router.use(protect);

// NOTE: Controllers are responsible for input validation (especially paths to prevent traversal) and sending JSON responses.

// File Management Routes
// POST /api/rag/upload - Handles file uploads with 'ragFile' field name
router.post('/upload', upload.single('ragFile'), (req: Request, res: Response) => {
    // Convert single file to array format for consistent handling with the controller
    if (req.file) {
        (req as any).files = [req.file];
    }
    uploadFileForRag(req, res);
});

// GET /api/rag/files - List indexed files/sources for the current KB
router.get('/files', listIndexedFiles);

// DELETE /api/rag/files/:filename - Delete a specific indexed file/source
// IMPORTANT: ':filename' might need to be a full path or unique ID depending on backend implementation.
// Client MUST use encodeURIComponent on the filename part of the URL.
// Controller MUST use decodeURIComponent(req.params.filename) and handle path construction safely.
router.delete('/files/:filename', deleteIndexedFile);

// POST /api/rag/upload-directory - Process files in a server-side directory
// Expects JSON body: { directoryPath: string, recursive?: boolean, storageType: 'local' | 'server' }
// Controller MUST validate directoryPath securely.
router.post('/upload-directory', uploadDirectoryForRag);

// Knowledge Base Management Routes
// GET /api/rag/kb-path - Get current knowledge base path and storage type
router.get('/kb-path', getKnowledgeBasePath);

// POST /api/rag/kb-path - Set current knowledge base path
// Expects JSON body: { path: string, storageType: 'local' | 'server' }
// Controller MUST validate path securely.
router.post('/kb-path', setKnowledgeBasePath);

// GET /api/rag/kb-list - List available/known knowledge bases
// Expects query param: ?storageType='local'|'server' (handled by controller)
router.get('/kb-list', listKnowledgeBases);

// POST /api/rag/kb-create - Create a new knowledge base (directory or entry)
// Expects JSON body: { name: string, storageType: 'local' | 'server' }
// Controller MUST validate name/path securely.
router.post('/kb-create', createKnowledgeBase);

// DELETE /api/rag/kb-remove - Remove/delete a knowledge base
// Expects JSON body: { path: string, storageType: 'local' | 'server' }
// Controller MUST validate path securely.
router.delete('/kb-remove', removeKnowledgeBase);

// POST /api/rag/reindex - Re-index all files in the current knowledge base
router.post('/reindex', reindexKnowledgeBase);

// GET /api/rag/list-directories - List sub-directories within a specified path
// Expects query params: ?path=string&storageType='local'|'server'
// Controller MUST validate path securely.
router.get('/list-directories', listDirectories);

// GET /api/rag/list-path-contents - List both files and directories within a specified path
// Expects query params: ?path=string&storageType='local'|'server'
// Controller MUST validate path securely.
router.get('/list-path-contents', listPathContents);

// POST /api/rag/create-directory - Create a new directory at the specified path
// Expects JSON body: { path: string }
// Controller MUST validate path securely.
router.post('/create-directory', createDirectory);

// POST /api/rag/update-settings - Update RAG handler settings
// Expects JSON body: { embeddingModel: string, chunkSize?: number, chunkOverlap?: number, dimension?: number }
router.post('/update-settings', updateRagSettings);


// Optional: Basic Multer Error Handling Middleware (apply after routes if needed)
// This is a simple example; more robust handling might be required.
router.use((err: any, req: Request, res: Response, next: NextFunction) => {
    if (err instanceof multer.MulterError) {
        // A Multer error occurred when uploading.
        console.warn('Multer Error:', err);
        return res.status(400).json({ message: `File upload error: ${err.message}`, code: err.code });
    } else if (err && err.message?.startsWith('Invalid file type')) {
        // Error from our custom fileFilter
        console.warn('File Type Error:', err);
        return res.status(400).json({ message: err.message });
     } else if (err) {
        // An unknown error occurred.
        console.error('Unhandled RAG Route Error:', err);
        return res.status(500).json({ message: 'An internal server error occurred.' });
     }
    // If no error or error already handled, pass to next middleware
    next();
});


export default router;