import bcrypt from 'bcrypt';
import jwt from 'jsonwebtoken';
import { IUser } from '../models/User'; // Assuming User model path

const SALT_ROUNDS = 10; // Standard number of salt rounds for bcrypt

// Function to hash a password
export const hashPassword = async (password: string): Promise<string> => {
  return await bcrypt.hash(password, SALT_ROUNDS);
};

// Function to compare a plain password with a hash
export const comparePassword = async (password: string, hash: string): Promise<boolean> => {
  return await bcrypt.compare(password, hash);
};

// Function to generate a JWT
export const generateToken = (user: IUser): string => {
  const jwtSecret = process.env.JWT_SECRET;
  if (!jwtSecret) {
    console.error('FATAL ERROR: JWT_SECRET is not defined in .env file');
    throw new Error('JWT Secret not configured'); // Throw error if secret is missing
  }

  // Payload includes user ID and email (you can add more non-sensitive info if needed)
  const payload = {
    id: user._id,
    email: user.email,
    // Add role or subscription tier if needed for authorization checks later
    // subscriptionTier: user.subscriptionTier,
  };

  // Sign the token with the secret and set an expiration time (e.g., 1 day)
  return jwt.sign(payload, jwtSecret, {
    expiresIn: '1d', // Token expires in 1 day
  });
};

// Interface for the expected JWT payload
interface JwtPayload extends jwt.JwtPayload {
  id: string; // From user._id
  email?: string; // Optional email field
  // Add other fields if they were included in generateToken payload
}

// Function to verify a JWT (used by middleware)
export const verifyToken = (token: string): JwtPayload | null => { // <<< Update return type
  const jwtSecret = process.env.JWT_SECRET;
  if (!jwtSecret) {
    console.error('FATAL ERROR: JWT_SECRET is not defined in .env file');
    return null; // Cannot verify without secret
  }

  try {
    // Verify the token using the secret
    const decoded = jwt.verify(token, jwtSecret);
    // Ensure the decoded object fits the JwtPayload structure (basic check)
    if (typeof decoded === 'object' && decoded !== null && 'id' in decoded) {
        return decoded as JwtPayload; // Cast to defined type
    }
    console.error('JWT Verification Warning: Decoded payload structure mismatch.');
    return null; // Return null if structure doesn't match expected
  } catch (error) {
    // console.error('JWT Verification Error:', error); // Keep logging minimal for production
    return null; // Return null if token is invalid or expired
  }
};
