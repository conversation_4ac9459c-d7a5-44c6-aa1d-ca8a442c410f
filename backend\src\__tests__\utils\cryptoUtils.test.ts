import { encrypt, decrypt } from '../../utils/cryptoUtils';

describe('Crypto Utils', () => {
  const testData = 'sensitive-api-key-12345';

  describe('encrypt', () => {
    it('should encrypt data successfully', () => {
      const encrypted = encrypt(testData);
      
      expect(encrypted).toBeDefined();
      expect(encrypted).not.toBe(testData);
      expect(encrypted).toContain(':'); // Should contain IV separator
    });

    it('should generate different encrypted values for same input', () => {
      const encrypted1 = encrypt(testData);
      const encrypted2 = encrypt(testData);
      
      expect(encrypted1).not.toBe(encrypted2);
    });

    it('should handle empty string', () => {
      const encrypted = encrypt('');
      
      expect(encrypted).toBeDefined();
      expect(encrypted).toContain(':');
    });
  });

  describe('decrypt', () => {
    it('should decrypt data successfully', () => {
      const encrypted = encrypt(testData);
      const decrypted = decrypt(encrypted!);
      
      expect(decrypted).toBe(testData);
    });

    it('should handle multiple encrypt/decrypt cycles', () => {
      let data = testData;
      
      for (let i = 0; i < 5; i++) {
        const encrypted = encrypt(data);
        const decrypted = decrypt(encrypted!);
        expect(decrypted).toBe(data);
      }
    });

    it('should return null for invalid encrypted data', () => {
      const invalidData = 'invalid-encrypted-data';
      const decrypted = decrypt(invalidData);
      
      expect(decrypted).toBeNull();
    });

    it('should return null for malformed encrypted data', () => {
      const malformedData = 'no-separator-here';
      const decrypted = decrypt(malformedData);
      
      expect(decrypted).toBeNull();
    });
  });

  describe('encrypt/decrypt integration', () => {
    it('should handle various data types as strings', () => {
      const testCases = [
        'simple-string',
        'string with spaces and symbols!@#$%',
        '{"json": "object", "number": 123}',
        'very-long-string-'.repeat(100),
        '🚀 unicode characters 中文 العربية'
      ];

      testCases.forEach(testCase => {
        const encrypted = encrypt(testCase);
        const decrypted = decrypt(encrypted!);
        expect(decrypted).toBe(testCase);
      });
    });
  });
});
