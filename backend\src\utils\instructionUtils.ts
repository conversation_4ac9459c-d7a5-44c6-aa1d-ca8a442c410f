// instructionUtils.ts - Adapts logic from instruction_templates.py

// --- Imports ---
import { IncomingAgentConfig } from '../server'; // Assuming IncomingConfigData might not be needed directly here
import logger from './logger';

// --- Instruction Snippets (Constants) ---
const K<PERSON><PERSON>LEDGE_BASE_FOCUS = `
When working with the knowledge base:
1. Prioritize information from the provided knowledge base.
2. Cite specific sections when relevant.
3. Highlight any information gaps.
`;

const INTERNET_SEARCH = `
When using internet search results:
1. Evaluate source credibility.
2. Cross-validate information across multiple sources.
3. Clearly indicate when using search results.
`;

const BASE_FORMAT = `
FORMATTING GUIDELINES:

1. Use clear, numbered sections for organization.
2. Use bullet points for lists.
3. Present technical specifications in simple tables.
4. Use consistent formatting throughout.
`;

const PREVIOUS_AGENT_HANDLING = `
IMPORTANT - HANDLING PREVIOUS AGENT OUTPUTS:

1. You will see outputs from previous agents in your context in 'PREVIOUS AGENT RESPONSES' section.
2. Explicitly reference these outputs in your response.
3. NEVER simply repeat their work - build upon it and improve it.
4. When appropriate, quote specific parts you're improving or expanding.
5. **Critically assess if previous agents missed any important context from knowledge base or search results and try to incorporate that if relevant.**

Example approach: "Agent X suggested [quote]. Building on this, I would refine/expand..."
`;

// --- Section Headers (Constants) ---
const SECTION_HEADER_TASK = "=== OVERALL TASK / CONTEXT ===";
const SECTION_HEADER_ROLE = "=== YOUR ROLE & SPECIFIC INSTRUCTIONS ===";
const SECTION_HEADER_KB = "=== KNOWLEDGE BASE GUIDELINES ===";
const SECTION_HEADER_INTERNET = "=== INTERNET SEARCH GUIDELINES ===";
const SECTION_HEADER_COLLAB = "=== COLLABORATION GUIDELINES (Handling Previous Responses) ===";
const SECTION_HEADER_FORMAT = "=== OUTPUT FORMATTING GUIDELINES ===";
const SECTION_SUB_HEADER_CUSTOM = "--- Specific Instructions for You ---"; // Sub-header for custom agent instructions

// --- Main Function ---

/**
 * Returns appropriate instructions for an agent based on their position and total agents.
 * Creates a collaborative workflow where agents build on each other's work.
 * (Adapted from Python's InstructionTemplates.get_agent_instructions)
 *
 * @param agentNumber The 1-based index of the current agent.
 * @param totalAgents The total number of agents in the discussion.
 * @param agentConfig The configuration for the specific agent.
 * @param globalInternetEnabled Whether internet search is globally enabled.
 * @param generalInstructions Optional general instructions for all agents (takes precedence).
 * @param baseInstructions Default base instructions (used if generalInstructions is empty AND useBaseInstructions is true).
 * @param useBaseInstructions Flag indicating whether to use baseInstructions as fallback.
 * @returns A formatted string containing the full instructions for the agent, or an error message string if inputs are invalid.
 */
export const getAgentInstructions = (
    agentNumber: number,
    totalAgents: number,
    agentConfig: IncomingAgentConfig,
    globalInternetEnabled: boolean,
    generalInstructions?: string,
    baseInstructions?: string,
    useBaseInstructions?: boolean
): string => {
    // --- Input Validation ---
    if (totalAgents < 1 || agentNumber < 1 || agentNumber > totalAgents) {
        const errorMsg = `[getAgentInstructions] Invalid agent numbering: agentNumber=${agentNumber}, totalAgents=${totalAgents}.`;
        logger.error(errorMsg);
        return `Error: Invalid agent numbering provided. Cannot generate instructions.`; // Return an error message
    }

    logger.debug(`[getAgentInstructions] Generating instructions for Agent ${agentNumber} of ${totalAgents}.`);
    logger.debug(`[getAgentInstructions] Received generalInstructions: ${generalInstructions ? `"${generalInstructions}"` : 'None'}`);
    logger.debug(`[getAgentInstructions] Received baseInstructions: ${baseInstructions ? `"${baseInstructions}"` : 'None'}`);
    logger.debug(`[getAgentInstructions] Received useBaseInstructions: ${useBaseInstructions}`);
    logger.debug(`[getAgentInstructions] Received agentConfig.instructions: ${agentConfig.instructions ? `"${agentConfig.instructions}"` : 'None'}`);
    logger.debug(`[getAgentInstructions] Received agentConfig.useDefaultInstructions: ${agentConfig.useDefaultInstructions}`);
    logger.debug(`[getAgentInstructions] Received agentConfig.internetEnabled: ${agentConfig.internetEnabled}`);
    logger.debug(`[getAgentInstructions] Received globalInternetEnabled: ${globalInternetEnabled}`);

    const instructions: string[] = [];
    const customInstructions = agentConfig.instructions;

    // Determine if internet is enabled for *this* agent
    const agentInternetEnabled = agentConfig.internetEnabled ?? globalInternetEnabled;
    logger.debug(`[getAgentInstructions] Agent ${agentNumber}: Determined agentInternetEnabled: ${agentInternetEnabled}`);

    // 1. Determine Primary Instructions (General > Base if enabled > None)
    let primaryInstructions = '';
    if (generalInstructions?.trim()) {
        primaryInstructions = generalInstructions;
        logger.debug(`[getAgentInstructions] Agent ${agentNumber}: Using generalInstructions as primary.`);
    } else if (useBaseInstructions && baseInstructions?.trim()) {
        primaryInstructions = baseInstructions;
        logger.debug(`[getAgentInstructions] Agent ${agentNumber}: Using baseInstructions as primary.`);
    } else {
        logger.debug(`[getAgentInstructions] Agent ${agentNumber}: No primary (general/base) instructions available or enabled.`);
    }

    // --- Assemble Instructions ---

    // Part 1: Core Task / Context (Only add if primaryInstructions is not empty)
    if (primaryInstructions) {
        instructions.push(`${SECTION_HEADER_TASK}\n${primaryInstructions}`);
    }

    // Part 2: Determine Role based on position
    let role = "";
    if (totalAgents === 1) {
        role = `
As the only agent, your task is to:
1. Analyze the complete request thoroughly.
2. Provide a comprehensive solution.
3. Ensure your response is accurate, practical, and actionable.
        `;
    } else {
        if (agentNumber === 1) {
            role = `
As the first agent, your task is to:
1. Analyze the initial request thoroughly.
2. Provide your best initial response to the request.
3. Focus on quality rather than comprehensiveness - subsequent agents will build on your work.

Remember that other agents will be building upon your response, so provide a strong foundation.
            `;
        } else if (agentNumber === totalAgents) {
            role = `
As the final agent, your task is to:
1. Review all previous agent responses carefully.
2. DO NOT simply provide a new separate response.
3. Instead, create a cohesive FINAL ANSWER that:
   - Incorporates the best elements from all previous responses.
   - Resolves any contradictions.
   - Fills any remaining gaps.
   - Provides a complete solution to the original request.

YOUR RESPONSE WILL BE PRESENTED AS THE FINAL ANSWER TO THE USER.
            `;
        } else {
            role = `
As agent ${agentNumber} of ${totalAgents}, your task is to:
1. Review the previous agent responses carefully.
2. DO NOT simply provide a completely new response.
3. Instead, build upon previous responses by:
   - Improving existing content.
   - Adding missing information.
   - Correcting any errors.
   - Expanding on promising ideas.

Your work will be further refined by subsequent agents, so focus on meaningful improvements.
            `;
        }
    }
    logger.debug(`[getAgentInstructions] Agent ${agentNumber}: Determined role instructions.`);

    // Combine Role and Custom Instructions into one block
    let roleAndCustom = `${SECTION_HEADER_ROLE}\n${role}`;
    // Only add custom instructions if they exist AND useDefaultInstructions is not true
    if (customInstructions?.trim() && agentConfig.useDefaultInstructions !== true) {
        roleAndCustom += `\n\n${SECTION_SUB_HEADER_CUSTOM}\n${customInstructions}`;
        logger.debug(`[getAgentInstructions] Agent ${agentNumber}: Appended custom instructions.`);
    } else if (customInstructions?.trim() && agentConfig.useDefaultInstructions === true) {
        logger.debug(`[getAgentInstructions] Agent ${agentNumber}: Custom instructions exist but not used due to useDefaultInstructions=true.`);
    }
    instructions.push(roleAndCustom);

    // Part 3: Guidelines & Context Handling
    const guidelines: string[] = [];
    guidelines.push(`${SECTION_HEADER_KB}\n${KNOWLEDGE_BASE_FOCUS}`);

    if (agentInternetEnabled) {
        guidelines.push(`${SECTION_HEADER_INTERNET}\n${INTERNET_SEARCH}`);
    }
    // Collaboration guidelines only needed if there's more than one agent and this isn't the first
    if (totalAgents > 1 && agentNumber > 1) {
        guidelines.push(`${SECTION_HEADER_COLLAB}\n${PREVIOUS_AGENT_HANDLING}`);
    }

    guidelines.push(`${SECTION_HEADER_FORMAT}\n${BASE_FORMAT}`);
    instructions.push(guidelines.join('\n\n'));
    logger.debug(`[getAgentInstructions] Agent ${agentNumber}: Assembled guidelines.`);

    // Combine all major parts
    const finalPrompt = instructions.join('\n\n');
    logger.debug(`[getAgentInstructions] Agent ${agentNumber}: Final System Prompt generation complete.`);
    // Optionally log the full prompt only at a higher debug level if needed, as it can be large
    // logger.trace(`[getAgentInstructions] Agent ${agentNumber}: Final System Prompt:\n---\n${finalPrompt}\n---`);

    return finalPrompt;
};

