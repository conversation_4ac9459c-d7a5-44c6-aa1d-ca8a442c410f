import React, { useState, useEffect, useCallback } from 'react';
import { EyeIcon, EyeSlashIcon, ArrowPathIcon } from '@heroicons/react/24/outline'; // Import icons, including ArrowPathIcon
import {
  getApi<PERSON><PERSON>s,
  add<PERSON>pi<PERSON>ey,
  update<PERSON><PERSON><PERSON><PERSON>,
  delete<PERSON><PERSON><PERSON><PERSON>,
  ApiKeyInfo,
  // ApiKeyResponse, // Removed as it's not explicitly used in this component
} from '../services/apiKeyApiService';
import Modal from './Modal'; // Re-use the existing Modal component
import { useConfigStore } from '../store/configStore'; // For global feedback

// Simple reusable form control component (similar to ConfigurationTabs)
const FormControl: React.FC<{ label: string; htmlFor: string; children: React.ReactNode; className?: string }> = ({ label, htmlFor, children, className = '' }) => (
    <div className={`mb-4 ${className}`}>
      <label htmlFor={htmlFor} className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
        {label}
      </label>
      {children}
    </div>
);

const ApiKeysSettingsTab: React.FC = () => {
  const [apiKeys, setApiKeys] = useState<ApiKeyInfo[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // State for the "Add New Key" form
  const [newServiceName, setNewServiceName] = useState('');
  const [newApiKey, setNewApiKey] = useState('');
  const [showNewApiKey, setShowNewApiKey] = useState(false); // State for showing/hiding new key
  const [isAdding, setIsAdding] = useState(false);

  // State for the "Update Key" modal
  const [isUpdateModalOpen, setIsUpdateModalOpen] = useState(false);
  const [keyToUpdate, setKeyToUpdate] = useState<ApiKeyInfo | null>(null);
  const [updateKeyValue, setUpdateKeyValue] = useState('');
  const [showUpdateKeyValue, setShowUpdateKeyValue] = useState(false); // State for showing/hiding update key
  const [isUpdating, setIsUpdating] = useState(false);

  // State for the "Delete Key" modal
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [keyToDelete, setKeyToDelete] = useState<ApiKeyInfo | null>(null);
  const [isDeleting, setIsDeleting] = useState(false);

  // Global feedback from Zustand store
  const { setGlobalSuccess, setGlobalError, clearGlobalMessages } = useConfigStore(state => ({
      setGlobalSuccess: state.setGlobalSuccess,
      setGlobalError: state.setGlobalError,
      clearGlobalMessages: state.clearGlobalMessages,
  }));

  // Fetch keys function
  const fetchKeys = useCallback(async () => {
    setIsLoading(true);
    setError(null);
    clearGlobalMessages(); // Clear global messages on fetch
    try {
      const keys = await getApiKeys();
      setApiKeys(keys);
    } catch (err: any) {
      console.error("Fetch API keys error:", err);
      const message = err.message || 'Failed to fetch API keys.';
      setError(message); // Set local error for display within the tab
      setGlobalError(message); // Also set global error
    } finally {
      setIsLoading(false);
    }
  }, [clearGlobalMessages, setGlobalError]); // Add dependencies

  // Fetch keys on component mount
  useEffect(() => {
    fetchKeys();
  }, [fetchKeys]); // Use fetchKeys in dependency array

  // --- Handlers ---

  const handleAddKey = async (e: React.FormEvent) => {
    e.preventDefault();
    clearGlobalMessages(); // Clear previous messages first

    const serviceNameTrimmed = newServiceName.trim();
    const apiKeyTrimmed = newApiKey.trim();

    if (!serviceNameTrimmed || !apiKeyTrimmed) {
      setGlobalError("Service name and API key cannot be empty.");
      return;
    }

    // Check for duplicate service name (case-insensitive)
    if (apiKeys.some(key => key.serviceName.toLowerCase() === serviceNameTrimmed.toLowerCase())) {
        setGlobalError(`An API key for service "${serviceNameTrimmed}" already exists.`);
        return;
    }

    setIsAdding(true);
    try {
      const addedKey = await addApiKey(newServiceName.trim(), newApiKey.trim());
      setGlobalSuccess(addedKey.message || 'API key added successfully!');
      setNewServiceName('');
      setNewApiKey('');
      setShowNewApiKey(false); // Hide key field after adding
      fetchKeys(); // Refresh the list
    } catch (err: any) {
      console.error("Add API key error:", err);
      setGlobalError(err.message || 'Failed to add API key.');
    } finally {
      setIsAdding(false);
    }
  };

  const handleOpenUpdateModal = (key: ApiKeyInfo) => {
    setKeyToUpdate(key);
    setUpdateKeyValue('');
    setShowUpdateKeyValue(false); // Ensure key is hidden initially
    setIsUpdateModalOpen(true);
    clearGlobalMessages();
  };

  const handleConfirmUpdate = async () => {
    if (!keyToUpdate || !updateKeyValue.trim()) {
       setGlobalError("New API key value cannot be empty.");
       return;
    }
    setIsUpdating(true);
    clearGlobalMessages();
    try {
        const updatedKey = await updateApiKey(keyToUpdate._id, updateKeyValue.trim());
        setGlobalSuccess(updatedKey.message || 'API key updated successfully!');
      setIsUpdateModalOpen(false);
      setKeyToUpdate(null);
      setUpdateKeyValue(''); // Clear value after update
      fetchKeys(); // Refresh the list
    } catch (err: any) {
        console.error("Update API key error:", err);
        setGlobalError(err.message || 'Failed to update API key.');
        // Keep modal open on error? Or close? Let's close it.
        setIsUpdateModalOpen(false);
        setKeyToUpdate(null);
    } finally {
        setIsUpdating(false);
    }
  };

  const handleOpenDeleteModal = (key: ApiKeyInfo) => {
    setKeyToDelete(key);
    setIsDeleteModalOpen(true);
    clearGlobalMessages();
  };

  const handleConfirmDelete = async () => {
    if (!keyToDelete) return;
    setIsDeleting(true);
    clearGlobalMessages();
    try {
        const result = await deleteApiKey(keyToDelete._id);
        setGlobalSuccess(result.message || 'API key deleted successfully!');
        setIsDeleteModalOpen(false);
        setKeyToDelete(null);
        fetchKeys(); // Refresh the list
    } catch (err: any) {
        console.error("Delete API key error:", err);
        setGlobalError(err.message || 'Failed to delete API key.');
        // Keep modal open on error? Or close? Let's close it.
        setIsDeleteModalOpen(false);
        setKeyToDelete(null);
    } finally {
        setIsDeleting(false);
    }
  };

  return (
    <div className="space-y-6">
      <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">API Key Management</h3>
      <p className="text-sm text-gray-600 dark:text-gray-400">
        Securely manage your API keys for external services (e.g., Serper, Google Search). Keys are encrypted at rest.
      </p>

      {/* Add New Key Form */}
      <form onSubmit={handleAddKey} className="p-4 border border-gray-200 dark:border-gray-700 rounded-lg bg-gray-50 dark:bg-gray-800/50 space-y-4">
        <h4 className="text-md font-medium text-gray-800 dark:text-gray-200">Add New API Key</h4>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <FormControl label="Service Name" htmlFor="newServiceName">
            <input
              type="text"
              id="newServiceName"
              value={newServiceName}
              onChange={(e) => setNewServiceName(e.target.value)}
              className="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
              placeholder="e.g., Serper, OpenAI"
              required
              disabled={isAdding}
            />
          </FormControl>
          <FormControl label="API Key" htmlFor="newApiKey" className="md:col-span-2">
            <div className="relative">
              <input
                type={showNewApiKey ? 'text' : 'password'} // Toggle type
                id="newApiKey"
                value={newApiKey}
                onChange={(e) => setNewApiKey(e.target.value)}
                className="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 pr-10" // Add padding for icon
                placeholder="Enter the API key value"
                required
                disabled={isAdding}
              />
              <button
                type="button"
                onClick={() => setShowNewApiKey(!showNewApiKey)}
                className="absolute inset-y-0 right-0 px-3 flex items-center text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
                title={showNewApiKey ? 'Hide key' : 'Show key'}
              >
                {showNewApiKey ? (
                  <EyeSlashIcon className="h-5 w-5" />
                ) : (
                  <EyeIcon className="h-5 w-5" />
                )}
              </button>
            </div>
          </FormControl>
        </div>
        <div className="flex justify-end">
          <button
            type="submit"
            disabled={isAdding || !newServiceName.trim() || !newApiKey.trim()}
            className="px-4 py-2 bg-primary text-white rounded hover:bg-primary-dark transition-colors text-sm disabled:opacity-50"
          >
            {isAdding ? 'Adding...' : 'Add Key'}
          </button>
        </div>
      </form>

      {/* List Existing Keys */}
      <div className="mt-6">
        <h4 className="text-md font-medium text-gray-800 dark:text-gray-200 mb-2">Saved API Keys</h4>
        {isLoading && (
            <div className="flex items-center space-x-2 text-sm text-gray-500 dark:text-gray-400 italic">
                <ArrowPathIcon className="h-4 w-4 animate-spin" />
                <span>Fetching keys...</span>
            </div>
        )}
        {error && !isLoading && <p className="text-sm text-red-600 dark:text-red-400">Error fetching keys: {error}</p>}
        {!isLoading && !error && apiKeys.length === 0 && (
          <p className="text-sm text-gray-500 dark:text-gray-400 italic">No API keys saved yet.</p>
        )}
        {!isLoading && !error && apiKeys.length > 0 && (
          <ul className="space-y-2">
            {apiKeys.map((key) => (
              <li key={key._id} className="p-3 border border-gray-200 dark:border-gray-700 rounded-md flex items-center justify-between bg-white dark:bg-gray-700/50">
                <div>
                  <span className="font-medium text-gray-900 dark:text-gray-100">{key.serviceName}</span>
                  <p className="text-xs text-gray-500 dark:text-gray-400">
                    Added: {new Date(key.createdAt).toLocaleString()} | Updated: {new Date(key.updatedAt).toLocaleString()}
                  </p>
                </div>
                <div className="flex space-x-2 flex-shrink-0">
                  <button
                    onClick={() => handleOpenUpdateModal(key)}
                    className="px-2 py-1 text-xs bg-yellow-100 text-yellow-800 rounded hover:bg-yellow-200 dark:bg-yellow-900/50 dark:text-yellow-300 dark:hover:bg-yellow-800/50 disabled:opacity-50"
                    disabled={isUpdating || isDeleting}
                  >
                    Update
                  </button>
                  <button
                    onClick={() => handleOpenDeleteModal(key)}
                    className="px-2 py-1 text-xs bg-red-100 text-red-700 rounded hover:bg-red-200 dark:bg-red-900/50 dark:text-red-300 dark:hover:bg-red-800/50 disabled:opacity-50"
                    disabled={isUpdating || isDeleting}
                  >
                    Delete
                  </button>
                </div>
              </li>
            ))}
          </ul>
        )}
      </div>

      {/* Update Key Modal */}
      <Modal
        isOpen={isUpdateModalOpen}
        onClose={() => setIsUpdateModalOpen(false)}
        title={`Update API Key for ${keyToUpdate?.serviceName || ''}`}
        footer={
          <>
            <button
              type="button"
              onClick={() => setIsUpdateModalOpen(false)}
              disabled={isUpdating}
              className="px-4 py-2 bg-gray-200 dark:bg-gray-600 text-gray-700 dark:text-gray-200 rounded hover:bg-gray-300 dark:hover:bg-gray-500 transition-colors text-sm disabled:opacity-50"
            >
              Cancel
            </button>
            <button
              type="button"
              onClick={handleConfirmUpdate}
              disabled={isUpdating || !updateKeyValue.trim()}
              className="px-4 py-2 bg-primary text-white rounded hover:bg-primary-dark transition-colors text-sm disabled:opacity-50"
            >
              {isUpdating ? 'Updating...' : 'Update Key'}
            </button>
          </>
        }
      >
        <FormControl label="New API Key Value" htmlFor="updateKeyValue">
          <div className="relative">
            <input
              type={showUpdateKeyValue ? 'text' : 'password'} // Toggle type
              id="updateKeyValue"
              value={updateKeyValue}
              onChange={(e) => setUpdateKeyValue(e.target.value)}
              className="block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 pr-10" // Add padding for icon
              placeholder="Enter the new API key"
              required
            />
            <button
              type="button"
              onClick={() => setShowUpdateKeyValue(!showUpdateKeyValue)}
              className="absolute inset-y-0 right-0 px-3 flex items-center text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
              title={showUpdateKeyValue ? 'Hide key' : 'Show key'}
            >
              {showUpdateKeyValue ? (
                <EyeSlashIcon className="h-5 w-5" />
              ) : (
                <EyeIcon className="h-5 w-5" />
              )}
            </button>
          </div>
        </FormControl>
      </Modal>

      {/* Delete Confirmation Modal */}
      <Modal
        isOpen={isDeleteModalOpen}
        onClose={() => setIsDeleteModalOpen(false)}
        title="Confirm Deletion"
        footer={
          <>
            <button
              type="button"
              onClick={() => setIsDeleteModalOpen(false)}
              disabled={isDeleting}
              className="px-4 py-2 bg-gray-200 dark:bg-gray-600 text-gray-700 dark:text-gray-200 rounded hover:bg-gray-300 dark:hover:bg-gray-500 transition-colors text-sm disabled:opacity-50"
            >
              Cancel
            </button>
            <button
              type="button"
              onClick={handleConfirmDelete}
              disabled={isDeleting}
              className="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700 transition-colors text-sm disabled:opacity-50"
            >
              {isDeleting ? 'Deleting...' : 'Delete'}
            </button>
          </>
        }
      >
        <p className="text-sm text-gray-700 dark:text-gray-300">
          Are you sure you want to delete the API key for "{keyToDelete?.serviceName}"? This action cannot be undone.
        </p>
      </Modal>

    </div>
  );
};

export default ApiKeysSettingsTab;
