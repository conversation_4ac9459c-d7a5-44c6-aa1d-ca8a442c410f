import { Socket } from 'socket.io';
// Import both text and image generation functions/types
import { callLL<PERSON>pi, LLMServiceInput, generateImageWithLLM, ImageGenerationInput, ImageGenerationResult } from './llmService';
import { IncomingConfigData } from '../server';
// Import ConversationManager, but ConversationMessage type will come from the model now
import { ConversationManager } from '../managers/conversationManager';
import Api<PERSON><PERSON> from '../models/ApiKey'; // Import ApiKey model
import { decrypt } from '../utils/cryptoUtils'; // Import decrypt utility
import { getAgentInstructions } from '../utils/instructionUtils';
import { SearchResult, performInternetSearch } from './internetSearchService'; // Import performInternetSearch here
import { processSearchResults } from './searchAgentService'; // Import search agent service
import { Rag<PERSON>and<PERSON> } from '../services/ragHandler';
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>g<PERSON><PERSON><PERSON>, <PERSON>Error, BaseError } from '../utils/errors'; // <<< Import custom errors
// Import Conversation model AND the updated ConversationMessage interface from it
import Conversation, { IConversation } from '../models/Conversation';

// Define ConversationMessage based on the schema's subdocument type if possible, or re-declare matching structure
// Re-declaring might be simpler if direct type inference is complex
interface ConversationMessage { // Keep this interface aligned with the schema
    role: 'user' | 'assistant' | 'system' | 'tool';
    content: string;
    agentName?: string;
    timestamp: string;
    localImagePath?: string;
    imageUrl?: string;
    imageDataUrl?: string;
}

/**
 * Fetches and decrypts an API key for a given user and service.
 * @param userId - The ID of the user.
 * @param serviceName - The name of the service (e.g., 'OPENAI_API_KEY').
 * @returns The decrypted API key as a string, or undefined if not found or decryption fails.
 */
async function _getDecryptedApiKey(userId: string, serviceName: string): Promise<string | undefined> {
    if (!userId || !serviceName) {
        console.warn(`[API Key Helper] Missing userId or serviceName.`);
        return undefined;
    }
    try {
        const apiKeyRecord = await ApiKey.findOne({ userId: userId, serviceName: serviceName });
        if (apiKeyRecord?.apiKey) {
            const decryptedResult = decrypt(apiKeyRecord.apiKey);
            if (decryptedResult !== null) {
                return decryptedResult;
            } else {
                console.error(`[API Key Helper] Decryption failed for service: ${serviceName}, user: ${userId}.`);
            }
        } else {
            // console.log(`[API Key Helper] No key found in DB for service: ${serviceName}, user: ${userId}.`); // Less verbose logging
        }
    } catch (keyError: any) {
        console.error(`[API Key Helper] Error fetching/decrypting key for ${serviceName}:`, keyError);
    }
    return undefined; // Return undefined if not found or error
}

/**
 * Loads an existing conversation history or starts a new one.
 * Adds the initial user prompt to the history.
 * Emits an 'update_agents_discussion' event for the user prompt.
 *
 * @param userId - The ID of the user.
 * @param conversationId - The ID of the conversation to load (if any).
 * @param initialPrompt - The user's initial prompt text.
 * @param imageDataUrl - Optional base64 data URL of an image attached to the prompt.
 * @param conversationManager - The ConversationManager instance to manage history.
 * @param socket - The Socket.IO socket instance for emitting updates.
 * @returns An object containing the final conversation ID and a flag indicating if it's a new conversation.
 */
async function _loadOrCreateConversation(
    userId: string,
    conversationId: string | undefined,
    initialPrompt: string,
    imageDataUrl: string | null | undefined,
    conversationManager: ConversationManager,
    socket: Socket
): Promise<{ conversationId: string | undefined; isNewConversation: boolean }> {
    let currentConversationId = conversationId;
    let isNewConversation = !conversationId;

    if (isNewConversation) {
        console.log("Starting new conversation for user:", userId);
        conversationManager.startNewConversation(initialPrompt, imageDataUrl);
        socket.emit('update_agents_discussion', { agentName: 'User', content: initialPrompt, type: 'user', hasImage: !!imageDataUrl });
    } else if (currentConversationId) {
        console.log(`Loading existing conversation: ${currentConversationId} for user: ${userId}`);
        try {
            const existingConversation = await Conversation.findOne({ _id: currentConversationId, userId: userId });
            if (existingConversation?.messages) { // Check messages exist
                const loadedMessages = existingConversation.messages.map(msg => ({
                    role: msg.role,
                    content: msg.content,
                    agentName: msg.agentName,
                    // Ensure all required fields from ConversationMessage are mapped
                    timestamp: msg.timestamp || new Date(0).toISOString(),
                    localImagePath: msg.localImagePath,
                    imageUrl: msg.imageUrl,
                    imageDataUrl: msg.imageDataUrl,
                }));
                conversationManager.loadHistory(loadedMessages);
                console.log(`Loaded ${existingConversation.messages.length} messages.`);
                // Add the *current* user prompt to the loaded history
                conversationManager.addMessage(initialPrompt, 'user', 'User', imageDataUrl);
                socket.emit('update_agents_discussion', { agentName: 'User', content: initialPrompt, type: 'user', hasImage: !!imageDataUrl });
            } else {
                console.warn(`Conversation ${currentConversationId} not found for user ${userId} or has no messages. Starting new.`);
                isNewConversation = true;
                currentConversationId = undefined; // Reset ID as we are starting new
                conversationManager.startNewConversation(initialPrompt, imageDataUrl);
                socket.emit('update_agents_discussion', { agentName: 'User', content: initialPrompt, type: 'user', hasImage: !!imageDataUrl });
            }
        } catch (error: any) {
            console.error(`Error loading conversation ${currentConversationId} for user ${userId}:`, error);
            isNewConversation = true;
            currentConversationId = undefined; // Reset ID
            conversationManager.startNewConversation(initialPrompt, imageDataUrl);
            socket.emit('update_agents_discussion', { agentName: 'User', content: initialPrompt, type: 'user', hasImage: !!imageDataUrl });
        }
    } else {
        // This case should ideally not happen if conversationId is managed correctly, but handle defensively
        console.error("Orchestrator called without conversationId for existing chat logic path. Starting new.");
        isNewConversation = true;
        conversationManager.startNewConversation(initialPrompt, imageDataUrl);
        socket.emit('update_agents_discussion', { agentName: 'User', content: initialPrompt, type: 'user', hasImage: !!imageDataUrl });
    }
    return { conversationId: currentConversationId, isNewConversation };
}

/**
 * Handles image generation requests. Detects intent, finds a suitable provider,
 * extracts the prompt, calls the LLM service, adds results/errors to history,
 * and emits socket events.
 *
 * @param initialPrompt - The original user prompt.
 * @param lowerCasePrompt - The lowercased user prompt for keyword matching.
 * @param imageGenKeywords - An array of keywords indicating image generation intent.
 * @param config - The current application configuration.
 * @param options - Orchestration options including socket and cancellation check.
 * @param userId - The ID of the user.
 * @param providerToServiceNameMap - Mapping from provider names to service names for API keys.
 * @param conversationManager - The ConversationManager instance.
 * @returns A promise resolving to the local path of the generated image, or an empty string if failed/cancelled.
 */
async function _handleImageGeneration(
    initialPrompt: string,
    lowerCasePrompt: string,
    imageGenKeywords: string[],
    config: IncomingConfigData,
    options: AgentOrchestrationOptions,
    userId: string,
    providerToServiceNameMap: { [key: string]: string },
    conversationManager: ConversationManager
): Promise<string> { // Returns the local image path or empty string
    const { socket, isCancelled } = options;
    console.log(`[Orchestrator] Handling image generation request for user ${userId}.`);
    if (isCancelled()) { console.log("Orchestration cancelled before image generation."); return ""; }

    // --- Configuration & Constants ---
    const LOG_PREFIX = "[Orchestrator Image Gen]";
    const ALLOWED_IMAGE_PROVIDERS = ['openai', 'google']; // Use lowercase
    const ERROR_MSG_NO_PROVIDER = "Image generation requires an agent configured with an allowed provider (OpenAI or Google).";
    const FALLBACK_IMAGE_PROMPT = "A generic, visually interesting image.";

    // --- Helper Function for Prompt Extraction ---
    /**
     * Attempts to extract the core image prompt by removing a leading keyword.
     * @param {string} text The full user input string.
     * @param {string[]} keywords List of keywords to check for at the beginning.
     * @returns {{ extractedPrompt: string, keywordFound: string | null }} Object containing the extracted prompt and the keyword found (or null).
     */
    function extractPromptFromLeadingKeyword(text: string, keywords: string[]): { extractedPrompt: string, keywordFound: string | null } {
        const lowerCaseText = text.toLowerCase().trim(); // Work with lowercase and trimmed

        for (const keyword of keywords) {
            const lowerCaseKeyword = keyword.toLowerCase(); // Ensure keyword is lowercase for comparison
            // Check if text starts with the keyword followed by a space, or if it's exactly the keyword
            if (lowerCaseText.startsWith(lowerCaseKeyword + ' ') || lowerCaseText === lowerCaseKeyword) {
                // Build regex to remove keyword, optional articles/prepositions, and leading space
                // Example: "generate image of a cat" -> removes "generate image of "
                // Example: "draw a dog" -> removes "draw "
                const regex = new RegExp(`^${keyword}\\s*(?:of|a|an|the)?\\s*`, 'i');
                const potentialPrompt = text.replace(regex, '').trim();

                // Return the potentially shortened prompt only if it's not empty
                return {
                    extractedPrompt: potentialPrompt.length > 0 ? potentialPrompt : text.trim(), // Fallback to original if extraction empties it
                    keywordFound: keyword
                };
            }
        }

        // No leading keyword found from the list
        return { extractedPrompt: text.trim(), keywordFound: null };
    }

    // --- Main Logic ---

    // 1. Find Agent Configuration
    const lowerCaseAllowedProviders = ALLOWED_IMAGE_PROVIDERS; // Already lowercase
    const imageGenAgentConfig = config.agentConfigurations.find(agent =>
        lowerCaseAllowedProviders.includes(agent.provider.toLowerCase())
    );

    // 2. Handle Missing Configuration
    if (!imageGenAgentConfig) {
        console.error(`${LOG_PREFIX} ${ERROR_MSG_NO_PROVIDER}`);
        socket.emit('image_generated', { error: ERROR_MSG_NO_PROVIDER, key: `error_${Date.now()}` });
        conversationManager.addMessage(`Configuration Error: ${ERROR_MSG_NO_PROVIDER}`, 'system', 'Error');
        return ""; // Or potentially return null or throw an error depending on caller expectation
    }

    // 3. Determine Image Prompt
    let imagePrompt = "";
    const trimmedInitialPrompt = initialPrompt.trim(); // Start with trimmed prompt

    if (options.isExplicitImageRequest) {
        // Use full prompt for explicit requests
        imagePrompt = trimmedInitialPrompt;
        console.log(`${LOG_PREFIX} Using full prompt for explicit image request: "${imagePrompt}"`);
    } else if (imageGenKeywords && imageGenKeywords.length > 0) {
        // Attempt keyword-based extraction only from the start
        const { extractedPrompt, keywordFound } = extractPromptFromLeadingKeyword(trimmedInitialPrompt, imageGenKeywords);

        if (keywordFound) {
            if (extractedPrompt === trimmedInitialPrompt) {
                // This case means extraction resulted in an empty prompt, so we reverted to the original
                console.warn(`${LOG_PREFIX} Keyword "${keywordFound}" found, but extraction resulted in an empty prompt. Using full prompt: "${trimmedInitialPrompt}"`);
                imagePrompt = trimmedInitialPrompt;
            } else {
                imagePrompt = extractedPrompt;
                console.log(`${LOG_PREFIX} Extracted prompt after removing leading keyword "${keywordFound}": "${imagePrompt}"`);
            }
        } else {
            // Keywords were defined, but none matched the start of the prompt
            // Try to find a keyword anywhere in the prompt as a fallback
            const matchedKeyword = imageGenKeywords.find(keyword => lowerCasePrompt.includes(keyword));
            if (matchedKeyword) {
                console.warn(`${LOG_PREFIX} No leading keyword matched, but found "${matchedKeyword}" in prompt. Using full prompt: "${trimmedInitialPrompt}"`);
            } else {
                console.warn(`${LOG_PREFIX} Intent detected but failed to identify any triggering keyword. Using full prompt.`);
            }
            imagePrompt = trimmedInitialPrompt;
        }
    } else {
        // No keywords defined or it's not an explicit request - use the initial prompt
        imagePrompt = trimmedInitialPrompt;
        console.log(`${LOG_PREFIX} No keywords defined or not an explicit request. Using full prompt: "${imagePrompt}"`);
    }

    // 4. Fallback for Empty Prompt
    if (!imagePrompt.trim()) { // Check if the final prompt is empty (e.g., if initialPrompt was just whitespace)
        imagePrompt = FALLBACK_IMAGE_PROMPT;
        console.log(`${LOG_PREFIX} Prompt was empty after processing, using fallback: "${imagePrompt}"`);
    }

    // Get API Key
    const providerKey = imageGenAgentConfig.provider.toLowerCase();
    const serviceName = providerToServiceNameMap[providerKey];
    const decryptedApiKey = await _getDecryptedApiKey(userId, serviceName);

    // Parse image size from prompt if specified
    let imageSize = '1024x1024'; // Default size
    let imageQuality = 'standard'; // Default quality
    let imageStyle = 'vivid'; // Default style

    // Check for size specification in the prompt
    const sizeMatch = imagePrompt.match(/\b(\d+x\d+)\b/i);
    if (sizeMatch) {
        const potentialSize = sizeMatch[1];
        // Validate size - only accept common sizes
        const validSizes = ['256x256', '512x512', '1024x1024', '1024x1792', '1792x1024'];
        if (validSizes.includes(potentialSize)) {
            imageSize = potentialSize;
            console.log(`${LOG_PREFIX} Detected image size in prompt: ${imageSize}`);
            // Remove the size from the prompt
            imagePrompt = imagePrompt.replace(sizeMatch[0], '').trim();
        }
    }

    // Check for quality specification in the prompt
    if (imagePrompt.toLowerCase().includes('hd') ||
        imagePrompt.toLowerCase().includes('high quality') ||
        imagePrompt.toLowerCase().includes('high-quality')) {
        imageQuality = 'hd';
        console.log(`${LOG_PREFIX} Detected HD quality request in prompt`);
        // Remove quality indicators from prompt
        imagePrompt = imagePrompt.replace(/\b(hd|high quality|high-quality)\b/gi, '').trim();
    }

    // Check for style specification in the prompt
    if (imagePrompt.toLowerCase().includes('natural') ||
        imagePrompt.toLowerCase().includes('realistic')) {
        imageStyle = 'natural';
        console.log(`${LOG_PREFIX} Detected natural style request in prompt`);
        // Remove style indicators from prompt
        imagePrompt = imagePrompt.replace(/\b(natural|realistic)\b/gi, '').trim();
    }

    const imageGenInput: ImageGenerationInput = {
        provider: imageGenAgentConfig.provider,
        model: imageGenAgentConfig.model,
        prompt: imagePrompt,
        apiKey: decryptedApiKey,
        isCancelled: isCancelled,
        size: imageSize,
        quality: imageQuality,
        style: imageStyle
    };

    let localImagePathSaved: string | undefined = undefined;
    const uniqueImageGenKey = `image_gen_${Date.now()}`;

    try {
        socket.emit('update_agents_discussion', {
            agentName: 'System',
            content: `Generating image with ${imageGenAgentConfig.provider} (Size: ${imageSize}, Quality: ${imageQuality}, Style: ${imageStyle})...`,
            type: 'system',
            key: `system_msg_${uniqueImageGenKey}`
        });
        const result: ImageGenerationResult = await generateImageWithLLM(imageGenInput);

        if (isCancelled()) {
             console.log("Orchestration cancelled just after successful image generation (before emitting).");
             conversationManager.addMessage(`(Image generation completed but cancelled before update)`, 'system', 'System');
        } else if (result.localImagePath) {
            localImagePathSaved = result.localImagePath;
            console.log("[Orchestrator Image Gen] Success. Local path:", localImagePathSaved);
            socket.emit('image_generated', {
                localImagePath: localImagePathSaved,
                imageUrl: result.imageUrl,
                imageDataUrl: result.imageDataUrl,
                key: uniqueImageGenKey
            });
            const imageRepresentation = `Generated Image (Size: ${imageSize}, Quality: ${imageQuality}, Style: ${imageStyle}): ${localImagePathSaved}`;
            conversationManager.addMessage(
                imageRepresentation, 'assistant', 'Image Generator', null,
                localImagePathSaved, result.imageUrl, result.imageDataUrl
            );
        } else {
             console.warn(`[Orchestrator Image Gen] Image generation succeeded but no local path was returned.`, result);
             // Add a message indicating success but no viewable image?
             conversationManager.addMessage(`(Image generated successfully, but could not be saved locally for display)`, 'system', 'System');
        }
    } catch (genError: any) {
         if (isCancelled() && !(genError instanceof LlmError && genError.code === 'CANCELLED')) {
              console.log("Orchestration cancelled during image generation await.");
              conversationManager.addMessage(`(Image generation cancelled by user)`, 'system', 'System');
         } else if (genError instanceof LlmError) {
              console.error(`[Orchestrator Image Gen] LlmError: ${genError.message} (Code: ${genError.code})`, genError.details);
              socket.emit('llm_error', { message: genError.message, code: genError.code, context: 'image_generation' });
              socket.emit('image_generated', { error: genError.message, key: uniqueImageGenKey });
              conversationManager.addMessage(`Image Generation Error: ${genError.message}`, 'system', 'Error');
         } else {
              const message = genError instanceof Error ? genError.message : String(genError);
              console.error(`[Orchestrator Image Gen] Unexpected error calling generateImageWithLLM:`, genError);
              const fatalErrorMsg = `Unexpected error during image generation: ${message}`;
              socket.emit('processing_error', { message: fatalErrorMsg, context: 'image_generation' });
              socket.emit('image_generated', { error: fatalErrorMsg, key: uniqueImageGenKey });
              conversationManager.addMessage(`System Error: ${fatalErrorMsg}`, 'system', 'Error');
         }
    }
    // Return the path (or empty string) to the main function
    return localImagePathSaved || "";
}


// --- Initialize RagHandler (Simple global instance for now) ---
/** Global instance of the RAG handler. */
export const ragHandlerInstance = new RagHandler();
// --- End Initialization ---

// Define options for the orchestrator, including cancellation
export interface AgentOrchestrationOptions {
    socket: Socket; // Socket now includes user property after socketProtect middleware
    conversationId?: string;
    isCancelled: () => boolean; // Function to check if cancellation was requested
    isExplicitImageRequest?: boolean; // Flag for explicit image generation request from UI
}

/**
 * Main orchestration function for handling user prompts.
 * Determines if the request is for image generation or text generation,
 * loads/creates conversation history, calls relevant helper functions,
 * saves the final history, and returns the result.
 *
 * @param initialPrompt - The user's initial prompt text.
 * @param config - The configuration settings for the agents and features.
 * @param options - Options object including socket, conversationId, and cancellation check.
 * @param imageDataUrl - Optional base64 data URL of an image attached to the prompt.
 * @returns A promise that resolves with the final answer (text or image path), or an empty string if cancelled.
 */
export const runAgentOrchestration = async (
    initialPrompt: string,
    config: IncomingConfigData,
    options: AgentOrchestrationOptions,
    imageDataUrl?: string | null // Renamed parameter to match server.ts
): Promise<string> => { // This function primarily returns the final *text* answer
    const { socket, conversationId, isCancelled } = options; // Destructure options
    const userId = socket.user?.id; // Get userId from authenticated socket

    console.log(`Starting agent orchestration... (User ID: ${userId}, Conversation ID: ${conversationId || 'New'})`);

    // --- Authorization Check ---
    if (!userId) {
        console.error("Orchestration Error: User ID not found on authenticated socket.");
        socket.emit('processing_error', { message: 'Authentication error: User ID missing.' });
        return "";
    }
    // --- End Authorization Check ---

    // --- Improved Image Generation Intent Detection ---
    // Check for explicit image request flag first
    const isExplicitImageRequest = options.isExplicitImageRequest || false;

    // Only use keyword detection as fallback if not explicitly requested
    let isKeywordBasedImageRequest = false;
    if (!isExplicitImageRequest) {
        const imageGenKeywords = [
            'generate image', 'create image', 'make image', 'image of',
            'draw picture', 'create picture', 'generate picture', 'make picture', 'picture of',
            'draw', 'sketch', 'illustrate', 'photo of',
            // Removing more ambiguous keywords that might trigger false positives
            // 'put', 'add',
            'modify image', 'edit image',
            'similar picture', 'another picture like', 'image like this'
        ];
        const lowerCasePrompt = initialPrompt.toLowerCase().trim();
        const startsWithKeyword = imageGenKeywords.some(keyword => lowerCasePrompt.startsWith(keyword + ' '));
        const containsKeyword = imageGenKeywords.some(keyword => lowerCasePrompt.includes(keyword));
        isKeywordBasedImageRequest = startsWithKeyword || containsKeyword;
    }

    // Combine both detection methods, prioritizing explicit requests
    const isImageGenerationRequest = isExplicitImageRequest || isKeywordBasedImageRequest;

    if (isExplicitImageRequest) {
        console.log(`[Intent Detection] Explicit image generation request received for prompt: "${initialPrompt.substring(0, 50)}..."`);
    } else if (isKeywordBasedImageRequest) {
        console.log(`[Intent Detection] Image generation intent DETECTED via keywords for prompt: "${initialPrompt.substring(0, 50)}..."`);
    } else {
        console.log(`[Intent Detection] Image generation intent NOT detected for prompt: "${initialPrompt.substring(0, 50)}..."`);
    }
    // --- End Intent Detection ---

    // Moved provider map inside helper or keep global if used elsewhere? Keep global for now.
    const providerToServiceNameMap: { [key: string]: string } = {
        'openai': 'OPENAI_API_KEY',
        'anthropic': 'ANTHROPIC_API_KEY',
        'google': 'GOOGLE_API_KEY',
        'groq': 'GROQ_API_KEY',
        'deepseek': 'DEEPSEEK_API_KEY',
        'openrouter': 'OPENROUTER_API_KEY',
    };

    // Create ConversationManager with token limit settings from config
    const maxContextWindow = config.maxContextWindow || 20000;
    const workingContextSize = config.workingContextSize || 16384;
    const conversationManager = new ConversationManager(maxContextWindow, workingContextSize);
    let currentConversationId = conversationId;
    let isNewConversation = !conversationId;

    const agentCount = config.agentConfigurations.length;

    // 1. Load or Create Conversation
    const convResult = await _loadOrCreateConversation(userId, conversationId, initialPrompt, imageDataUrl, conversationManager, socket);
    currentConversationId = convResult.conversationId;
    isNewConversation = convResult.isNewConversation;

    // --- Handle Image Generation Request ---
    if (isImageGenerationRequest) {
        // For explicit image requests, we don't need to extract keywords from the prompt
        // Just pass the full prompt to the image generation function
        const lowerCasePrompt = initialPrompt.toLowerCase().trim();
        const imageGenKeywords = isExplicitImageRequest ? [] : [
            'generate image', 'create image', 'make image', 'image of',
            'draw picture', 'create picture', 'generate picture', 'make picture', 'picture of',
            'draw', 'sketch', 'illustrate', 'photo of',
            'modify image', 'edit image',
            'similar picture', 'another picture like', 'image like this'
        ];

        const imageResult = await _handleImageGeneration(
            initialPrompt,
            lowerCasePrompt,
            imageGenKeywords,
            config,
            options,
            userId,
            providerToServiceNameMap,
            conversationManager
        );
        // Save history regardless of image generation success/failure/cancellation
        await saveConversationHistory(
            userId, currentConversationId, isNewConversation,
            conversationManager, config, socket, isCancelled() // Pass cancellation status
        );
        return imageResult; // Return path or empty string
    }
    // --- Text Generation Flow ---
    else {
        const textResult = await _handleTextGeneration(
            initialPrompt,
            imageDataUrl, // Pass image data for potential use in first turn
            config,
            options,
            userId,
            providerToServiceNameMap,
            conversationManager
        );
        // Save history regardless of text generation success/failure/cancellation
        await saveConversationHistory(
            userId, currentConversationId, isNewConversation,
            conversationManager, config, socket, isCancelled() // Pass cancellation status
        );
        return textResult; // Return final answer or empty string
    }
};

/**
 * Handles the main logic for text generation, including pre-processing and agent turns.
 *
 * @param initialPrompt - The user's initial prompt.
 * @param imageDataUrl - Optional image data for the first turn.
 * @param config - The application configuration.
 * @param options - Orchestration options (socket, cancellation check).
 * @param userId - The ID of the user.
 * @param providerToServiceNameMap - Mapping of provider names to service names.
 * @param conversationManager - The conversation manager instance.
 * @returns A promise resolving to the final text answer or an empty string if cancelled/failed.
 */
async function _handleTextGeneration(
    initialPrompt: string,
    imageDataUrl: string | null | undefined, // Image data for first turn
    config: IncomingConfigData,
    options: AgentOrchestrationOptions,
    userId: string,
    providerToServiceNameMap: { [key: string]: string },
    conversationManager: ConversationManager
): Promise<string> {
    const { socket, isCancelled } = options;
    console.log("[Orchestrator] Handling text generation flow.");

    // Log image data information
    if (imageDataUrl) {
        console.log("[Orchestrator] Image data URL provided for text generation flow.");
        console.log("[Orchestrator] Image data URL length:", imageDataUrl.length);
        console.log("[Orchestrator] Image data URL first 100 chars:", imageDataUrl.substring(0, 100));
    } else {
        console.log("[Orchestrator] No image data URL provided for text generation flow.");
    }

    // 1. Perform Pre-processing (Search & RAG)
    socket.emit('update_agents_discussion', { agentName: 'System', content: `(Analyzing prompt and preparing context...)`, type: 'system' });
    const searchResults = await _performPreProcessing(
        initialPrompt, config, options, userId, conversationManager
    );
    if (isCancelled()) return ""; // Check cancellation after pre-processing

    // 2. Run Agent Turns
    const agentTurnsCompleted = await _runAgentTurns(
        config, options, userId, providerToServiceNameMap, conversationManager, searchResults, imageDataUrl
    );
    if (isCancelled() || !agentTurnsCompleted) {
        console.log(`Orchestration cancelled or agent turns did not complete.`);
        return ""; // Return empty if cancelled or turns failed/stopped early
    }

    // 3. Determine Final Answer
    const finalHistory = conversationManager.getFullHistory();
    const assistantMessages = finalHistory.filter((msg: ConversationMessage) => msg.role === 'assistant');
    // Find the last non-image-generator assistant message
    const lastTextMessage = assistantMessages.reverse().find(msg => msg.agentName !== 'Image Generator');
    const finalAnswer = lastTextMessage?.content || "No final text answer could be generated.";

    console.log('Agent orchestration finished (Text Flow).');
    return finalAnswer;
}

/**
 * Performs pre-processing steps like internet search and RAG retrieval if enabled.
 * Adds the retrieved context as 'tool' messages to the conversation history.
 *
 * @param initialPrompt - The user's initial prompt.
 * @param config - The application configuration.
 * @param options - Orchestration options (socket, cancellation check).
 * @param userId - The ID of the user.
 * @param conversationManager - The conversation manager instance.
 * @returns A promise resolving to an array of SearchResult objects from the internet search (if performed).
 */
async function _performPreProcessing(
    initialPrompt: string,
    config: IncomingConfigData,
    options: AgentOrchestrationOptions,
    userId: string,
    conversationManager: ConversationManager
): Promise<SearchResult[]> {
    const { socket, isCancelled } = options;
    let searchResults: SearchResult[] = [];

    // Internet Search
    if (config.internetSettings?.enabled) {
        if (isCancelled()) { console.log("Orchestration cancelled before internet search."); return []; }
        try {
            console.log(`[PreProcessing] Internet search enabled for user ${userId}. Performing search...`);
            searchResults = await performInternetSearch(
                initialPrompt, config.internetSettings.searchApiKey,
                config.internetSettings.searchProvider, config.internetSettings.googleCxId,
                isCancelled
            );
            if (isCancelled()) { console.log("Orchestration cancelled after internet search."); return []; }
            console.log(`[PreProcessing] Search results obtained: ${searchResults.length}`);

            if (searchResults.length > 0) {
                // Process search results with the dedicated search agent
                try {
                    console.log("[PreProcessing] Using search agent to process results...");
                    socket.emit('update_agents_discussion', { agentName: 'System', content: `(Processing search results with dedicated agent...)`, type: 'system' });

                    // Configure the search agent
                    // First, check if there's a matching agent configuration we can use
                    let searchAgentConfig;

                    // Try to find an agent with the same provider as the search agent settings
                    const matchingAgentConfig = config.agentConfigurations.find(agent =>
                        agent.provider.toLowerCase() === (config.searchAgentSettings?.provider || '').toLowerCase());

                    if (matchingAgentConfig && config.searchAgentSettings) {
                        // Use the matching agent's provider and model, but keep the search agent's temperature and maxTokens
                        searchAgentConfig = {
                            provider: matchingAgentConfig.provider,
                            model: matchingAgentConfig.model,
                            temperature: config.searchAgentSettings.temperature || 0.3,
                            maxTokens: config.searchAgentSettings.maxTokens || 4000,
                            systemPrompt: config.searchAgentSettings.systemPrompt
                        };
                        console.log(`[PreProcessing] Using matching agent configuration for search agent: provider=${matchingAgentConfig.provider}, model=${matchingAgentConfig.model}`);
                    } else if (config.searchAgentSettings) {
                        // Use the search agent settings as is
                        searchAgentConfig = {
                            provider: config.searchAgentSettings.provider || 'Google',
                            model: config.searchAgentSettings.model || 'gemini-2.0-flash',
                            temperature: config.searchAgentSettings.temperature || 0.3,
                            maxTokens: config.searchAgentSettings.maxTokens || 4000, // Increased from 1500 to 4000 for better news summaries
                            systemPrompt: config.searchAgentSettings.systemPrompt
                        };
                        console.log(`[PreProcessing] Using configured search agent settings: provider=${searchAgentConfig.provider}, model=${searchAgentConfig.model}`);
                    } else {
                        // Fallback to default settings
                        searchAgentConfig = {
                            provider: 'Google',
                            model: 'gemini-2.0-flash',
                            temperature: 0.3,
                            maxTokens: 4000, // Increased from 1500 to 4000 for better news summaries
                            systemPrompt: undefined // Use default prompt from searchAgentService
                        };
                        console.log(`[PreProcessing] Using default search agent settings: provider=${searchAgentConfig.provider}, model=${searchAgentConfig.model}`);
                    }

                    console.log(`[PreProcessing] Final search agent configuration:`, {
                        provider: searchAgentConfig.provider,
                        model: searchAgentConfig.model,
                        temperature: searchAgentConfig.temperature,
                        maxTokens: searchAgentConfig.maxTokens,
                        hasSystemPrompt: !!searchAgentConfig.systemPrompt
                    });
                    console.log(`[PreProcessing] Original config.searchAgentSettings:`, config.searchAgentSettings);

                    // Process the search results
                    const processedResults = await processSearchResults(
                        searchResults,
                        initialPrompt,
                        userId,
                        searchAgentConfig
                    );

                    if (processedResults.success) {
                        // Add the synthesized content to the conversation
                        const searchContext = processedResults.synthesizedContent;
                        conversationManager.addMessage(searchContext, 'tool', 'Search Agent');

                        // Add sources as a separate message if available
                        if (processedResults.sources && processedResults.sources.length > 0) {
                            const sourcesMessage = "Sources:\n" + processedResults.sources.map(url => `- ${url}`).join('\n');
                            conversationManager.addMessage(sourcesMessage, 'tool', 'Search Sources');
                        }

                        socket.emit('update_agents_discussion', {
                            agentName: 'System',
                            content: `(Search agent processed ${searchResults.length} results)`,
                            type: 'system'
                        });
                        console.log("[PreProcessing] Added search agent's synthesized results to context.");
                    } else {
                        // Fallback to the original method if search agent fails
                        console.warn("[PreProcessing] Search agent failed, falling back to basic search results.");
                        const searchContext = "Use the following summarized search results to answer the user's query:\n\n" +
                            searchResults.slice(0, 3).map((r, i) =>
                                `Source ${i+1}: ${r.title || 'Untitled'}\nURL: ${r.url}\nSummary:\n${r.content || r.snippet || 'No summary available.'}`
                            ).join('\n\n');
                        conversationManager.addMessage(searchContext, 'tool', 'Search Engine');
                        socket.emit('update_agents_discussion', { agentName: 'System', content: `(Incorporating top ${Math.min(searchResults.length, 3)} search results)`, type: 'system' });
                        console.log("[PreProcessing] Added summarized search results to context.");
                    }
                } catch (searchAgentError: any) {
                    // Fallback to the original method if search agent throws an error
                    console.error("[PreProcessing] Error using search agent:", searchAgentError);

                    // Emit a more detailed error message to the client
                    const errorMessage = searchAgentError.message || 'Unknown error';
                    socket.emit('update_agents_discussion', {
                        agentName: 'System',
                        content: `(Search agent error: ${errorMessage}. Falling back to basic search results.)`,
                        type: 'system'
                    });

                    // If the error is related to API keys, provide more helpful information
                    if (errorMessage.includes('API Key') || errorMessage.includes('api key')) {
                        socket.emit('update_agents_discussion', {
                            agentName: 'System',
                            content: `(Hint: Make sure you have configured an API key for the search agent provider in your settings.)`,
                            type: 'system'
                        });
                    }

                    // Fallback to basic search results
                    const searchContext = "Use the following summarized search results to answer the user's query:\n\n" +
                        searchResults.slice(0, 3).map((r, i) =>
                            `Source ${i+1}: ${r.title || 'Untitled'}\nURL: ${r.url}\nSummary:\n${r.content || r.snippet || 'No summary available.'}`
                        ).join('\n\n');
                    conversationManager.addMessage(searchContext, 'tool', 'Search Engine');
                    socket.emit('update_agents_discussion', { agentName: 'System', content: `(Incorporating top ${Math.min(searchResults.length, 3)} search results)`, type: 'system' });
                    console.log("[PreProcessing] Added summarized search results to context.");
                }
            }
        } catch (searchError: any) {
             if (searchError instanceof SearchError) {
                 console.error(`[PreProcessing Search] SearchError: ${searchError.message} (Code: ${searchError.code})`, searchError.details);
                 socket.emit('search_error', { message: searchError.message, code: searchError.code });
                 conversationManager.addMessage(`Internet Search Error: ${searchError.message}`, 'system', 'Error');
             } else {
                 const message = searchError instanceof Error ? searchError.message : String(searchError);
                 console.error(`[PreProcessing Search] Unexpected error during internet search:`, searchError);
                 socket.emit('processing_error', { message: `Unexpected internet search error: ${message}`, context: 'internet_search' });
                 conversationManager.addMessage(`System Error during Search: ${message}`, 'system', 'Error');
             }
             searchResults = []; // Reset on error
        }
    } else {
        console.log(`[PreProcessing] Internet search disabled.`);
    }

    if (isCancelled()) return []; // Check cancellation between search and RAG

    // RAG Context Retrieval
    if (config.ragSettings?.enabled) {
        if (isCancelled()) { console.log("Orchestration cancelled before RAG retrieval."); return searchResults; } // Return potentially existing search results
        console.log(`[PreProcessing RAG] RAG enabled for user ${userId}. Retrieving relevant chunks...`);
        try {
            // Apply all RAG settings from the configuration
            // Update the RAG handler configuration with the current settings
            await ragHandlerInstance.updateConfiguration({
                embeddingModel: config.ragSettings.embeddingModel,
                chunkSize: config.ragSettings.chunkSize,
                chunkOverlap: config.ragSettings.chunkOverlap,
                chunkingStrategy: config.ragSettings.chunkingStrategy,
                retrievalNResults: config.ragSettings.retrievalNResults,
                retrievalThreshold: config.ragSettings.retrievalThreshold,
                useReranking: config.ragSettings.useReranking,
                useQueryExpansion: config.ragSettings.useQueryExpansion
            });

            // Get the number of results to retrieve
            const nResults = config.ragSettings.retrievalNResults ?? 3;

            // Log the RAG settings being used
            console.log(`[PreProcessing RAG] Using settings: model=${config.ragSettings.embeddingModel}, ` +
                `strategy=${config.ragSettings.chunkingStrategy}, results=${nResults}, ` +
                `reranking=${config.ragSettings.useReranking}, queryExpansion=${config.ragSettings.useQueryExpansion}`);

            // Retrieve relevant chunks
            const relevantChunks = await ragHandlerInstance.getRelevantChunks(initialPrompt, nResults, isCancelled);

            if (isCancelled()) { console.log("Orchestration cancelled after RAG retrieval."); return searchResults; }

            if (relevantChunks?.length > 0) {
                const ragContextString = "=== Knowledge Base Context ===\n" +
                    relevantChunks.map((chunk, i) =>
                        `Chunk ${i+1} (Source: ${chunk.metadata.source}, Similarity: ${chunk.similarity?.toFixed(3) ?? 'N/A'}):\n${chunk.content}`
                    ).join('\n\n---\n\n') +
                    "\n=== End Knowledge Base Context ===";
                conversationManager.addMessage(ragContextString, 'tool', 'Knowledge Base');
                socket.emit('update_agents_discussion', { agentName: 'System', content: `(Incorporating ${relevantChunks.length} relevant chunks from knowledge base)`, type: 'system' });
                console.log(`[PreProcessing RAG] Added ${relevantChunks.length} RAG chunks to context.`);
            } else {
                 console.log("[PreProcessing RAG] No relevant chunks found in knowledge base.");
                 socket.emit('update_agents_discussion', { agentName: 'System', content: `(No relevant information found in knowledge base)`, type: 'system' });
            }
        } catch (ragError: any) {
             if (isCancelled() && !(ragError instanceof RagError && ragError.code === 'CANCELLED')) {
                  console.log("Orchestration cancelled during RAG retrieval await.");
                  conversationManager.addMessage(`(RAG retrieval cancelled by user)`, 'system', 'System');
             } else if (ragError instanceof RagError) {
                 console.error(`[PreProcessing RAG] RagError: ${ragError.message} (Code: ${ragError.code})`, ragError.details);
                 socket.emit('rag_error', { message: ragError.message, code: ragError.code });
                 conversationManager.addMessage(`Knowledge Base Error: ${ragError.message}`, 'system', 'Error');
             } else {
                 const message = ragError instanceof Error ? ragError.message : String(ragError);
                 console.error("[PreProcessing RAG] Unexpected error during RAG retrieval:", ragError);
                 socket.emit('processing_error', { message: `Unexpected knowledge base error: ${message}`, context: 'rag_retrieval' });
                 conversationManager.addMessage(`System Error during RAG: ${message}`, 'system', 'Error');
             }
        }
    } else {
         console.log("[PreProcessing RAG] RAG disabled.");
    }

    return searchResults; // Return search results for potential use in agent prompts
}

/**
 * Executes the agent conversation turns based on the configuration.
 * Handles fetching API keys, formatting messages, calling the LLM service,
 * streaming responses, and updating conversation history.
 *
 * @param config - The application configuration.
 * @param options - Orchestration options (socket, cancellation check).
 * @param userId - The ID of the user.
 * @param providerToServiceNameMap - Mapping of provider names to service names.
 * @param conversationManager - The conversation manager instance.
 * @param searchResults - Results from the internet search pre-processing step.
 * @param imageDataUrl - Optional image data for the first turn.
 * @returns A promise resolving to true if turns completed normally, false if cancelled or an error caused an early exit.
 */
async function _runAgentTurns(
    config: IncomingConfigData,
    options: AgentOrchestrationOptions,
    userId: string,
    providerToServiceNameMap: { [key: string]: string },
    conversationManager: ConversationManager,
    searchResults: SearchResult[], // Pass search results
    imageDataUrl?: string | null // Pass image data for first turn
): Promise<boolean> { // Returns true if turns completed normally, false if cancelled/error break
    const { socket, isCancelled } = options;
    const agentCount = config.agentConfigurations.length;
    const maxRunsPerAgent = (config.maxAgentRuns && config.maxAgentRuns >= 1) ? config.maxAgentRuns : 1;
    const totalTurns = agentCount * maxRunsPerAgent;
    console.log(`[Agent Turns] Starting. Total agents: ${agentCount}, Runs per agent: ${maxRunsPerAgent}, Total turns: ${totalTurns}`);

    for (let currentTurn = 0; currentTurn < totalTurns; currentTurn++) {
        if (isCancelled()) {
            console.log(`[Agent Turns] Orchestration cancelled before turn ${currentTurn + 1}.`);
            return false; // Indicate cancellation
        }

        const agentIndex = currentTurn % agentCount;
        const agentNumber = agentIndex + 1;
        const agentConfig = config.agentConfigurations[agentIndex];
        const agentName = `Agent ${agentNumber} (${agentConfig.provider}/${agentConfig.model})`;
        console.log(`\n--- ${agentName} Turn ${Math.floor(currentTurn / agentCount) + 1} ---`);

        const agentSystemPrompt = getAgentInstructions(
            agentNumber, agentCount, agentConfig,
            config.internetSettings.enabled, config.generalInstructions,
            config.baseInstructions, config.useBaseInstructions
        );
        const contextMessages = conversationManager.getContextMessages();
        const messagesForApi = contextMessages.map(msg => {
            // Create the base message with role and content
            const baseMessage: any = {
                role: msg.role as 'user' | 'assistant' | 'system' | 'tool',
                content: msg.content
            };

            // If the message has an image path, include it in the message
            if (msg.localImagePath) {
                // Add image information to help agents understand there's an image
                baseMessage.content += `\n\n[This message includes an image: ${msg.localImagePath}]`;
                baseMessage.localImagePath = msg.localImagePath;
            }

            return baseMessage;
        });

        // Check if there are any images in the context
        const hasImagesInContext = contextMessages.some(msg => msg.localImagePath);

        // Modify the instruction based on whether there are images in the context
        let textFlowInstruction = "\nIMPORTANT: You are currently in text generation mode. You CANNOT generate or modify images directly. If the user asks for an image or image modification, state that you cannot perform this action.";

        if (hasImagesInContext) {
            textFlowInstruction += "\n\nNOTE: There are one or more images in the conversation history. You can see references to these images in the messages. You can describe, analyze, or comment on these images based on their descriptions, but you cannot modify them directly.";
        }

        let finalSystemPrompt = agentSystemPrompt + textFlowInstruction;

        // Add search synthesis instruction only for the first round of agents if search results exist
        if (searchResults?.length > 0 && currentTurn < agentCount) {
            // Check if we're using the search agent (results will already be synthesized)
            const historyMessages = conversationManager.getFullHistory();
            const hasSearchAgentResults = historyMessages.some(msg => msg.agentName === 'Search Agent');

            if (hasSearchAgentResults) {
                // If search agent already processed results, just add a reminder to use that information
                const searchReminderInstruction = "IMPORTANT: Use the information provided by the Search Agent in the conversation history to answer the user's query. This information has already been synthesized from reliable sources.\n\n---\n\n";
                finalSystemPrompt = searchReminderInstruction + agentSystemPrompt + textFlowInstruction;
                console.log(`[Agent Turns] Added search agent reminder to system prompt for ${agentName}`);
            } else {
                // If using raw search results, add synthesis instruction
                const synthesisInstruction = "IMMEDIATE TASK: Synthesize the information from the 'Internet Search Results' provided in the conversation history to directly answer the user's query. DO NOT mention the search process or that you have results. Focus ONLY on presenting the synthesized answer based on the provided results.\n\n---\n\n";
                finalSystemPrompt = synthesisInstruction + agentSystemPrompt + textFlowInstruction;
                console.log(`[Agent Turns] Prepended search result synthesis instruction to system prompt for ${agentName}`);
            }
        }

        let fullAgentResponse = "";
        let streamError: string | null = null;
        let uniqueTurnKey: string = '';

        try {
             if (isCancelled()) { console.log(`[Agent Turns] Orchestration cancelled before LLM call for ${agentName}.`); return false; }
             uniqueTurnKey = `${agentName}_${Date.now()}`;

            const decryptedApiKey = await _getDecryptedApiKey(userId, providerToServiceNameMap[agentConfig.provider.toLowerCase()]);

            const llmInput: LLMServiceInput = {
                ...agentConfig,
                messages: messagesForApi,
                systemPrompt: finalSystemPrompt,
                apiKey: decryptedApiKey,
                imageDataUrl: currentTurn === 0 ? imageDataUrl : null // Pass image data only on the very first turn
            };

            if (llmInput.imageDataUrl) {
                console.log(`[Agent Turns] Passing image data to LLM for ${agentName} (Turn ${currentTurn + 1})`);
                console.log(`[Agent Turns] Image data URL length: ${llmInput.imageDataUrl.length}`);
                console.log(`[Agent Turns] Image data URL first 100 chars: ${llmInput.imageDataUrl.substring(0, 100)}...`);
                console.log(`[Agent Turns] Provider: ${agentConfig.provider}, Model: ${agentConfig.model}`);

                // Check if the model supports vision
                const supportsVision = ['gpt-4-vision', 'gpt-4-turbo', 'gpt-4o', 'claude-3', 'gemini'].some(visionModel =>
                    agentConfig.model.toLowerCase().includes(visionModel.toLowerCase()));

                console.log(`[Agent Turns] Model ${agentConfig.model} ${supportsVision ? 'SUPPORTS' : 'DOES NOT SUPPORT'} vision capabilities`);

                // If the model doesn't support vision, emit a warning to the client
                if (!supportsVision) {
                    const warningMsg = `Warning: The model ${agentConfig.provider}/${agentConfig.model} does not support vision capabilities. The image will be ignored.`;
                    console.warn(`[Agent Turns] ${warningMsg}`);
                    socket.emit('global_message', { type: 'warning', message: warningMsg });

                    // Remove the image data from the input to avoid errors
                    llmInput.imageDataUrl = null;
                }
            }

             const stream = callLLMApi(llmInput);
             socket.emit('agent_response_start', { agentName, uniqueTurnKey });

             for await (const chunk of stream) {
                  if (isCancelled()) {
                     console.log(`[Agent Turns] Orchestration cancelled during ${agentName} stream.`);
                     streamError = "Processing cancelled by user.";
                     socket.emit('agent_response_chunk', { agentName, uniqueTurnKey, error: streamError });
                     break; // Break inner stream loop
                  }
                 if (chunk.type === 'content') {
                     fullAgentResponse += chunk.content;
                     socket.emit('agent_response_chunk', { agentName, uniqueTurnKey, content: chunk.content });
                 } else if (chunk.type === 'error') {
                     console.error(`[Agent Turns] Error during ${agentName} stream:`, chunk.error);
                     streamError = chunk.error || 'Unknown streaming error';
                     socket.emit('agent_response_chunk', { agentName, uniqueTurnKey, error: streamError });
                     break; // Break inner stream loop
                 } else if (chunk.type === 'end') {
                      if (!isCancelled()) {
                          console.log(`[Agent Turns] ${agentName} stream finished.`);
                          socket.emit('agent_response_end', { agentName, uniqueTurnKey });
                      }
                 }
            } // End stream loop

             if (streamError === "Processing cancelled by user.") {
                  conversationManager.addMessage(`(Processing cancelled by user during ${agentName}'s turn)`, 'system', 'System');
                  socket.emit('agent_response_end', { agentName, uniqueTurnKey });
                  return false; // Indicate cancellation break
             } else if (streamError) {
                  conversationManager.addMessage(`Stream Error: ${streamError}`, 'system', 'Error');
                  socket.emit('agent_response_end', { agentName, uniqueTurnKey });
                  // Decide whether to continue or break on stream error
                  // break; // Option: Stop entire orchestration on first agent stream error
             } else if (fullAgentResponse) {
                  conversationManager.addMessage(fullAgentResponse, 'assistant', agentName);
                  console.log(`[Agent Turns] ${agentName} Full Response: ${fullAgentResponse.substring(0, 100)}...`);
             } else {
                  console.warn(`[Agent Turns] ${agentName} produced an empty response.`);
                  conversationManager.addMessage("(Agent produced no response)", 'system', agentName);
                  socket.emit('agent_response_chunk', { agentName, uniqueTurnKey, content: "(Agent produced no response)" });
                  socket.emit('agent_response_end', { agentName, uniqueTurnKey });
            }
         } catch (error: any) {
             if (isCancelled()) {
                  console.log(`[Agent Turns] Orchestration cancelled for ${agentName} (caught error likely due to cancellation).`);
                  socket.emit('agent_response_end', { agentName, uniqueTurnKey });
                  return false; // Indicate cancellation break
             } else if (error instanceof LlmError) {
                 console.error(`[Agent Turns] LlmError for ${agentName}: ${error.message} (Code: ${error.code})`, error.details);
                 const errorMessage = `Error for ${agentName}: ${error.message}`;
                 socket.emit('llm_error', { message: error.message, code: error.code, agentName: agentName, context: `agent_turn_${agentName}` });
                 conversationManager.addMessage(errorMessage, 'system', 'Error');
                 socket.emit('agent_response_chunk', { agentName, uniqueTurnKey, error: errorMessage });
                 socket.emit('agent_response_end', { agentName, uniqueTurnKey });
                 // break; // Option: Stop entire orchestration on first agent error
             } else {
                 const message = error instanceof Error ? error.message : String(error);
                 console.error(`[Agent Turns] Unexpected error during ${agentName} turn setup:`, error);
                 const errorMessage = `Unexpected Error for ${agentName}: ${message}`;
                 socket.emit('processing_error', { message: errorMessage, context: `agent_turn_${agentName}` });
                 conversationManager.addMessage(errorMessage, 'system', 'Error');
                 socket.emit('agent_response_chunk', { agentName, uniqueTurnKey, error: errorMessage });
                 socket.emit('agent_response_end', { agentName, uniqueTurnKey });
                 // break; // Option: Stop entire orchestration on unexpected error
            }
        } // End Agent Turn Try-Catch
    } // End Agent Turn Loop

    // If loop completes without being cancelled
    return true;
}
/**
 * Saves the current conversation history to the database.
 * Creates a new conversation document if it's a new conversation,
 * otherwise updates the existing one.
 *
 * @param userId - The ID of the user owning the conversation.
 * @param conversationId - The ID of the conversation to update (undefined for new).
 * @param isNew - Flag indicating if this is a new conversation.
 * @param conversationManager - The ConversationManager instance holding the history.
 * @param config - The configuration snapshot to save with the conversation.
 * @param socket - The Socket.IO socket instance for emitting events.
 * @param cancelled - Optional flag indicating if the orchestration was cancelled (to skip saving).
 */
async function saveConversationHistory(
    userId: string,
    conversationId: string | undefined,
    isNew: boolean,
    conversationManager: ConversationManager,
    config: IncomingConfigData,
    socket: Socket,
    cancelled: boolean = false
) {
    if (cancelled) {
        console.log("[Save Helper] Skipping DB save due to cancellation flag.");
        return;
    }

    const historyToSave = conversationManager.getFullHistory();

    if (historyToSave.length === 0) {
         console.log("[Save Helper] Skipping DB save for empty history.");
         return;
    }

    try {
        if (isNew) {
            const firstMessageContent = historyToSave[0]?.content || 'Untitled';
            const title = firstMessageContent.substring(0, 50) + (firstMessageContent.length > 50 ? '...' : '');

            const newConversation = new Conversation({
                userId: userId,
                title: title,
                messages: historyToSave,
                metadata: { configSnapshot: config }
            });
            console.log(`[Save Helper] Attempting to save new conversation for userId: ${userId}`);
            const savedConv = await newConversation.save();
            const newConvId = savedConv.id;
            console.log(`[Save Helper] New conversation saved with ID: ${newConvId}`);
            socket.emit('conversation_started', { conversationId: newConvId });
        } else if (conversationId) {
            const conversationToUpdate = await Conversation.findOne({ _id: conversationId, userId: userId });
            if (conversationToUpdate) {
                conversationToUpdate.messages = historyToSave;
                conversationToUpdate.metadata = { configSnapshot: config };
                console.log(`[Save Helper] Attempting to update conversation ${conversationId} for userId: ${userId}`);
                 await conversationToUpdate.save();
                 console.log(`[Save Helper] Conversation ${conversationId} updated for user ${userId}.`);
             } else {
                  console.error(`[Save Helper] Update failed: Conversation ${conversationId} not found for user ${userId}.`);
             }
         }
     } catch(dbError: any) {
          console.error(`[Save Helper] Error saving conversation for user ${userId}:`, dbError);
          socket.emit('processing_error', { message: `Failed to save conversation: ${dbError.message}` });
     }
}
