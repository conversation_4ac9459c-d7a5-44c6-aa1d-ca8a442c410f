import React, { useState } from 'react';
import AgentSettingsTab from './AgentSettingsTab';
import RagSettingsTab from './RagSettingsTab';
import SearchSettingsTab from './SearchSettingsTab';
import SystemSettingsTab from './SystemSettingsTab';
import ApiKeysSettingsTab from './ApiKeysSettingsTab'; // Remove .tsx extension
import Modal from './Modal'; // Import the Modal component
import ExampleProfilesModal from './ExampleProfilesModal'; // Import the ExampleProfilesModal component
// FeedbackDisplay will be moved to MainAppLayout
import { useConfigStore } from '../store/configStore'; // Import Zustand store
import {
  // saveConfigurationProfile, // No longer needed directly here
  getConfigurationProfiles,
  getConfigurationProfileById,
  deleteConfigurationProfile,
  ProfileListItem
} from '../services/configApiService'; // Import API service functions

const TABS = ["Agents", "RAG", "Search", "System", "API Keys"]; // Removed "Internet" as it's now part of "Search"

// Define props interface
interface ConfigurationTabsProps {
  providers: string[];
  providersLoading: boolean;
  providersError: string | null;
}

// Define FormControl locally as it's simple and only used here currently
const FormControl: React.FC<{ label: string; htmlFor: string; children: React.ReactNode; className?: string }> = ({ label, htmlFor, children, className = '' }) => (
    <div className={`space-y-1 ${className}`}>
      <label htmlFor={htmlFor} className="block text-sm font-medium text-gray-700 dark:text-gray-300">
        {label}
      </label>
      {children}
    </div>
);


const ConfigurationTabs: React.FC<ConfigurationTabsProps> = ({
  providers,
  providersLoading,
  providersError
}) => {
  const [activeTab, setActiveTab] = useState(0); // Index of the active tab
  // Get actions and state from store
  const {
    loadSettings,
    setCurrentProfile,
    saveCurrentProfile,
    currentProfileName,
    currentProfileId,
    // Global feedback state/actions
    setGlobalError,
    setGlobalSuccess,
    clearGlobalMessages,
    isGlobalLoading // Use global loading for save/delete
  } = useConfigStore((state) => ({
    loadSettings: state.loadSettings,
    setCurrentProfile: state.setCurrentProfile,
    saveCurrentProfile: state.saveCurrentProfile,
    currentProfileName: state.currentProfileName,
    currentProfileId: state.currentProfileId,
    setGlobalError: state.setGlobalError,
    setGlobalSuccess: state.setGlobalSuccess,
    clearGlobalMessages: state.clearGlobalMessages,
    isGlobalLoading: state.isGlobalLoading,
  }));

  // State for Save/Load UI
  const [profiles, setProfiles] = useState<ProfileListItem[]>([]);
  const [isListLoading, setIsListLoading] = useState(false); // Keep local loading for list operations
  const [showLoadList, setShowLoadList] = useState(false); // Toggle for load list display
  const [isSaveModalOpen, setIsSaveModalOpen] = useState(false); // State for save modal
  const [saveProfileName, setSaveProfileName] = useState(''); // State for profile name input
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false); // State for delete modal
  const [profileToDelete, setProfileToDelete] = useState<{ id: string; name: string } | null>(null); // State for profile to delete
  const [isExampleModalOpen, setIsExampleModalOpen] = useState(false); // State for example profiles modal

  // --- Handlers ---

  // Opens the save modal
  const handleOpenSaveModal = () => {
    setSaveProfileName(currentProfileName || ''); // Pre-fill with current name or empty
    setIsSaveModalOpen(true);
    clearGlobalMessages(); // Clear global messages when opening modal
  };

  // Handles the actual save operation from the modal
  const handleConfirmSave = async () => {
    if (!saveProfileName.trim()) {
      // alert("Profile name cannot be empty."); // Replaced alert
      setGlobalError("Profile name cannot be empty."); // Use global error state
      return;
    }

    const isSaveAs = saveProfileName !== currentProfileName;
    // Loading/Error/Success state is now handled within saveCurrentProfile action

    try {
      // Call the store action, passing the name only if it's a "Save As" or new profile operation
      await saveCurrentProfile(isSaveAs || !currentProfileId ? saveProfileName : undefined);
      // Check if successful (no error thrown) before closing modal
      // The store action now sets globalSuccess/globalError
      if (!useConfigStore.getState().globalError) {
          setIsSaveModalOpen(false); // Close modal on success
      }
    } catch (err: any) {
      // Error is handled globally by the store action, just log here if needed
      console.error("Save profile dispatch failed:", err);
      // Keep modal open on error
    }
    // Loading state is handled globally by the store action
  };

  const handleLoadClick = async () => {
    setIsListLoading(true); // Use local loading for this list fetch
    clearGlobalMessages(); // Clear global messages
    setShowLoadList(false); // Hide list initially
    try {
      const fetchedProfiles = await getConfigurationProfiles();
      setProfiles(fetchedProfiles);
      if (fetchedProfiles.length > 0) {
        setShowLoadList(true); // Show list if profiles exist
      } else {
        setGlobalSuccess("No saved profiles found."); // Use global success
      }
    } catch (err: any) {
      console.error("Fetch profiles failed:", err);
      setGlobalError(err.response?.data?.message || err.message || 'Failed to fetch profiles.'); // Use global error
    } finally {
      setIsListLoading(false); // Clear local loading
    }
  };

  const handleSelectProfileToLoad = async (profileId: string) => {
     setIsListLoading(true); // Use local loading
     clearGlobalMessages();
     setShowLoadList(false); // Hide list after selection
     try {
        const profileData = await getConfigurationProfileById(profileId);
        loadSettings(profileData.settings); // Load settings into Zustand store
        setCurrentProfile(profileData._id, profileData.name); // Set the current profile context
        setGlobalSuccess(`Profile '${profileData.name}' loaded successfully!`); // Use global success
     } catch (err: any) {
        console.error("Load profile failed:", err);
        setGlobalError(err.response?.data?.message || err.message || 'Failed to load profile.'); // Use global error
     } finally {
        setIsListLoading(false); // Clear local loading
     }
  };

  // Opens the delete confirmation modal
  const handleOpenDeleteModal = (profile: ProfileListItem) => {
      setProfileToDelete({ id: profile._id, name: profile.name });
      setIsDeleteModalOpen(true);
      clearGlobalMessages(); // Clear global messages
  };

  // Handles the actual delete operation
  const handleConfirmDelete = async () => {
    if (!profileToDelete) return;

    // Use global loading state for delete operation
    useConfigStore.getState().setGlobalLoading(true);
    clearGlobalMessages();

    try {
      await deleteConfigurationProfile(profileToDelete.id);
      setGlobalSuccess(`Profile '${profileToDelete.name}' deleted.`); // Use global success
      // Refresh the list after delete
      const fetchedProfiles = await getConfigurationProfiles(); // Keep using local state for list display
      setProfiles(fetchedProfiles);
      if (fetchedProfiles.length === 0) setShowLoadList(false); // Hide list if empty
      // Also clear current profile if the deleted one was loaded
      if (currentProfileId === profileToDelete.id) {
          setCurrentProfile(null, null);
      }
      setIsDeleteModalOpen(false); // Close modal on success
      setProfileToDelete(null); // Reset profile to delete
    } catch (err: any) {
      console.error("Delete failed:", err);
      setGlobalError(err.response?.data?.message || err.message || 'Failed to delete profile.'); // Use global error
      // Keep modal open on error? Or close? Let's close it.
      setIsDeleteModalOpen(false);
      setProfileToDelete(null);
    } finally {
      useConfigStore.getState().setGlobalLoading(false); // Clear global loading
    }
  };


  return (
    <div className="h-full flex flex-col"> {/* Changed to flex column */}
      {/* Tab Headers */}
      <div className="flex border-b border-gray-200 dark:border-gray-700 mb-4 flex-shrink-0">
        {TABS.map((tabName, index) => (
          <button
            key={tabName}
            onClick={() => setActiveTab(index)}
            className={`py-2 px-4 text-sm font-medium focus:outline-none ${
              activeTab === index
                ? 'border-b-2 border-primary text-primary dark:text-primary-light dark:border-primary-light'
                : 'text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300 hover:border-gray-300 dark:hover:border-gray-600'
            }`}
          >
            {tabName}
          </button>
        ))}
      </div>

      {/* Tab Content Area */}
      <div className="flex-grow p-4 bg-surface-light dark:bg-surface-dark rounded-lg overflow-y-auto">
        {/* Conditionally render the active tab's component, passing props */}
        {activeTab === 0 && (
          <AgentSettingsTab
            providers={providers}
            providersLoading={providersLoading}
            providersError={providersError}
          />
        )}
        {activeTab === 1 && <RagSettingsTab />}
        {activeTab === 2 && <SearchSettingsTab />}
        {activeTab === 3 && <SystemSettingsTab />}
        {activeTab === 4 && <ApiKeysSettingsTab />}
      </div>

      {/* Load Profile List Display (Simple Version) */}
      {showLoadList && (
        <div className="mt-4 p-3 border border-gray-300 dark:border-gray-600 rounded-md bg-gray-50 dark:bg-gray-700 flex-shrink-0">
          <h4 className="text-sm font-medium mb-2 text-gray-800 dark:text-gray-200">Select Profile to Load:</h4>
          <ul className="max-h-32 overflow-y-auto text-sm space-y-1">
            {profiles.map(profile => (
              <li key={profile._id} className="flex items-center justify-between"> {/* Added flex for button alignment */}
                <button
                  onClick={() => handleSelectProfileToLoad(profile._id)}
                  disabled={isListLoading || isGlobalLoading} // Disable if list or global action is loading
                  className="text-left flex-grow px-2 py-1 rounded hover:bg-primary-light hover:text-white dark:hover:bg-primary-dark disabled:opacity-50 truncate" // Added truncate
                >
                  {profile.name} <span className="text-xs text-gray-400 dark:text-gray-500">({new Date(profile.updatedAt).toLocaleDateString()})</span>
                </button>
                {/* Delete Button */}
                <button
                  type="button" // Added type="button"
                  onClick={() => handleOpenDeleteModal(profile)}
                  disabled={isListLoading || isGlobalLoading} // Disable if list or global action is loading
                  className="ml-2 px-2 py-0.5 text-xs bg-red-100 text-red-700 rounded hover:bg-red-200 dark:bg-red-900/50 dark:text-red-300 dark:hover:bg-red-800/50 disabled:opacity-50 flex-shrink-0"
                  title={`Delete profile "${profile.name}"`}
                >
                  Delete
                </button>
              </li>
            ))}
          </ul>
           <button
             onClick={() => setShowLoadList(false)}
             className="mt-2 text-xs text-gray-500 dark:text-gray-400 hover:underline"
           >
             Cancel
           </button>
        </div>
      )}

      {/* Global Feedback Display Area (Removed - Moved to MainAppLayout) */}
      {/* <div className="mt-1 flex-shrink-0">
          <FeedbackDisplay />
      </div> */}

      {/* Save/Load Buttons & Current Profile Display */}
      <div className="mt-1 pt-4 border-t border-gray-200 dark:border-gray-700 flex justify-between items-center flex-shrink-0">
        {/* Display Current Profile Name */}
        <div className="text-xs text-gray-500 dark:text-gray-400 truncate pr-2">
          {currentProfileName ? (
            <span>Current Profile: <span className="font-medium">{currentProfileName}</span></span>
          ) : (
            <span className="italic">No profile loaded.</span>
          )}
        </div>
        {/* Buttons */}
        <div className="flex space-x-2">
           <button
             className="px-4 py-2 bg-gray-200 dark:bg-gray-600 text-gray-700 dark:text-gray-200 rounded hover:bg-gray-300 dark:hover:bg-gray-500 transition-colors text-sm disabled:opacity-50"
             onClick={handleLoadClick}
             disabled={isListLoading || isGlobalLoading} // Use local loading for list fetch
           >
             Load Config
           </button>
           <button
             className="px-4 py-2 bg-gray-200 dark:bg-gray-600 text-gray-700 dark:text-gray-200 rounded hover:bg-gray-300 dark:hover:bg-gray-500 transition-colors text-sm disabled:opacity-50"
             onClick={() => setIsExampleModalOpen(true)}
             disabled={isGlobalLoading} // Disable based on global loading
           >
             Example Instructions
           </button>
           <button
             className="px-4 py-2 bg-primary text-white rounded hover:bg-primary-dark transition-colors text-sm disabled:opacity-50"
             onClick={handleOpenSaveModal} // Changed to open modal
             disabled={isGlobalLoading} // Disable based on global loading
           >
             {/* Change button text based on whether a profile is loaded */}
             {currentProfileId ? 'Save / Save As...' : 'Save As...'}
           </button>
        </div>
      </div>

       {/* Save Profile Modal */}
       <Modal
         isOpen={isSaveModalOpen}
         onClose={() => setIsSaveModalOpen(false)}
         title={currentProfileId ? "Save/Update Profile" : "Save New Profile"} // Use selected currentProfileId
         footer={
           <>
             <button
               type="button" // Added type
               onClick={() => setIsSaveModalOpen(false)}
               className="px-4 py-2 bg-gray-200 dark:bg-gray-600 text-gray-700 dark:text-gray-200 rounded hover:bg-gray-300 dark:hover:bg-gray-500 transition-colors text-sm"
             >
               Cancel
             </button>
             <button
               type="button" // Added type
               onClick={handleConfirmSave}
               disabled={isGlobalLoading || !saveProfileName.trim()} // Disable if global loading or name is empty
               className="px-4 py-2 bg-primary text-white rounded hover:bg-primary-dark transition-colors text-sm disabled:opacity-50"
             >
               {/* Use selected currentProfileId */}
               {isGlobalLoading ? 'Saving...' : (currentProfileId && saveProfileName === currentProfileName) ? 'Update' : 'Save'}
             </button>
           </>
         }
       >
         {/* Modal Body */}
         <FormControl label="Profile Name:" htmlFor="saveProfileNameInput"> {/* Use defined FormControl */}
           <input
             type="text"
             id="saveProfileNameInput"
             value={saveProfileName}
             onChange={(e) => setSaveProfileName(e.target.value)}
             className="mt-1 block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
             placeholder="Enter profile name..."
           />
         </FormControl>
         {/* Display save errors directly in the modal? */}
         {/* {error && <p className="text-xs text-red-500 mt-2">{error}</p>} */}
       </Modal>

       {/* Delete Confirmation Modal */}
       <Modal
         isOpen={isDeleteModalOpen}
         onClose={() => setIsDeleteModalOpen(false)}
         title="Confirm Deletion"
         footer={
           <>
             <button
               type="button" // Added type
               onClick={() => setIsDeleteModalOpen(false)}
               disabled={isGlobalLoading} // Disable based on global loading
               className="px-4 py-2 bg-gray-200 dark:bg-gray-600 text-gray-700 dark:text-gray-200 rounded hover:bg-gray-300 dark:hover:bg-gray-500 transition-colors text-sm disabled:opacity-50"
             >
               Cancel
             </button>
             <button
               type="button" // Added type
               onClick={handleConfirmDelete}
               disabled={isGlobalLoading} // Disable based on global loading
               className="px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700 transition-colors text-sm disabled:opacity-50"
             >
               {isGlobalLoading ? 'Deleting...' : 'Delete'}
             </button>
           </>
         }
       >
         <p className="text-sm text-gray-700 dark:text-gray-300">
           Are you sure you want to delete the profile "{profileToDelete?.name}"? This action cannot be undone.
         </p>
       </Modal>

       {/* Example Profiles Modal */}
       <ExampleProfilesModal
         isOpen={isExampleModalOpen}
         onClose={() => setIsExampleModalOpen(false)}
       />

    </div>
  );
};

export default ConfigurationTabs; // Ensure default export exists
