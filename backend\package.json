{"name": "maia-web-backend", "version": "0.1.0", "description": "Backend server for MAIA-Web multi-agent AI assistant", "main": "dist/server.js", "scripts": {"start": "node dist/server.js", "dev": "nodemon --exec ts-node src/server.ts", "build": "tsc", "lint": "eslint . --ext .ts", "test": "jest"}, "dependencies": {"@anthropic-ai/sdk": "^0.39.0", "@google/generative-ai": "^0.24.0", "@mozilla/readability": "^0.6.0", "@types/cheerio": "^0.22.35", "@types/jsdom": "^21.1.7", "@types/mozilla__readability": "^0.4.2", "@types/multer": "^1.4.12", "@types/passport": "^1.0.17", "@types/passport-google-oauth20": "^2.0.16", "@xenova/transformers": "^2.17.2", "axios": "^1.8.4", "bcrypt": "^5.1.1", "better-sqlite3": "^11.9.1", "cheerio": "^1.0.0", "cors": "^2.8.5", "csv-parse": "^5.6.0", "dotenv": "^16.3.1", "express": "^4.18.2", "faiss-node": "^0.5.1", "form-data": "^4.0.2", "googleapis": "^148.0.0", "groq-sdk": "^0.17.0", "jsdom": "^26.0.0", "jsonwebtoken": "^9.0.2", "mammoth": "^1.9.0", "mongoose": "^8.0.1", "msgpack-lite": "^0.1.26", "multer": "^1.4.5-lts.2", "node-html-parser": "^7.0.1", "openai": "^4.91.0", "passport": "^0.7.0", "passport-google-oauth20": "^2.0.0", "pdf-parse": "^1.1.1", "react-icons": "^5.5.0", "socket.io": "^4.7.2", "xlsx": "^0.18.5"}, "devDependencies": {"@types/bcrypt": "^5.0.2", "@types/bcryptjs": "^2.4.6", "@types/better-sqlite3": "^7.6.13", "@types/cors": "^2.8.16", "@types/express": "^4.17.21", "@types/jsonwebtoken": "^9.0.9", "@types/msgpack-lite": "^0.1.11", "@types/node": "^20.9.1", "@types/pdf-parse": "^1.1.4", "@types/uuid": "^10.0.0", "eslint": "^8.53.0", "nodemon": "^3.0.1", "ts-node": "^10.9.1", "typescript": "^5.2.2"}}