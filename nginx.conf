server {
    listen 80;
    server_name localhost; # Adjust if needed for your domain

    # Root directory for static files
    root /usr/share/nginx/html;
    index index.html index.htm;

    location / {
        # Try to serve file directly, fallback to index.html
        try_files $uri $uri/ /index.html;
    }

    # Optional: Add configuration for handling API requests via proxy
    # location /api/ {
    #     proxy_pass http://backend:3000/; # Assuming 'backend' is the service name in docker-compose
    #     proxy_http_version 1.1;
    #     proxy_set_header Upgrade $http_upgrade;
    #     proxy_set_header Connection 'upgrade';
    #     proxy_set_header Host $host;
    #     proxy_cache_bypass $http_upgrade;
    # }

    # Optional: Add configuration for handling WebSocket connections via proxy
    # location /socket.io/ {
    #     proxy_pass http://backend:3000/socket.io/; # Assuming 'backend' is the service name
    #     proxy_http_version 1.1;
    #     proxy_set_header Upgrade $http_upgrade;
    #     proxy_set_header Connection "upgrade";
    #     proxy_set_header Host $host;
    # }

    # Optional: Add error pages
    error_page 500 502 503 504 /50x.html;
    location = /50x.html {
        root /usr/share/nginx/html;
    }
}
