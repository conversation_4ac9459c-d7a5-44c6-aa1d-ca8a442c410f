import { Request, Response } from 'express';
import axios from 'axios';

// Interfaces for expected API responses (can be refined)
interface OllamaModel {
    name: string;
    // Add other properties if needed (modified_at, size, etc.)
}

interface LmStudioModel {
    id: string; // Assuming LM Studio uses 'id'
    // Add other properties if needed
}

// Helper function to get Ollama models
const getOllamaModels = async (): Promise<string[]> => {
    // Use IPv4 address explicitly to avoid IPv6 connection issues
    const ollamaBaseUrl = process.env.OLLAMA_BASE_URL || 'http://127.0.0.1:11434';
    // Get the timeout setting from environment variable or use default (10 seconds for model listing)
    const ollamaTimeout = parseInt(process.env.OLLAMA_REQUEST_TIMEOUT || '60000', 10);
    // Use a shorter timeout for model listing than for model loading
    const modelListTimeout = Math.min(ollamaTimeout, 10000);

    try {
        console.log(`[Ollama] Attempting to fetch models from ${ollamaBaseUrl}/api/tags with timeout ${modelListTimeout}ms`);

        const response = await axios.get<{ models: OllamaModel[] }>(`${ollamaBaseUrl}/api/tags`, {
            timeout: modelListTimeout
        });

        // Log the response for debugging
        console.log(`[Ollama] Models API response:`, JSON.stringify(response.data, null, 2));

        if (response.data?.models && Array.isArray(response.data.models)) {
            // Extract model names (format: "name:tag")
            const models = response.data.models.map((model: OllamaModel) => model.name);
            console.log(`[Ollama] Found ${models.length} models:`, models);
            return models;
        }

        console.warn(`[Ollama] Could not parse models from ${ollamaBaseUrl}/api/tags. Response format might differ.`);
        console.warn(`[Ollama] Response data:`, response.data);
        return [];
    } catch (error: any) {
        console.error(`[Ollama] Error fetching models from ${ollamaBaseUrl}:`, error.message);
        // Return empty list if server is down or endpoint doesn't exist
        return [];
    }
};

// Helper function to get LM Studio models
const getLmStudioModels = async (): Promise<string[]> => {
    // Use IPv4 address explicitly to avoid IPv6 connection issues
    const lmStudioBaseUrl = process.env.LMSTUDIO_BASE_URL || 'http://127.0.0.1:1234/v1';
    // Get the timeout setting from environment variable or use default (10 seconds for model listing)
    const lmStudioTimeout = parseInt(process.env.LMSTUDIO_REQUEST_TIMEOUT || '30000', 10);
    // Use a shorter timeout for model listing than for model loading
    const modelListTimeout = Math.min(lmStudioTimeout, 10000);

    try {
        console.log(`[LM Studio] Attempting to fetch models from ${lmStudioBaseUrl}/models with timeout ${modelListTimeout}ms`);

        // LM Studio follows OpenAI API format
        const response = await axios.get(`${lmStudioBaseUrl}/models`, {
            timeout: modelListTimeout
        });

        // Log the response for debugging
        console.log(`[LM Studio] Models API response:`, JSON.stringify(response.data, null, 2));

        // Handle the OpenAI-compatible format where models are in a 'data' array
        if (response.data?.data && Array.isArray(response.data.data)) {
            const models = response.data.data.map((model: LmStudioModel) => model.id);
            console.log(`[LM Studio] Found ${models.length} models:`, models);
            return models;
        }

        console.warn(`[LM Studio] Could not parse models from ${lmStudioBaseUrl}/models. Response format might differ.`);
        console.warn(`[LM Studio] Response data:`, response.data);
        return [];
    } catch (error: any) {
        console.error(`[LM Studio] Error fetching models from ${lmStudioBaseUrl}:`, error.message);
        // Return empty list if server is down or endpoint doesn't exist
        return [];
    }
};


// Controller function to get models for local providers
export const getLocalModels = async (req: Request, res: Response): Promise<void> => {
    const provider = req.params.provider?.toLowerCase();

    try {
        let modelStrings: string[] = [];
        if (provider === 'ollama') {
            modelStrings = await getOllamaModels();
        } else if (provider === 'lm studio') {
            modelStrings = await getLmStudioModels();
        } else {
            res.status(400).json({ message: `Unsupported local provider: ${req.params.provider}` });
            return;
        }

        // Convert string arrays to ModelData objects
        const models: ModelData[] = modelStrings.map(modelId => ({
            id: modelId,
            name: modelId
        }));

        console.log(`Fetched ${models.length} models for local provider: ${provider}`);
        res.json(models);

    } catch (error: any) {
        console.error(`Error getting local models for provider ${provider}:`, error);
        res.status(500).json({ message: 'Failed to fetch local models', error: error.message });
    }
};

// Centralized provider listing
export const getProviders = async (_req: Request, res: Response): Promise<void> => {
     // Add DeepSeek to the list
     res.json(['OpenAI', 'Anthropic', 'Google', 'Groq', 'Ollama', 'LM Studio', 'DeepSeek', 'OpenRouter']);
};

// Get API key for a specific service
export const getApiKey = async (req: Request & { user?: { id: string } }, res: Response): Promise<void> => {
    if (!req.user?.id) {
        res.status(401).json({ message: 'User not authenticated' });
        return;
    }

    const { serviceName } = req.params;
    if (!serviceName) {
        res.status(400).json({ message: 'Service name is required' });
        return;
    }

    try {
        // Import the necessary modules
        const ApiKey = require('../models/ApiKey').default;
        const { decrypt } = require('../utils/cryptoUtils');

        // Find the API key for the user and service
        const apiKeyRecord = await ApiKey.findOne({ userId: req.user.id, serviceName });
        if (!apiKeyRecord?.apiKey) {
            res.status(404).json({ message: `API key for service '${serviceName}' not found` });
            return;
        }

        // Decrypt the API key
        const decryptedKey = decrypt(apiKeyRecord.apiKey);
        if (!decryptedKey) {
            res.status(500).json({ message: 'Failed to decrypt API key' });
            return;
        }

        // Return the decrypted key
        res.status(200).json({ key: decryptedKey });
    } catch (error: any) {
        console.error(`Error retrieving API key for service '${serviceName}':`, error);
        res.status(500).json({ message: 'Failed to retrieve API key', error: error.message });
    }
};

// Interface for model data
interface ModelData {
    id: string;
    name: string;
    contextWindow?: number;
    maxOutputTokens?: number;
}

// Centralized model listing for non-local providers
export const getModelsForProvider = async (req: Request, res: Response): Promise<void> => {
    const provider = req.params.provider?.toLowerCase();

    if (provider === 'openrouter') {
        try {
            // Get the API key from the request headers or from the user's stored API keys
            let apiKey = req.headers['authorization']?.replace(/^Bearer\s+/i, '');

            // If no API key in headers, try to get it from the user's stored API keys
            if (!apiKey && req.user?.id) {
                try {
                    // Import the necessary modules
                    const ApiKey = require('../models/ApiKey').default;
                    const { decrypt } = require('../utils/cryptoUtils');

                    // Find the API key for the user and service
                    const apiKeyRecord = await ApiKey.findOne({ userId: req.user.id, serviceName: 'OPENROUTER_API_KEY' });
                    if (apiKeyRecord?.apiKey) {
                        // Decrypt the API key
                        apiKey = decrypt(apiKeyRecord.apiKey);
                        console.log('Successfully retrieved OpenRouter API key from database');
                    }
                } catch (keyError) {
                    console.error('Error retrieving OpenRouter API key from database:', keyError);
                }
            }

            if (!apiKey) {
                res.status(400).json({ error: 'Missing OpenRouter API key. Please add your API key in the API Keys tab.' });
                return;
            }

            console.log('Fetching models from OpenRouter API...');
            const response = await axios.get('https://openrouter.ai/api/v1/models', {
                headers: {
                    'Authorization': `Bearer ${apiKey}`,
                    'Content-Type': 'application/json',
                    'HTTP-Referer': 'https://maiachat.app', // Updated domain
                    'X-Title': 'MAIAChat' // Updated app name
                }
            });

            // OpenRouter returns { data: [ { id, name, ... }, ... ] }
            const models = (response.data.data || []).map((m: any) => ({
                id: m.id,
                name: m.name || m.id
            }));
            console.log(`Successfully fetched ${models.length} models from OpenRouter`);
            res.json(models);
            return;
        } catch (err: any) {
            console.error('[OpenRouter] Failed to fetch models:', err?.response?.data || err.message);
            res.status(500).json({ error: 'Failed to fetch models from OpenRouter.' });
            return;
        }
    }

    // Hardcoded examples - can be expanded or made dynamic later
    // Note: Ollama and LM Studio are handled by getLocalModels
    const modelStrings: Record<string, string[]> = {
        openai: ['gpt-4o', 'gpt-mini-2025-01-31'],
        anthropic: ['claude-3-7-sonnet-20250219'],
        google: ['gemini-2.5-pro-exp-03-25', 'gemini-2.0-flash-exp-image-generation','gemini-2.0-flash'],
        groq: ['llama3-8b-8192', 'llama3-70b-8192', 'mixtral-8x7b-32768', 'gemma-7b-it'],
        deepseek: ['deepseek-chat'], // Add DeepSeek models
        // Ollama and LM Studio models are fetched dynamically via /local-models endpoint
    };

    // Convert string arrays to ModelData objects
    const modelStringsForProvider = modelStrings[provider || ''] || [];
    const models: ModelData[] = modelStringsForProvider.map(modelId => ({
        id: modelId,
        name: modelId
    }));

    res.json(models); // Return models or empty array
};
