// Script to reindex files in the knowledge base
const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// Function to reindex the knowledge base by copying files to a temp directory and back
async function reindexFiles() {
  try {
    console.log('Starting reindexing process...');
    
    // Create a temporary directory
    const tempDir = path.join(__dirname, 'temp_reindex');
    if (!fs.existsSync(tempDir)) {
      fs.mkdirSync(tempDir);
    }
    
    // Find the PDF file in the uploads directory
    const uploadsDir = path.join(__dirname, 'uploads', 'rag_temp');
    if (!fs.existsSync(uploadsDir)) {
      console.log('Uploads directory does not exist:', uploadsDir);
      return;
    }
    
    // List all files in the uploads directory
    const files = fs.readdirSync(uploadsDir);
    const pdfFiles = files.filter(file => file.endsWith('.pdf'));
    
    if (pdfFiles.length === 0) {
      console.log('No PDF files found in uploads directory');
      return;
    }
    
    console.log(`Found ${pdfFiles.length} PDF files:`, pdfFiles);
    
    // Copy each PDF file to the temp directory
    for (const pdfFile of pdfFiles) {
      const sourcePath = path.join(uploadsDir, pdfFile);
      const tempPath = path.join(tempDir, pdfFile);
      
      fs.copyFileSync(sourcePath, tempPath);
      console.log(`Copied ${pdfFile} to temp directory`);
    }
    
    // Delete the original files from the uploads directory
    for (const pdfFile of pdfFiles) {
      const sourcePath = path.join(uploadsDir, pdfFile);
      fs.unlinkSync(sourcePath);
      console.log(`Deleted ${pdfFile} from uploads directory`);
    }
    
    // Copy the files back to the uploads directory
    for (const pdfFile of pdfFiles) {
      const tempPath = path.join(tempDir, pdfFile);
      const destPath = path.join(uploadsDir, pdfFile);
      
      fs.copyFileSync(tempPath, destPath);
      console.log(`Copied ${pdfFile} back to uploads directory`);
    }
    
    // Clean up the temp directory
    for (const pdfFile of pdfFiles) {
      const tempPath = path.join(tempDir, pdfFile);
      fs.unlinkSync(tempPath);
    }
    fs.rmdirSync(tempDir);
    
    console.log('Reindexing process completed successfully!');
    console.log('The server should automatically detect and process the files.');
  } catch (error) {
    console.error('Error during reindexing:', error);
  }
}

reindexFiles();
