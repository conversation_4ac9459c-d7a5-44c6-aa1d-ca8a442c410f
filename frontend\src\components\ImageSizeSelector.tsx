import React from 'react';

interface ImageSizeSelectorProps {
  onSizeSelect: (size: string) => void;
  selectedSize: string;
}

const ImageSizeSelector: React.FC<ImageSizeSelectorProps> = ({ onSizeSelect, selectedSize }) => {
  const sizes = [
    { value: '256x256', label: 'Small (256x256)' },
    { value: '512x512', label: 'Medium (512x512)' },
    { value: '1024x1024', label: 'Large (1024x1024)' },
    { value: '1024x1792', label: 'Portrait (1024x1792)' },
    { value: '1792x1024', label: 'Landscape (1792x1024)' },
  ];

  return (
    <div className="flex flex-col space-y-2">
      <label className="text-sm font-medium text-gray-700 dark:text-gray-300">
        Image Size
      </label>
      <div className="grid grid-cols-2 gap-2">
        {sizes.map((size) => (
          <button
            key={size.value}
            onClick={() => onSizeSelect(size.value)}
            className={`px-3 py-2 text-xs rounded-md transition-colors ${
              selectedSize === size.value
                ? 'bg-blue-600 text-white'
                : 'bg-gray-200 dark:bg-gray-700 text-gray-800 dark:text-gray-200 hover:bg-gray-300 dark:hover:bg-gray-600'
            }`}
          >
            {size.label}
          </button>
        ))}
      </div>
    </div>
  );
};

export default ImageSizeSelector;
