import { create } from 'zustand';
import { immer } from 'zustand/middleware/immer';
import {
    saveConfigurationProfile,
    updateConfigurationProfile,
    getConfigurationProfiles, // Import function to list profiles
    getConfigurationProfileById, // Import function to get a specific profile
    FullProfileData,
} from '../services/configApiService';
// Import Auth API service functions and types
import {
    registerUser as registerUserApi,
    loginUser as loginUserApi,
    fetchCurrentUser,
    updateLastUsedProfile,
    storeToken,
    getToken,
    removeToken,
    UserData
} from '../services/authApiService';

// Define the structure for a single agent's configuration
interface AgentConfig {
  provider: string;
  model: string;
  instructions: string;
  useDefaultInstructions?: boolean;
  internetEnabled?: boolean;
  temperature?: number;
  maxTokens?: number;
  topP?: number;
  presencePenalty?: number;
  frequencyPenalty?: number;

  // OpenRouter advanced parameters (all optional)
  topK?: number;
  repetitionPenalty?: number;
  minP?: number;
  topA?: number;
  seed?: number;
  logitBias?: Record<string, number>;
  logprobs?: boolean;
  topLogprobs?: number;
  responseFormat?: Record<string, any>;
  structuredOutputs?: boolean;
  stop?: string[];
  toolChoice?: any;
  maxPrice?: Record<string, any>;
}

// Define the search agent settings interface
interface SearchAgentSettings {
  provider: string;
  model: string;
  temperature: number;
  maxTokens?: number;
  systemPrompt?: string;

  // OpenRouter advanced parameters (all optional)
  topP?: number;
  topK?: number;
  frequencyPenalty?: number;
  presencePenalty?: number;
  repetitionPenalty?: number;
  minP?: number;
  topA?: number;
  seed?: number;
  logitBias?: Record<string, number>;
  logprobs?: boolean;
  topLogprobs?: number;
  responseFormat?: Record<string, any>;
  structuredOutputs?: boolean;
  stop?: string[];
  toolChoice?: any;
  maxPrice?: Record<string, any>;
}

// Define the structure of the settings object that can be loaded/saved
export interface LoadableConfigurationSettings {
  agentCount: number;
  generalInstructions: string;
  agentConfigurations: AgentConfig[];
  internetSettings: ConfigState['internetSettings'];
  searchAgentSettings?: SearchAgentSettings;
  ragSettings: ConfigState['ragSettings'];
  maxAgentRuns?: number;
  baseInstructions?: string;
  useBaseInstructions?: boolean;
  // Token limit settings
  maxContextWindow?: number;
  workingContextSize?: number;
  // New structure to store user additions/removals/last selected per provider
  userManagedModels?: Record<string, {
    added: string[];
    removed: string[];
    lastSelected?: string;
  }>;
}


// Define the state structure for the store
interface ConfigState {
  // --- Configuration State ---
  agentCount: number;
  generalInstructions: string;
  baseInstructions: string;
  useBaseInstructions: boolean;
  agentConfigurations: AgentConfig[];
  internetSettings: {
    enabled: boolean;
    searchProvider: string;
    searchApiKey: string;
    googleCxId?: string;
    includedDomains: string[];
    excludedDomains: string[];
  };
  searchAgentSettings?: SearchAgentSettings;
  ragSettings: {
    enabled: boolean;
    chunkingStrategy: string;
    chunkSize: number;
    chunkOverlap: number;
    embeddingModel: string;
    retrievalNResults: number;
    retrievalThreshold: number;
    useReranking: boolean;
    useQueryExpansion: boolean;
  };
  maxAgentRuns: number;
  // Token limit settings
  maxContextWindow: number;
  workingContextSize: number;
  // Store user additions/removals/last selected per provider
  userManagedModels: Record<string, {
    added: string[];
    removed: string[];
    lastSelected?: string;
  }>;
  currentProfileId: string | null;
  currentProfileName: string | null;

  // --- UI/Processing State ---
  isProcessing: boolean;
  isCancelling: boolean; // <<< ADDED: State for cancellation request
  isGlobalLoading: boolean;
  globalError: string | null;
  globalSuccess: string | null;

  // --- Authentication State ---
  isAuthenticated: boolean;
  user: UserData | null;
  token: string | null;
  authLoading: boolean; // Loading state specific to auth actions

  // --- Configuration Actions ---
  setAgentCount: (count: number) => void;
  setGeneralInstructions: (instructions: string) => void;
  setBaseInstructions: (instructions: string) => void;
  setUseBaseInstructions: (use: boolean) => void;
  updateAgentConfig: (index: number, newConfig: Partial<AgentConfig>) => void;
  setAgentInstructions: (index: number, instructions: string) => void;
  initializeAgents: (count: number) => void;
  setInternetEnabled: (enabled: boolean) => void;
  setSearchProvider: (provider: string) => void;
  setSearchApiKey: (apiKey: string) => void;
  setGoogleCxId: (cxId: string) => void;
  setIncludedDomains: (domains: string[]) => void;
  setExcludedDomains: (domains: string[]) => void;
  updateSearchAgentSettings: (settings: Partial<SearchAgentSettings>) => void;
  setRagEnabled: (enabled: boolean) => void;
  setChunkingStrategy: (strategy: string) => void;
  setChunkSize: (size: number) => void;
  setChunkOverlap: (overlap: number) => void;
  setEmbeddingModel: (model: string) => void;
  setRetrievalNResults: (n: number) => void;
  setRetrievalThreshold: (threshold: number) => void;
  setUseReranking: (use: boolean) => void;
  setUseQueryExpansion: (use: boolean) => void;
  loadSettings: (settings: LoadableConfigurationSettings) => void;
  setMaxAgentRuns: (runs: number) => void;
  // Token limit actions
  setMaxContextWindow: (size: number) => void;
  setWorkingContextSize: (size: number) => void;
  // Actions for managing the new structure
  addUserModel: (provider: string, modelName: string) => void;
  removeUserModel: (provider: string, modelName: string) => void;
  setLastSelectedModel: (provider: string, modelName: string) => void;
  setCurrentProfile: (id: string | null, name: string | null) => void;
  getSettingsForSave: () => LoadableConfigurationSettings;
  saveCurrentProfile: (newName?: string) => Promise<void>;

  // --- UI/Processing Actions ---
  setIsProcessing: (processing: boolean) => void;
  setIsCancelling: (cancelling: boolean) => void; // <<< ADDED: Action for cancellation state
  setGlobalLoading: (isLoading: boolean) => void;
  setGlobalError: (error: string | null) => void;
  setGlobalSuccess: (message: string | null) => void;
  clearGlobalMessages: () => void;

  // --- Authentication Actions ---
  login: (credentials: { email: string; password: string }) => Promise<void>;
  register: (credentials: { email: string; password: string }) => Promise<void>;
  logout: () => void;
  checkAuth: () => Promise<void>; // Action to check token validity on app load
  setAuthToken: (token: string) => Promise<void>; // <<< ADDED: New action for token login
}

const MIN_AGENTS = 1;
const MAX_AGENTS = 5;

const defaultAgentConfig: AgentConfig = {
  provider: '',
  model: '',
  instructions: '',
  useDefaultInstructions: true,
  internetEnabled: true,
  temperature: 0.7,
  maxTokens: 4096,
  topP: 1.0,
  presencePenalty: 0.0,
  frequencyPenalty: 0.0,
};

// Create the Zustand store
export const useConfigStore = create<ConfigState>()(
  immer((set, get) => ({
    // --- Initial State ---
    agentCount: MIN_AGENTS,
    generalInstructions: '',
    baseInstructions: '',
    useBaseInstructions: false,
    agentConfigurations: Array(MIN_AGENTS).fill(defaultAgentConfig),
    internetSettings: {
      enabled: false,
      searchProvider: 'Serper',
      searchApiKey: '',
      googleCxId: '',
      includedDomains: [],
      excludedDomains: [],
    },
    searchAgentSettings: {
      provider: 'Google',
      model: 'gemini-2.0-flash',
      temperature: 0.3,
      maxTokens: 4000, // Increased from 1500 to 4000 for better news summaries
      systemPrompt: `You are a specialized Search Synthesis Agent. Your purpose is to analyze web search results and create a comprehensive, accurate, and contextually relevant summary based *only* on the provided information.

TODAY'S DATE IS: ${new Date().toLocaleDateString('en-US', { weekday: 'long', year: 'numeric', month: 'long', day: 'numeric' })}. Use this as your reference point for recency when evaluating search results.

**Your Core Task:**

1. **Analyze:** Carefully examine all provided search result snippets and content.
2. **Extract & Synthesize:** Identify and extract the most relevant and accurate information related to the user's query. Synthesize this into a well-structured response that includes **key information and important contextual details** necessary for a full understanding.
3. **Connect (If Applicable & Supported):** If multiple reliable sources clearly point to an objective trend, consensus, or connection directly relevant to the query, you may highlight this, ensuring it is strongly supported by and grounded in the provided sources.
4. **Cite:** Accurately cite the source URLs for the information presented.
5. **Prioritize Recency:** Give priority to information that is most current relative to TODAY'S DATE, especially for time-sensitive queries like "news".
6. **Clean Content:** The search results may contain HTML, CSS, or other formatting artifacts. Ignore these technical elements and focus only on the actual content. If you see CSS variables, HTML tags, or other code elements, do not include them in your response.

**Important Guidelines:**

* **Strictly Grounded:** Your entire response **must** be based *solely* on the information contained within the provided search results. Do **not** introduce external knowledge, personal opinions, or interpretations not directly and clearly supported by the sources.
* **Factual Accuracy:** Ensure all presented information is factually accurate according to the sources.
* **Handle Limitations:** If the search results lack sufficient relevant information or are outdated, clearly state this limitation (e.g., "The search results did not contain recent information on X," or "The available information on Y appears to be from [Date/Source]").
* **Date Reporting:** You may report future dates *if they are explicitly mentioned as factual information within the search results* (e.g., 'an election is scheduled for [Date]'). Do **not** speculate about the future or misrepresent the current date (TODAY'S DATE IS: ${new Date().toLocaleDateString('en-US', { weekday: 'long', year: 'numeric', month: 'long', day: 'numeric' })}).
* **Clarity and Structure:** Organize the synthesized information logically using paragraphs, bullet points, or lists as appropriate for readability.
* **Neutral Tone:** Maintain an objective and neutral tone.
* **No Self-Reference:** Do **not** mention that you are a search agent or refer to the process of analyzing search results. Act as if you are presenting the synthesized information directly.
* **Downstream Use:** Your synthesis will likely be used by another AI agent for further analysis or response generation. Aim for a balance of comprehensiveness (including relevant context) and conciseness.

**Output Format:**

    [Raw data received from search engine]
[Your synthesized answer here, well-structured and incorporating relevant context from the sources.]

Sources:
- [URL1]
- [URL2]
...`
    },
    ragSettings: {
      enabled: false,
      chunkingStrategy: 'semantic',
      chunkSize: 512,
      chunkOverlap: 50,
      embeddingModel: 'Xenova/all-mpnet-base-v2',
      retrievalNResults: 5,
      retrievalThreshold: 0.7,
      useReranking: false,
      useQueryExpansion: false,
    },
    isProcessing: false,
    maxAgentRuns: 1,
    // Token limit settings with default values
    maxContextWindow: 20000,
    workingContextSize: 16384,
    userManagedModels: {}, // Initialize as empty object
    currentProfileId: null,
    currentProfileName: null,
    isCancelling: false, // <<< ADDED: Initial state
    isGlobalLoading: false,
    globalError: null,
    globalSuccess: null,
    // Auth Initial State
    isAuthenticated: false,
    user: null,
    token: getToken(), // Initialize token from storage
    authLoading: false,

    // --- Configuration Actions Implementation ---
    setAgentCount: (count) => {
        const clampedCount = Math.max(MIN_AGENTS, Math.min(MAX_AGENTS, count));
        set((state) => {
            state.agentCount = clampedCount;
            const currentConfigs = state.agentConfigurations;
            const newConfigs = Array(clampedCount).fill(null).map((_, i) =>
                currentConfigs[i] || defaultAgentConfig
            );
            state.agentConfigurations = newConfigs;
        });
    },
    setGeneralInstructions: (instructions) => set((state) => { state.generalInstructions = instructions; }),
    setBaseInstructions: (instructions) => set((state) => { state.baseInstructions = instructions; }),
    setUseBaseInstructions: (use) => set((state) => { state.useBaseInstructions = use; }),
    setAgentInstructions: (index, instructions) => {
        set((state) => {
            if (index >= 0 && index < state.agentConfigurations.length) {
                // Only update the instructions field, leave other fields unchanged
                state.agentConfigurations[index] = {
                    ...state.agentConfigurations[index],
                    instructions: instructions,
                    useDefaultInstructions: false // Set to false since we're providing custom instructions
                };
            } else {
                console.warn(`Attempted to update instructions for non-existent agent at index: ${index}`);
            }
        });
    },
    updateAgentConfig: (index, newConfig) => {
        set((state) => {
            if (index >= 0 && index < state.agentConfigurations.length) {
                // Get the current configuration
                const currentConfig = state.agentConfigurations[index];

                // Check if the update would actually change anything
                let hasChanges = false;
                for (const key in newConfig) {
                    if (Object.prototype.hasOwnProperty.call(newConfig, key)) {
                        const typedKey = key as keyof AgentConfig;
                        if (newConfig[typedKey] !== currentConfig[typedKey]) {
                            hasChanges = true;
                            break;
                        }
                    }
                }

                // Skip the update if nothing would change
                if (!hasChanges) {
                    return;
                }

                // Special handling for model changes
                if (newConfig.model !== undefined) {
                    // If we're setting a model, ensure we have a provider
                    const provider = newConfig.provider || currentConfig.provider;

                    // Skip if provider is missing
                    if (!provider) {
                        console.warn(`Cannot update model without a provider for agent ${index}`);
                        return;
                    }

                    // If we're clearing the model (empty string), don't do anything special
                    if (newConfig.model !== '') {
                        // Ensure we have a userManagedModels entry for this provider
                        if (!state.userManagedModels[provider]) {
                            state.userManagedModels[provider] = { added: [], removed: [], lastSelected: undefined };
                        }

                        // Update the lastSelected model for this provider
                        state.userManagedModels[provider].lastSelected = newConfig.model;
                        console.log(`Store: Updated lastSelected model for ${provider} to ${newConfig.model}`);
                    }
                }

                // Update the agent configuration
                state.agentConfigurations[index] = { ...currentConfig, ...newConfig };
            } else {
                console.warn(`Attempted to update non-existent agent config at index: ${index}`);
            }
        });
    },
    initializeAgents: (count) => {
        const clampedCount = Math.max(MIN_AGENTS, Math.min(MAX_AGENTS, count));
        set({ agentCount: clampedCount, agentConfigurations: Array(clampedCount).fill(defaultAgentConfig) });
    },
    setInternetEnabled: (enabled) => set((state) => { state.internetSettings.enabled = enabled; }),
    setSearchProvider: (provider) => set((state) => { state.internetSettings.searchProvider = provider; }),
    setSearchApiKey: (apiKey) => set((state) => { state.internetSettings.searchApiKey = apiKey; }),
    setGoogleCxId: (cxId) => set((state) => { state.internetSettings.googleCxId = cxId; }),
    setIncludedDomains: (domains) => set((state) => { state.internetSettings.includedDomains = domains; }),
    setExcludedDomains: (domains) => set((state) => { state.internetSettings.excludedDomains = domains; }),
    updateSearchAgentSettings: (settings) => set((state) => {
      // Initialize searchAgentSettings if it doesn't exist
      if (!state.searchAgentSettings) {
        state.searchAgentSettings = {
          provider: 'Google',
          model: 'gemini-2.0-flash',
          temperature: 0.3,
          maxTokens: 4000,
          systemPrompt: `You are a specialized Search Synthesis Agent. Your purpose is to analyze web search results and create a comprehensive, accurate, and contextually relevant summary based *only* on the provided information.`
        };
      }
      // Update with new settings
      state.searchAgentSettings = { ...state.searchAgentSettings, ...settings };
    }),
    setRagEnabled: (enabled) => set((state) => { state.ragSettings.enabled = enabled; }),
    setChunkingStrategy: (strategy) => set((state) => { state.ragSettings.chunkingStrategy = strategy; }),
    setChunkSize: (size) => set((state) => { state.ragSettings.chunkSize = size; }),
    setChunkOverlap: (overlap) => set((state) => { state.ragSettings.chunkOverlap = overlap; }),
    setEmbeddingModel: (model) => set((state) => { state.ragSettings.embeddingModel = model; }),
    setRetrievalNResults: (n) => set((state) => { state.ragSettings.retrievalNResults = n; }),
    setRetrievalThreshold: (threshold) => set((state) => { state.ragSettings.retrievalThreshold = threshold; }),
    setUseReranking: (use) => set((state) => { state.ragSettings.useReranking = use; }),
    setUseQueryExpansion: (use) => set((state) => { state.ragSettings.useQueryExpansion = use; }),
    loadSettings: (settings) => {
        set((state) => {
            const agentCount = settings.agentCount ?? MIN_AGENTS;
            const agentConfigurations = settings.agentConfigurations?.map(agent => ({
                ...defaultAgentConfig, ...agent
            })) ?? Array(agentCount).fill(defaultAgentConfig);
            // Ensure array length matches count
            if (agentConfigurations.length !== agentCount) {
                const currentConfigs = agentConfigurations;
                agentConfigurations.length = agentCount; // Adjust length
                for(let i = 0; i < agentCount; i++) {
                    agentConfigurations[i] = currentConfigs[i] || defaultAgentConfig;
                }
            }
            state.agentCount = agentCount;
            state.generalInstructions = settings.generalInstructions ?? '';
            state.agentConfigurations = agentConfigurations;
            state.internetSettings = settings.internetSettings ?? state.internetSettings;
            state.searchAgentSettings = settings.searchAgentSettings ?? state.searchAgentSettings;
            state.ragSettings = settings.ragSettings ?? state.ragSettings;
            state.maxAgentRuns = settings.maxAgentRuns ?? state.maxAgentRuns;
            state.baseInstructions = settings.baseInstructions ?? '';
            state.useBaseInstructions = settings.useBaseInstructions ?? false;
            // Load token limit settings
            state.maxContextWindow = settings.maxContextWindow ?? state.maxContextWindow;
            state.workingContextSize = settings.workingContextSize ?? state.workingContextSize;
            // Load userManagedModels, ensuring it's an object and validating sub-properties
            const loadedManaged = settings.userManagedModels;
            if (typeof loadedManaged === 'object' && loadedManaged !== null) {
                // Basic validation for each provider entry
                Object.keys(loadedManaged).forEach(provider => {
                    const entry = loadedManaged[provider];
                    if (typeof entry === 'object' && entry !== null) {
                        state.userManagedModels[provider] = {
                            added: Array.isArray(entry.added) ? entry.added : [],
                            removed: Array.isArray(entry.removed) ? entry.removed : [],
                            lastSelected: typeof entry.lastSelected === 'string' ? entry.lastSelected : undefined,
                        };
                    } else {
                         // If an entry is invalid, initialize it safely
                         state.userManagedModels[provider] = { added: [], removed: [], lastSelected: undefined };
                    }
                });
            } else {
                state.userManagedModels = {}; // Default to empty if not an object
            }
        });
    },
    setMaxAgentRuns: (runs) => set((state) => { state.maxAgentRuns = Math.max(1, runs); }),
    // Token limit actions
    setMaxContextWindow: (size) => set((state) => { state.maxContextWindow = Math.max(1000, size); }),
    setWorkingContextSize: (size) => set((state) => { state.workingContextSize = Math.max(1000, size); }),
    // Actions for managing the new structure
    addUserModel: (provider, modelName) => {
        if (!provider || !modelName) return;
        set((state) => {
            // Ensure provider entry exists (inlined helper logic)
            if (!state.userManagedModels[provider]) {
                state.userManagedModels[provider] = { added: [], removed: [], lastSelected: undefined };
            }
            const entry = state.userManagedModels[provider];
            // Add to 'added' list if not already there
            if (!entry.added.includes(modelName)) {
                entry.added.push(modelName);
            }
            // Remove from 'removed' list if it was previously removed
            entry.removed = entry.removed.filter(m => m !== modelName);
        });
        // Note: Component should trigger saveCurrentProfile after calling this
    },
    removeUserModel: (provider, modelName) => {
        if (!provider || !modelName) return;
        set((state) => {
             // Ensure provider entry exists (inlined helper logic)
             if (!state.userManagedModels[provider]) {
                // If no entry, nothing to remove
                return;
            }
            const entry = state.userManagedModels[provider];
            // Add to 'removed' list if not already there
            if (!entry.removed.includes(modelName)) {
                entry.removed.push(modelName);
            }
            // Remove from 'added' list if it was previously added
            entry.added = entry.added.filter(m => m !== modelName);
            // If the removed model was the last selected, clear lastSelected
            if (entry.lastSelected === modelName) {
                entry.lastSelected = undefined;
            }
        });
        // Note: Component should trigger saveCurrentProfile after calling this
    },
    setLastSelectedModel: (provider, modelName) => {
        if (!provider) return;
        set((state) => {
            // Ensure provider entry exists (inlined helper logic)
             if (!state.userManagedModels[provider]) {
                state.userManagedModels[provider] = { added: [], removed: [], lastSelected: undefined };
            }
            // Only update if the modelName is actually provided (don't set undefined)
            if (modelName) {
                 state.userManagedModels[provider].lastSelected = modelName;
            } else {
                 // If modelName is empty/null, clear the last selected
                 delete state.userManagedModels[provider].lastSelected;
            }
        });
        // Note: Component should trigger saveCurrentProfile after calling this
    },
    setCurrentProfile: (id, name) => {
        set((state) => { state.currentProfileId = id; state.currentProfileName = name; });
        // If we have a valid profile ID, update the user's last used profile ID
        if (id) {
            updateLastUsedProfile(id).catch(error => {
                console.error('Error updating last used profile ID:', error);
            });
        }
    },
    getSettingsForSave: () => {
        // Use get() to access the latest state within this action/selector
        const state = get();
        return {
            agentCount: state.agentCount,
            generalInstructions: state.generalInstructions,
            agentConfigurations: state.agentConfigurations,
            internetSettings: state.internetSettings,
            searchAgentSettings: state.searchAgentSettings,
            ragSettings: state.ragSettings,
            maxAgentRuns: state.maxAgentRuns,
            baseInstructions: state.baseInstructions,
            useBaseInstructions: state.useBaseInstructions,
            maxContextWindow: state.maxContextWindow,
            workingContextSize: state.workingContextSize,
            userManagedModels: state.userManagedModels, // Include the new structure
        };
    },
    saveCurrentProfile: async (newName?: string) => {
        const currentId = get().currentProfileId;
        const currentName = get().currentProfileName;
        const settingsToSave = get().getSettingsForSave(); // *** Re-enable fetching settings ***
        const nameToSave = newName || currentName;

        console.log('Attempting to save profile with settings:', settingsToSave); // Log the settings being saved

        if (!nameToSave) {
            get().setGlobalError("Profile name cannot be empty.");
            return;
        }
        get().setGlobalLoading(true);
        get().clearGlobalMessages();
        try {
            if (currentId && !newName) { // Update existing profile
                // *** Correct arguments again: Pass ID and the data object { name, settings } ***
                await updateConfigurationProfile(currentId, { name: nameToSave, settings: settingsToSave });
                if (nameToSave !== currentName) {
                    set({ currentProfileName: nameToSave });
                }
            } else { // Save new profile or Save As
                 // *** Correct arguments: Pass name and settings object ***
                const savedProfile: FullProfileData = await saveConfigurationProfile(nameToSave, settingsToSave);
                set({ currentProfileId: savedProfile._id, currentProfileName: savedProfile.name });
            }
            get().setGlobalSuccess(`Profile '${nameToSave}' saved successfully!`);
        } catch (error: any) {
            get().setGlobalError(error.response?.data?.message || error.message || 'Failed to save profile.');
            throw error;
        } finally {
            get().setGlobalLoading(false);
        }
    },

    // --- UI/Processing Actions Implementation ---
    setIsProcessing: (processing) => set((state) => { state.isProcessing = processing; }),
    setIsCancelling: (cancelling) => set((state) => { state.isCancelling = cancelling; }), // <<< ADDED: Implementation
    setGlobalLoading: (isLoading) => set((state) => { state.isGlobalLoading = isLoading; }),
    setGlobalError: (error) => set((state) => { state.globalError = error; state.globalSuccess = null; state.isGlobalLoading = false; }),
    setGlobalSuccess: (message) => set((state) => { state.globalSuccess = message; state.globalError = null; state.isGlobalLoading = false; }),
    clearGlobalMessages: () => set((state) => { state.globalError = null; state.globalSuccess = null; }),

    // --- Authentication Actions Implementation ---
    login: async (credentials) => {
      set({ authLoading: true, globalError: null });
      try {
        const { token, user } = await loginUserApi(credentials);
        storeToken(token);
        set({ isAuthenticated: true, user, token, authLoading: false });
        // --- Load first profile automatically ---
        try {
            console.log("Login successful, attempting to load profiles...");
            const profiles = await getConfigurationProfiles();
            if (profiles && profiles.length > 0) {
                // Check if the user has a last used profile ID
                if (user.lastUsedProfileId) {
                    console.log(`User has a last used profile ID: ${user.lastUsedProfileId}, attempting to load it...`);
                    try {
                        const fullProfile = await getConfigurationProfileById(user.lastUsedProfileId);
                        get().loadSettings(fullProfile.settings);
                        get().setCurrentProfile(fullProfile._id, fullProfile.name);
                        console.log("Last used profile loaded successfully.");
                    } catch (lastProfileError) {
                        console.error("Error loading last used profile, falling back to first profile:", lastProfileError);
                        // Fall back to loading the first profile
                        const fullProfile = await getConfigurationProfileById(profiles[0]._id);
                        get().loadSettings(fullProfile.settings);
                        get().setCurrentProfile(fullProfile._id, fullProfile.name);
                        console.log("First profile loaded as fallback.");
                    }
                } else {
                    // No last used profile, load the first one
                    console.log(`No last used profile ID, loading first one: ${profiles[0].name} (${profiles[0]._id})`);
                    const fullProfile = await getConfigurationProfileById(profiles[0]._id);
                    get().loadSettings(fullProfile.settings);
                    get().setCurrentProfile(fullProfile._id, fullProfile.name);
                    console.log("First profile loaded successfully.");
                }
            } else {
                 console.log("No profiles found for user.");
                 // Optionally reset to default state if needed, though login doesn't clear state by default
                 // get().initializeAgents(MIN_AGENTS); // Example reset if desired
                 get().setCurrentProfile(null, null); // Ensure no profile is marked as current
            }
        } catch (profileError: any) {
             console.error("Error auto-loading profile after login:", profileError);
             get().setGlobalError(`Login successful, but failed to load profiles: ${profileError.message}`);
             get().setCurrentProfile(null, null); // Ensure no profile is marked as current
        }
        // --- End profile loading ---
      } catch (error: any) {
        removeToken();
        set({ isAuthenticated: false, user: null, token: null, authLoading: false, globalError: error.message || 'Login failed' });
        throw error; // Re-throw for component handling
      }
    },
    register: async (credentials) => {
      set({ authLoading: true, globalError: null });
      try {
        const { token, user } = await registerUserApi(credentials);
        storeToken(token);
        set({ isAuthenticated: true, user, token, authLoading: false });
         // --- Load first profile automatically ---
         try {
            console.log("Registration successful, attempting to load profiles...");
            const profiles = await getConfigurationProfiles(); // Fetch profiles (might be empty for new user)
            if (profiles && profiles.length > 0) {
                // For new registrations, we'll just load the first profile
                // since there's no last used profile yet
                console.log(`Found ${profiles.length} profiles, loading first one: ${profiles[0].name} (${profiles[0]._id})`);
                const fullProfile = await getConfigurationProfileById(profiles[0]._id);
                get().loadSettings(fullProfile.settings);
                get().setCurrentProfile(fullProfile._id, fullProfile.name);
                console.log("First profile loaded successfully.");
            } else {
                 console.log("No profiles found for new user (expected).");
                 get().setCurrentProfile(null, null); // Ensure no profile is marked as current
            }
        } catch (profileError: any) {
             console.error("Error auto-loading profile after registration:", profileError);
             get().setGlobalError(`Registration successful, but failed to load profiles: ${profileError.message}`);
             get().setCurrentProfile(null, null);
        }
        // --- End profile loading ---
      } catch (error: any) {
        removeToken();
        set({ isAuthenticated: false, user: null, token: null, authLoading: false, globalError: error.message || 'Registration failed' });
        throw error; // Re-throw for component handling
      }
    },
    logout: () => {
      removeToken();
      // TODO: Disconnect socket if connected
      set({ isAuthenticated: false, user: null, token: null });
      // Optionally clear other user-specific state (configs, conversations)
    },
    checkAuth: async () => {
      const token = get().token; // Get token from state (initialized from storage)
      if (!token) {
        set({ isAuthenticated: false, user: null, token: null });
        return;
      }
        set({ authLoading: true });
        try {
          const user = await fetchCurrentUser(token);
          set({ isAuthenticated: true, user, token, authLoading: false });
           // --- Load first profile automatically ---
           try {
                console.log("Auth check successful, attempting to load profiles...");
                const profiles = await getConfigurationProfiles();
                if (profiles && profiles.length > 0) {
                    // Check if the user has a last used profile ID
                    if (user.lastUsedProfileId) {
                        console.log(`User has a last used profile ID: ${user.lastUsedProfileId}, attempting to load it...`);
                        try {
                            const fullProfile = await getConfigurationProfileById(user.lastUsedProfileId);
                            get().loadSettings(fullProfile.settings);
                            get().setCurrentProfile(fullProfile._id, fullProfile.name);
                            console.log("Last used profile loaded successfully.");
                        } catch (lastProfileError) {
                            console.error("Error loading last used profile, falling back to first profile:", lastProfileError);
                            // Fall back to loading the first profile
                            const fullProfile = await getConfigurationProfileById(profiles[0]._id);
                            get().loadSettings(fullProfile.settings);
                            get().setCurrentProfile(fullProfile._id, fullProfile.name);
                            console.log("First profile loaded as fallback.");
                        }
                    } else {
                        // No last used profile, load the first one
                        console.log(`No last used profile ID, loading first one: ${profiles[0].name} (${profiles[0]._id})`);
                        const fullProfile = await getConfigurationProfileById(profiles[0]._id);
                        get().loadSettings(fullProfile.settings);
                        get().setCurrentProfile(fullProfile._id, fullProfile.name);
                        console.log("First profile loaded successfully.");
                    }
                } else {
                    console.log("No profiles found for user.");
                    get().setCurrentProfile(null, null); // Ensure no profile is marked as current
                }
            } catch (profileError: any) {
                console.error("Error auto-loading profile after auth check:", profileError);
                get().setGlobalError(`Authentication successful, but failed to load profiles: ${profileError.message}`);
                get().setCurrentProfile(null, null);
            }
          // --- End profile loading ---
        } catch (error) {
          console.warn('Auth check failed, logging out:', error);
          get().logout();
        set({ authLoading: false }); // Ensure loading is stopped
      }
    },
    setAuthToken: async (token) => {
        try {
            storeToken(token);
            set({ isAuthenticated: true, token });
            // Fetch user details using the new token
            const response = await fetchCurrentUser(token);
            set({ user: response });
            console.log('Auth token set, user details fetched:', response);
        } catch (error) {
            console.error('Failed to set auth token or fetch user:', error);
            // If fetching user fails, token might be invalid/expired, so log out
            get().logout();
            throw error; // Re-throw for component handling if needed
        }
    },
  }))
);
