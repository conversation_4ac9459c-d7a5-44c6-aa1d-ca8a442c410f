// Test script for <PERSON>g<PERSON><PERSON>ler
import { <PERSON>g<PERSON>and<PERSON> } from './services/ragHandler';
import path from 'path';
import fs from 'fs/promises';

async function testRagHandler() {
  try {
    console.log('Initializing RAG handler with default knowledge base...');
    // Initialize RagHandler with default knowledge base
    const ragHandler = new RagHandler({
      embeddingModel: 'Xenova/all-MiniLM-L6-v2',
      chunkSize: 512,
      chunkOverlap: 50,
      retrievalThreshold: 0.5 // Lower threshold to get more results
    });

    // Wait for initialization
    console.log('Waiting for initialization...');
    await new Promise(resolve => setTimeout(resolve, 10000));

    // Add PDF files to the index
    console.log('\nAdding PDF files to the index...');
    const uploadsDir = path.join(__dirname, '..', 'uploads', 'rag_temp');
    const files = await fs.readdir(uploadsDir);
    const pdfFiles = files.filter(file => file.includes('blighter-b400-series'));

    console.log(`Found ${pdfFiles.length} matching PDF files:`, pdfFiles);

    for (const pdfFile of pdfFiles) {
      const filePath = path.join(uploadsDir, pdfFile);
      console.log(`Adding file: ${pdfFile}`);
      await ragHandler.addFile(filePath);
    }

    console.log('Files added successfully!');
    await new Promise(resolve => setTimeout(resolve, 2000));

    // Test retrieval for B400 radar
    console.log('\nTesting query: "Tell me about the B400 radar"');
    const results = await ragHandler.getRelevantChunks('Tell me about the B400 radar');

    console.log(`Retrieved ${results.length} chunks:`);
    results.forEach((chunk, i) => {
      console.log(`\nChunk ${i + 1}:`);
      console.log(`Content: ${chunk.content.substring(0, 150)}...`);
      console.log(`Source: ${chunk.metadata.source}`);
      console.log(`Similarity Score: ${chunk.similarity}`);
      if ('l2Distance' in chunk) {
        console.log(`L2 Distance: ${chunk.l2Distance}`);
      }
    });

    // Test retrieval for a different query
    console.log('\nTesting query: "What is the maximum detection range?"');
    const rangeResults = await ragHandler.getRelevantChunks('What is the maximum detection range?');

    console.log(`Retrieved ${rangeResults.length} chunks:`);
    rangeResults.forEach((chunk, i) => {
      console.log(`\nChunk ${i + 1}:`);
      console.log(`Content: ${chunk.content.substring(0, 150)}...`);
      console.log(`Source: ${chunk.metadata.source}`);
      console.log(`Similarity Score: ${chunk.similarity}`);
      if ('l2Distance' in chunk) {
        console.log(`L2 Distance: ${chunk.l2Distance}`);
      }
    });

    console.log('\nTest completed successfully!');
  } catch (error) {
    console.error('Error testing RAG handler:', error);
  }
}

// Run the test
testRagHandler();
