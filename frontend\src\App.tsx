import { useState, useEffect, useRef, useCallback } from 'react';
import { Routes, Route, Navigate } from 'react-router-dom'; // Import routing components
import MainAppLayout from './components/MainAppLayout'; // Import the layout component
import LoginPage from './pages/LoginPage'; // Import Login page
import RegisterPage from './pages/RegisterPage'; // Import Register page
import LandingPage from './pages/LandingPage'; // Import Landing page
import ProtectedRoute from './components/ProtectedRoute'; // Import ProtectedRoute
// Placeholder for future Account page
const AccountPagePlaceholder: React.FC = () => <div className="p-4">Account/Subscription Management Page (Placeholder)</div>;
import { connectSocket, disconnectSocket } from './services/socketService';
import { useConfigStore } from './store/configStore';
// import axios from 'axios'; // Removed unused import
import apiClient from './services/authApiService'; // Import configured client
import { FullConversationData, DiscussionRecord } from './types';
import AuthCallbackPage from './pages/AuthCallbackPage'; // Import the callback page

// Type for theme
type Theme = 'light' | 'dark';

// Import the base URL from config is no longer needed here
// We're using apiClient from authApiService which already has the base URL configured

// Main Application Component
function App() {
  // Theme state
  const [theme, setTheme] = useState<Theme>(() => {
    const savedTheme = localStorage.getItem('theme') as Theme;
    if (savedTheme) return savedTheme;
    if (window.matchMedia && window.matchMedia('(prefers-color-scheme: dark)').matches) {
      return 'dark';
    }
    return 'light';
  });

  // UI State
  const [isConsoleVisible, setIsConsoleVisible] = useState(false);
  const [isConversationListVisible, setIsConversationListVisible] = useState(true);

  // Config State (partially from Zustand)
  const setIsProcessing = useConfigStore((state) => state.setIsProcessing);
  const setIsCancelling = useConfigStore((state) => state.setIsCancelling); // <<< Get cancelling setter
  const loadSettings = useConfigStore((state) => state.loadSettings);
  const setCurrentProfile = useConfigStore((state) => state.setCurrentProfile);
  const [providers, setProviders] = useState<string[]>([]);
  const [providersLoading, setProvidersLoading] = useState<boolean>(true);
  const [providersError, setProvidersError] = useState<string | null>(null);

  // Chat Content State
  const [agentDiscussion, setAgentDiscussion] = useState<DiscussionRecord>({});
  const [discussionOrder, setDiscussionOrder] = useState<string[]>([]);
  const [finalAnswer, setFinalAnswer] = useState<string>('');
  const [finalAnswerImage, setFinalAnswerImage] = useState<string | null>(null);
  const [currentConversationId, setCurrentConversationId] = useState<string | null>(null);
  const [consoleLogs, setConsoleLogs] = useState<string[]>([]);
  const consoleEndRef = useRef<HTMLDivElement>(null);

  // Authentication State & Actions from Zustand store
  const {
    isAuthenticated,
    token,
    checkAuth,
    // logout // Removed unused import
  } = useConfigStore((state) => ({
    isAuthenticated: state.isAuthenticated,
    token: state.token,
    checkAuth: state.checkAuth,
    // logout: state.logout // Removed unused import
  }));

  // Helper function to add logs
  const MAX_LOGS = 100;
  const addLog = useCallback((message: string) => {
      const timestamp = new Date().toLocaleTimeString();
      setConsoleLogs(prevLogs => {
          const newLogs = [...prevLogs, `[${timestamp}] ${message}`];
          return newLogs.slice(Math.max(newLogs.length - MAX_LOGS, 0));
      });
  }, [setConsoleLogs]);

  // Apply theme class
  useEffect(() => {
    document.documentElement.classList.toggle('dark', theme === 'dark');
    localStorage.setItem('theme', theme);
  }, [theme]);

  // Check authentication status on initial load
  useEffect(() => {
    checkAuth();
  }, [checkAuth]);

  // Function to clear chat state and reset conversation ID
  const clearChat = useCallback(() => {
      setAgentDiscussion({});
      setDiscussionOrder([]);
      setFinalAnswer('');
      setFinalAnswerImage(null);
      setCurrentConversationId(null);
      addLog("Chat cleared.");
  }, [addLog]);

  // Function to load a selected conversation
  const handleLoadConversation = useCallback(async (conversationId: string) => {
      addLog(`Attempting to load conversation: ${conversationId}`);
      // --- Explicitly clear previous state before loading ---
      setAgentDiscussion({});
      setDiscussionOrder([]);
      setFinalAnswer('');
      setFinalAnswerImage(null);
      setCurrentConversationId(null); // Temporarily clear ID while loading
      setCurrentProfile(null, null);
      setIsProcessing(false); // Ensure processing is false
      // --- End clearing ---
      try {
          // Use apiClient which handles token automatically
          const response = await apiClient.get<FullConversationData>(`/api/conversations/${conversationId}`); // Use relative path
          const conversationData = response.data;

          // --- Set the ID *after* successful fetch ---
          setCurrentConversationId(conversationData._id);
          // setCurrentProfile(null, null); // Already cleared above

          const loadedDiscussion: DiscussionRecord = {};
          const loadedOrder: string[] = [];
          // let currentAgentName: string | null = null; // Removed unused variable

          conversationData.messages.forEach(msg => {
              let agentKey = '';
              // Create unique keys for discussion panel rendering
              if (msg.role === 'user') agentKey = `user_${msg.timestamp || Date.now()}`;
              else if (msg.role === 'assistant') agentKey = `${msg.agentName || 'Assistant'}_${msg.timestamp || Date.now()}`;
              else agentKey = `system_${msg.timestamp || Date.now()}`; // Group system/tool

              // Special handling for Image Generator messages
              if (msg.role === 'assistant' && msg.agentName === 'Image Generator') {
                // Check for image path in content
                const imagePathMatch = msg.content?.match(/\(Viewable in chat\): (\/generated_images\/[\w\-\.]+)/i) ||
                                      msg.content?.match(/Generated Image.*?: (\/generated_images\/[\w\-\.]+)/i);

                if (imagePathMatch && imagePathMatch[1]) {
                  // Create a special image message key
                  const imageKey = `image_gen_${msg.timestamp || Date.now()}`;
                  const imagePath = imagePathMatch[1];

                  // Store the image message as a GeneratedImageMessage object
                  loadedDiscussion[imageKey] = {
                    type: 'image_generated',
                    key: imageKey,
                    localImagePath: imagePath,
                    error: undefined
                  };
                  loadedOrder.push(imageKey);

                  // Also add the text description if it exists
                  const textContent = msg.content.replace(imagePathMatch[0], '').trim();
                  if (textContent) {
                    if (loadedDiscussion[agentKey]) {
                      // Append if key collision (unlikely with timestamp but safe)
                      loadedDiscussion[agentKey] += `\n${textContent}`;
                    } else {
                      loadedDiscussion[agentKey] = textContent;
                      loadedOrder.push(agentKey);
                    }
                  }
                } else {
                  // Regular message handling for Image Generator without image path
                  if (loadedDiscussion[agentKey]) {
                    // Append if key collision (unlikely with timestamp but safe)
                    loadedDiscussion[agentKey] += `\n${msg.content}`;
                  } else {
                    loadedDiscussion[agentKey] = msg.content;
                    loadedOrder.push(agentKey);
                  }
                }
              } else {
                // Regular message handling
                if (loadedDiscussion[agentKey]) {
                  // Append if key collision (unlikely with timestamp but safe)
                  loadedDiscussion[agentKey] += `\n${msg.content}`;
                } else {
                  loadedDiscussion[agentKey] = msg.content;
                  loadedOrder.push(agentKey);
                }
              }
          });

          setAgentDiscussion(loadedDiscussion);
          setDiscussionOrder(loadedOrder);

          // Find the last assistant message for the final answer panel
          const lastAssistantMessage = [...conversationData.messages].reverse().find(m => m.role === 'assistant');
          setFinalAnswer(lastAssistantMessage?.content || '');

          // Check if there's an image to display in the final answer
          const lastImageMessage = [...conversationData.messages].reverse().find(m =>
            m.role === 'assistant' && m.agentName === 'Image Generator'
          );

          if (lastImageMessage) {
            // Check for image path in content
            const imagePathMatch = lastImageMessage.content?.match(/\(Viewable in chat\): (\/generated_images\/[\w\-\.]+)/i) ||
                                  lastImageMessage.content?.match(/Generated Image.*?: (\/generated_images\/[\w\-\.]+)/i);

            if (imagePathMatch && imagePathMatch[1]) {
              setFinalAnswerImage(imagePathMatch[1]);
            } else {
              setFinalAnswerImage(null);
            }
          } else {
            setFinalAnswerImage(null);
          }

          if (conversationData.metadata?.configSnapshot) {
              addLog("Loading configuration snapshot from conversation metadata...");
              loadSettings(conversationData.metadata.configSnapshot);
          } else {
               addLog("Warning: No configuration snapshot found in conversation metadata.");
               // Optionally reset to default settings?
          }

          addLog(`Conversation ${conversationId} loaded successfully.`);

      } catch (error: any) {
           addLog(`Error loading conversation ${conversationId}: ${error.response?.data?.message || error.message}`);
           alert(`Error loading conversation: ${error.response?.data?.message || error.message}`);
           // If loading fails, ensure state is cleared
           clearChat(); // Use clearChat to reset everything
      }
  }, [addLog, setAgentDiscussion, setDiscussionOrder, setFinalAnswer, setCurrentConversationId, setCurrentProfile, setIsProcessing, loadSettings, clearChat]);

  // Socket connection and event listeners (only if authenticated)
  useEffect(() => {
    if (!isAuthenticated || !token) {
        // Ensure socket is disconnected if not authenticated or token is missing
        disconnectSocket();
        return;
    }

    addLog('Authenticated. Attempting to connect socket...');
    // Pass token for socket authentication
    const socket = connectSocket(token);

    socket.on('connect', () => addLog('Socket connected successfully.'));
    socket.on('disconnect', (reason) => addLog(`Socket disconnected: ${reason}`));
    socket.on('connect_error', (error) => addLog(`Socket connection error: ${error.message}`));

    // --- Event Listeners ---
    // Updated listener to accept payload with conversationId
    const onProcessingStarted = ({ conversationId }: { conversationId?: string | null }) => {
      addLog(`Processing started... (Received ID: ${conversationId || 'None'})`);
      // Only clear discussion/answer if it's a NEW chat (ID from event is null/undefined)
      if (!conversationId) {
          addLog('New conversation detected (based on event data), clearing discussion panel.');
          setAgentDiscussion({});
          setDiscussionOrder([]);
          setFinalAnswer('');
      } else {
          addLog(`Continuing existing conversation ${conversationId} (based on event data), preserving discussion panel.`);
          // Optionally clear just the final answer if desired when continuing?
          // setFinalAnswer(''); // Decide if final answer should clear even when continuing
      }
    };

    // Updated listener to use uniqueTurnKey
    const onAgentResponseStart = ({ agentName, uniqueTurnKey }: { agentName: string, uniqueTurnKey: string }) => {
        addLog(`Agent response started: ${agentName} (Turn: ${uniqueTurnKey})`);
        // Use uniqueTurnKey as the key in the discussion state
        setAgentDiscussion(prev => ({ ...prev, [uniqueTurnKey]: '' }));
        // Add the uniqueTurnKey to the order
        setDiscussionOrder(prev => [...prev, uniqueTurnKey]);
    };

    // Updated listener to use uniqueTurnKey
    const onAgentResponseChunk = ({ agentName, uniqueTurnKey, content, error }: { agentName: string, uniqueTurnKey: string, content?: string, error?: string }) => {
        if (error) {
            addLog(`Agent stream error (${agentName} / ${uniqueTurnKey}): ${error}`);
        }
        // Use uniqueTurnKey to update the correct entry
        setAgentDiscussion(prev => {
            const currentContent = prev[uniqueTurnKey] || ''; // Use uniqueTurnKey
            const newContent = error ? `${currentContent}\n[STREAM ERROR: ${error}]` : currentContent + (content || ''); // Handle potentially undefined content
            return { ...prev, [uniqueTurnKey]: newContent }; // Use uniqueTurnKey
        });
        // Note: Order is already handled by onAgentResponseStart
    };

    // Updated listener (though mostly for logging now)
    const onAgentResponseEnd = ({ agentName, uniqueTurnKey }: { agentName: string, uniqueTurnKey: string }) => {
         addLog(`Agent response ended: ${agentName} (Turn: ${uniqueTurnKey})`);
     };

    // Handles final text answer OR the image path returned on successful image generation
    const onUpdateFinalAnswer = (answerOrPath: string) => {
      // Check if it looks like an image path returned from the orchestrator
      if (answerOrPath.startsWith('/generated_images/')) {
          addLog(`Image generation successful, received path: ${answerOrPath}`);
          // Set the image path for the final answer panel
          setFinalAnswerImage(answerOrPath);
          // Keep any existing text answer or clear it
          // setFinalAnswer(''); // Uncomment to clear text when image is generated
      } else {
          addLog('Final text answer received.');
          setFinalAnswer(answerOrPath); // It's a regular text answer
          // Don't clear the image when receiving text - this allows text+image combinations
          // setFinalAnswerImage(null); // Uncomment to clear image when text is received
      }
    };

    const onDiscussionCompleted = () => {
      addLog('Discussion completed.');
      setIsProcessing(false);
      setIsCancelling(false); // <<< Reset cancelling state
    };

    const onPong = (data: any) => {
      addLog(`Pong received: ${JSON.stringify(data)}`);
    };

    // Listener for when the backend confirms a new conversation has been created
    const onConversationStarted = ({ conversationId }: { conversationId: string }) => {
        addLog(`Backend confirmed new conversation started with ID: ${conversationId}`);
        setCurrentConversationId(conversationId); // Update the state

        // Create a reference to the conversation list refresh function
        const conversationListRef = document.getElementById('refresh-conversations-button');
        if (conversationListRef) {
            addLog('Triggering conversation list refresh...');
            // Simulate a click on the refresh button
            (conversationListRef as HTMLButtonElement).click();

            // Set a timeout to select the new conversation after the list refreshes
            setTimeout(() => {
                addLog(`Auto-selecting new conversation: ${conversationId}`);
                handleLoadConversation(conversationId);
            }, 500); // Wait for the refresh to complete
        } else {
            addLog('Warning: Could not find conversation list refresh button');
        }
    };

    // --- Specific Error Event Listeners ---
    const onLlmError = ({ message, code, agentName, context }: { message: string, code?: string, agentName?: string, context?: string }) => {
        const errorMsg = `LLM Error${agentName ? ` from ${agentName}` : ''}${context ? ` (${context})` : ''}: ${message}${code ? ` [${code}]` : ''}`;
        addLog(errorMsg);
        useConfigStore.getState().setGlobalError(errorMsg); // Use global feedback
    };
    const onRagError = ({ message, code }: { message: string, code?: string }) => {
        const errorMsg = `Knowledge Base Error: ${message}${code ? ` [${code}]` : ''}`;
        addLog(errorMsg);
        useConfigStore.getState().setGlobalError(errorMsg);
    };
    const onSearchError = ({ message, code }: { message: string, code?: string }) => {
        const errorMsg = `Internet Search Error: ${message}${code ? ` [${code}]` : ''}`;
        addLog(errorMsg);
        useConfigStore.getState().setGlobalError(errorMsg);
    };
    const onProcessingError = ({ message, context }: { message: string, context?: string }) => {
        const errorMsg = `Processing Error${context ? ` (${context})` : ''}: ${message}`;
        addLog(errorMsg);
        useConfigStore.getState().setGlobalError(errorMsg);
    };
    // Handle global messages from the server (warnings, info, etc.)
    const onGlobalMessage = ({ type, message }: { type: 'info' | 'warning' | 'error' | 'success', message: string }) => {
        addLog(`Global message (${type}): ${message}`);
        if (type === 'error') {
            useConfigStore.getState().setGlobalError(message);
        } else if (type === 'warning') {
            useConfigStore.getState().setGlobalError(message); // Use error display for warnings too
        } else if (type === 'success') {
            useConfigStore.getState().setGlobalSuccess(message);
        } else {
            // For info messages, just log them
            console.log(`[Info] ${message}`);
        }
    };
    // --- End Specific Error Listeners ---


    // Register listeners
    socket.on('processing_started', onProcessingStarted);
    socket.on('agent_response_start', onAgentResponseStart);
    socket.on('agent_response_chunk', onAgentResponseChunk);
    socket.on('agent_response_end', onAgentResponseEnd);
    socket.on('update_final_answer', onUpdateFinalAnswer);
    socket.on('discussion_completed', onDiscussionCompleted);
    socket.on('pong', onPong);
    socket.on('conversation_started', onConversationStarted);
    // Register new error listeners
    socket.on('llm_error', onLlmError);
    socket.on('rag_error', onRagError);
    socket.on('search_error', onSearchError);
    socket.on('processing_error', onProcessingError); // Catch generic processing errors too
    socket.on('global_message', onGlobalMessage); // Register global message listener
    // --- Add listener for image_generated to potentially clear finalAnswer ---
    // This ensures the final answer panel shows the generated image
    const handleImageGeneratedForFinalAnswer = (data: { localImagePath?: string; error?: string; key: string }) => {
        if (data.localImagePath && !data.error) {
            addLog(`Image generated event received (key: ${data.key}), updating final answer panel.`);
            setFinalAnswerImage(data.localImagePath);
            // Don't clear text answer - allows text+image combinations
            // setFinalAnswer(''); // Uncomment to clear text when image is generated
        } else if (data.error) {
             // Optionally set final answer to show the error?
             // setFinalAnswer(`Image Generation Error: ${data.error}`);
        }
    };
    socket.on('image_generated', handleImageGeneratedForFinalAnswer);
    // --- End image_generated listener ---

    // Cleanup function
    return () => {
        addLog('Cleaning up App listeners and disconnecting socket...');
        // Unregister listeners
        socket.off('processing_started', onProcessingStarted);
        socket.off('agent_response_start', onAgentResponseStart);
        socket.off('agent_response_chunk', onAgentResponseChunk);
        socket.off('agent_response_end', onAgentResponseEnd);
        socket.off('update_final_answer', onUpdateFinalAnswer);
        socket.off('discussion_completed', onDiscussionCompleted);
        socket.off('pong');
        socket.off('conversation_started', onConversationStarted);
        socket.off('image_generated', handleImageGeneratedForFinalAnswer); // Unregister image listener
        // Unregister new error listeners
        socket.off('llm_error', onLlmError);
        socket.off('rag_error', onRagError);
        socket.off('search_error', onSearchError);
        socket.off('processing_error', onProcessingError);
        socket.off('global_message', onGlobalMessage); // Unregister global message listener
        disconnectSocket(); // Ensure disconnection on cleanup
    };
    // Re-run effect if authentication status changes
  }, [isAuthenticated, token, setIsProcessing, setIsCancelling, addLog, handleLoadConversation]); // Include handleLoadConversation in dependencies

  // Fetch providers on mount or when authentication status changes
  useEffect(() => {
    // Only fetch if authenticated
    if (!isAuthenticated) {
        setProviders([]); // Clear providers if not authenticated
        return;
    }

    const fetchProviders = async () => {
      addLog('Fetching LLM providers...');
      setProvidersLoading(true);
      setProvidersError(null);
      try {
        const response = await apiClient.get('/api/llm/providers');
        if (response.status !== 200) throw new Error(`HTTP error! status: ${response.status}`);
        const data = response.data;
        if (!Array.isArray(data)) throw new Error('Invalid data format received for providers');
        setProviders(data);
        addLog(`Loaded ${data.length} providers.`);
      } catch (error: any) {
        addLog(`Error fetching providers: ${error.message}`);
        setProvidersError(error.message || 'Failed to load providers.');
      } finally {
        setProvidersLoading(false);
      }
    };
    fetchProviders();
    // Re-run this effect when isAuthenticated changes
  }, [isAuthenticated, addLog]);

  // Scroll console to bottom
  useEffect(() => {
      consoleEndRef.current?.scrollIntoView({ behavior: "smooth" });
  }, [consoleLogs]);

  // --- Handlers ---
  const toggleTheme = () => {
    const newTheme = theme === 'light' ? 'dark' : 'light';
    addLog(`Toggling theme to: ${newTheme}`);
    setTheme(newTheme);
  };

  const toggleConsoleVisibility = () => {
    addLog(`Toggling console visibility to: ${!isConsoleVisible}`);
    setIsConsoleVisible(!isConsoleVisible);
  };

  const toggleConversationList = () => { // Handler to toggle sidebar
      addLog(`Toggling conversation list visibility to: ${!isConversationListVisible}`);
      setIsConversationListVisible(!isConversationListVisible);
  };

  // const sendPing = () => { // Removed unused function
  //   const socket = getSocket();
  //   if (socket?.connected) {
  //     addLog('Sending ping to server...');
  //     socket.emit('ping', { message: 'Hello from client', timestamp: new Date().toISOString() });
  //   } else {
  //     addLog('Error: Socket not connected. Cannot send ping.');
  //   }
  // };

  // Function to clear chat state and reset conversation ID - moved above

  // Function to start a new chat (clears state but keeps config)
  const startNewChat = useCallback(() => {
      setAgentDiscussion({});
      setDiscussionOrder([]);
      setFinalAnswer('');
      setFinalAnswerImage(null);
      setCurrentConversationId(null);
      setIsProcessing(false);
      addLog("New chat started.");
  }, [addLog, setIsProcessing]);

  // Function to load a selected conversation - removed from here

  // --- Render Logic ---
  return (
    <Routes>
      <Route path="/login" element={<LoginPage />} />
      <Route path="/register" element={<RegisterPage />} />
      {/* Add placeholder route for account page */}
      <Route path="/account" element={<ProtectedRoute><AccountPagePlaceholder /></ProtectedRoute>} />
      <Route path="/auth/callback" element={<AuthCallbackPage />} />
      {/* Landing page route */}
      <Route path="/home" element={<LandingPage />} />
      <Route
        path="/app"
        element={
          <ProtectedRoute> {/* Wrap main layout with ProtectedRoute */}
            <MainAppLayout
              theme={theme}
              toggleTheme={toggleTheme} // Pass down handlers
              toggleConversationList={toggleConversationList}
              startNewChat={startNewChat}
              isConversationListVisible={isConversationListVisible}
              handleLoadConversation={handleLoadConversation}
              agentDiscussion={agentDiscussion}
              discussionOrder={discussionOrder}
              finalAnswer={finalAnswer}
              finalAnswerImage={finalAnswerImage}
              clearChat={clearChat}
              toggleConsoleVisibility={toggleConsoleVisibility}
              isConsoleVisible={isConsoleVisible}
              consoleLogs={consoleLogs}
              consoleEndRef={consoleEndRef}
              // Pass state setters down for immediate UI update
              setAgentDiscussion={setAgentDiscussion}
              setDiscussionOrder={setDiscussionOrder}
              providers={providers}
              providersLoading={providersLoading}
              providersError={providersError}
              currentConversationId={currentConversationId}
            />
          </ProtectedRoute>
        }
      />
      {/* Root path redirects to home (landing page) if not authenticated, or app if authenticated */}
      <Route path="/" element={<Navigate to={isAuthenticated ? "/app" : "/home"} replace />} />
      {/* Catch-all route */}
      <Route path="*" element={<Navigate to={isAuthenticated ? "/app" : "/home"} replace />} />
    </Routes>
  );
}

export default App;
