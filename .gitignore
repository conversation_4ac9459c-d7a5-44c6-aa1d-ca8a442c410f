# Python specific
__pycache__/
*.py[cod]
*$py.class
.pytest_cache/
venv/
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
.coverage
htmlcov/

# Dependencies
node_modules/
/.pnp
.pnp.js

# Production build files
/frontend/dist/
/backend/dist/

# Environment files
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*
rag_handler.log

# IDE specific files
.vscode/*
!.vscode/extensions.json
.idea/
.DS_Store
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# Testing
/coverage
test*.pdf
test*.js

# TypeScript incremental compilation cache
*.tsbuildinfo

# Large model files
*.onnx
backend/cache/Xenova/

# Generated/Temporary files
backend/public/generated_images/
backend/uploads/
backend/uploads/rag_temp/
code_archives/
nltk_data/

# RAG data files
*.pkl
*.index
backend/knowledge_base*/
backend/test_rag/

# Archives and temporary files
*.zip
*.tar.gz
*.rar
tatus
