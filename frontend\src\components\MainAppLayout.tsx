import React, { useState, useEffect, useCallback } from 'react';
import AgentDiscussionPanel from './AgentDiscussionPanel';
import FinalAnswerPanel from './FinalAnswerPanel';
import PromptInputArea from './PromptInputArea';
import ConfigurationTabs from './ConfigurationTabs';
import Header from './Header';
import ConversationList from './ConversationList';
import FeedbackDisplay from './FeedbackDisplay';
import CollapsiblePanel from './CollapsiblePanel';
import ResizableColumn from './ResizableColumn';

// Define props for the layout component
interface MainAppLayoutProps {
  theme: 'light' | 'dark';
  toggleTheme: () => void;
  toggleConversationList: () => void;
  startNewChat: () => void;
  isConversationListVisible: boolean;
  handleLoadConversation: (id: string) => void;
  agentDiscussion: Record<string, string | any>;
  discussionOrder: string[];
  finalAnswer: string;
  finalAnswerImage: string | null;
  clearChat: () => void;
  toggleConsoleVisibility: () => void;
  isConsoleVisible: boolean;
  consoleLogs: string[];
  consoleEndRef: React.RefObject<HTMLDivElement>;
  providers: string[];
  providersLoading: boolean;
  providersError: string | null;
  currentConversationId: string | null;
  // Add state setters for immediate UI update
  setAgentDiscussion: React.Dispatch<React.SetStateAction<Record<string, string | any>>>;
  setDiscussionOrder: React.Dispatch<React.SetStateAction<string[]>>;
}

const MainAppLayout: React.FC<MainAppLayoutProps> = ({
  theme,
  toggleTheme,
  toggleConversationList,
  startNewChat,
  isConversationListVisible,
  handleLoadConversation,
  agentDiscussion,
  discussionOrder,
  finalAnswer,
  finalAnswerImage,
  clearChat,
  toggleConsoleVisibility,
  isConsoleVisible,
  consoleLogs,
  consoleEndRef,
  providers,
  providersLoading,
  providersError,
  // Destructure the new state setters
  setAgentDiscussion,
  setDiscussionOrder,
  currentConversationId,
}) => {
  // State for collapsible panels
  const [isConvPanelPinned, setIsConvPanelPinned] = useState(false);
  const [isConfigPanelVisible, setIsConfigPanelVisible] = useState(true);
  const [isConfigPanelPinned, setIsConfigPanelPinned] = useState(true);

  // Handle keyboard shortcuts
  const handleKeyDown = useCallback((e: KeyboardEvent) => {
    // Ctrl+/ to toggle conversations panel
    if (e.ctrlKey && e.key === '/') {
      e.preventDefault();
      toggleConversationList();
    }
    // Ctrl+Enter to submit prompt (handled in PromptInputArea)
  }, [toggleConversationList]);

  // Set up keyboard shortcuts
  useEffect(() => {
    document.addEventListener('keydown', handleKeyDown);
    return () => {
      document.removeEventListener('keydown', handleKeyDown);
    };
  }, [handleKeyDown]);
  return (
    <div className="flex flex-col h-screen bg-background-light dark:bg-background-dark text-text-light dark:text-text-dark">
      <Header
        theme={theme}
        toggleTheme={toggleTheme}
        toggleConversationList={toggleConversationList}
        startNewChat={startNewChat}
      />

      <main className="flex flex-1 overflow-hidden relative bg-gray-50 dark:bg-gray-900 h-[calc(100vh-64px)]">
        {/* Set a fixed height for the main content area */}
        {/* Far Left Column (Conversations) - Collapsible */}
        <CollapsiblePanel
          title="Conversations"
          isVisible={isConversationListVisible}
          isPinned={isConvPanelPinned}
          onTogglePin={() => setIsConvPanelPinned(!isConvPanelPinned)}
          onToggleVisibility={toggleConversationList}
          position="left"
          width="300px"
          minWidth="250px"
          maxWidth="400px"
          backgroundColor="bg-gray-50 dark:bg-gray-900"
          initialWidth="300px"
          onResize={(width) => console.log('Conversations panel resized:', width)}
          showPartialWhenCollapsed={true}
          partialWidth="40px"
        >
          <ConversationList onLoadConversation={handleLoadConversation} />
        </CollapsiblePanel>

        {/* Left Column (Agent Discussion) */}
        <ResizableColumn
          initialWidth="30%"
          minWidth="300px"
          maxWidth="40%"
          className="border-r border-gray-200 dark:border-gray-700 h-full"
          backgroundColor="bg-gray-100 dark:bg-gray-850"
          title="Agent Discussion"
        >
          <div className="h-full p-5 overflow-y-auto">
            <AgentDiscussionPanel discussion={agentDiscussion} order={discussionOrder} />
          </div>
        </ResizableColumn>

        {/* Center Column (Final Answer & Prompt) */}
        <div className="flex-1 flex flex-col overflow-hidden bg-gray-100 dark:bg-gray-850 shadow-lg mx-2 rounded-lg h-full" style={{ maxWidth: '60%' }}>
          {/* Final Answer */}
          <div className="flex-1 p-6 overflow-y-auto flex flex-col items-center">
            <div className="w-full max-w-8xl mx-auto">
              <h2 className="text-xl font-semibold mb-4 text-center text-primary-dark dark:text-primary-light flex items-center justify-center">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-3a1 1 0 00-.867.5 1 1 0 11-1.731-1A3 3 0 0113 8a3.001 3.001 0 01-2 2.83V11a1 1 0 11-2 0v-1a1 1 0 011-1 1 1 0 100-2zm0 8a1 1 0 100-2 1 1 0 000 2z" clipRule="evenodd" />
                </svg>
                Final Answer
              </h2>
              <div className="bg-white dark:bg-gray-800 rounded-xl shadow-md border border-gray-200 dark:border-gray-700 p-4">
                <FinalAnswerPanel answer={finalAnswer} imagePath={finalAnswerImage} />
              </div>
            </div>
          </div>

          {/* Prompt Area */}
          <div className="p-6 border-t border-gray-200 dark:border-gray-700 flex-shrink-0 flex justify-center bg-gray-50 dark:bg-gray-900 rounded-b-lg">
            <div className="w-full max-w-8xl mx-auto">
              <PromptInputArea
                setAgentDiscussion={setAgentDiscussion}
                setDiscussionOrder={setDiscussionOrder}
                toggleConsole={toggleConsoleVisibility}
                clearChat={clearChat}
                currentConversationId={currentConversationId}
              />
            </div>
          </div>

          {/* Console Output Area */}
          {isConsoleVisible && (
            <div className="h-48 p-4 border-t border-gray-200 dark:border-gray-700 flex-shrink-0 overflow-y-auto bg-gray-100 dark:bg-gray-850">
              <div className="max-w-8xl mx-auto">
                <h3 className="text-md font-semibold mb-2 text-gray-600 dark:text-gray-400">Console Output</h3>
                <pre className="text-xs text-gray-700 dark:text-gray-300 whitespace-pre-wrap">
                  {consoleLogs.length > 0 ? consoleLogs.join('\n') : 'No logs yet...'}
                  <div ref={consoleEndRef} />
                </pre>
              </div>
            </div>
          )}
        </div>

        {/* Right Column (Configuration) - Collapsible */}
        <CollapsiblePanel
          title="Configuration"
          isVisible={isConfigPanelVisible}
          isPinned={isConfigPanelPinned}
          onTogglePin={() => setIsConfigPanelPinned(!isConfigPanelPinned)}
          onToggleVisibility={() => setIsConfigPanelVisible(!isConfigPanelVisible)}
          position="right"
          width="700px"
          minWidth="500px"
          maxWidth="900px"
          initialWidth="700px"
          onResize={(width) => console.log('Configuration panel resized:', width)}
          showPartialWhenCollapsed={true}
          partialWidth="50px"
          className="h-full"
        >
          <div className="mb-2 flex-shrink-0">
            <FeedbackDisplay />
          </div>
          <div className="flex-grow overflow-y-auto">
            <ConfigurationTabs
              providers={providers}
              providersLoading={providersLoading}
              providersError={providersError}
            />
          </div>
        </CollapsiblePanel>
      </main>
    </div>
  );
};

export default MainAppLayout;
