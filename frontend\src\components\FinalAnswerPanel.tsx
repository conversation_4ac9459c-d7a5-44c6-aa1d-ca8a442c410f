// src/components/FinalAnswerPanel.tsx

import React from 'react';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';          // Plugin for GitHub Flavored Markdown (tables, etc.)
import rehypeSanitize from 'rehype-sanitize'; // Plugin to sanitize HTML output for security
import rehypeH<PERSON>light from 'rehype-highlight'; // Plugin for code syntax highlighting
import { BACKEND_BASE_URL } from '../config'; // Import the base URL

// Import a highlight.js theme. Choose one that suits your application's design.
// You can find more themes here: https://highlightjs.org/static/demo/
import 'highlight.js/styles/github-dark.css'; // Example: Dark theme suitable for dark mode

interface FinalAnswerPanelProps {
  /** The final answer string, potentially containing Markdown */
  answer: string | null | undefined; // Allow null/undefined for clearer empty states
  /** Optional image path for displaying generated images */
  imagePath?: string | null;
}

/**
 * Renders the final answer, processing it as Markdown with GFM support,
 * syntax highlighting for code blocks, and HTML sanitization.
 * It displays a placeholder if no answer is provided.
 * Wrapped in React.memo for performance optimization.
 */
const FinalAnswerPanelComponent = ({ answer, imagePath }: FinalAnswerPanelProps) => {
  // Function to handle image download
  const handleDownloadImage = (imageUrl: string, e?: React.MouseEvent) => {
    if (e) {
      e.preventDefault();
      e.stopPropagation();
    }
    const link = document.createElement('a');
    link.href = imageUrl;
    link.download = imageUrl.split('/').pop() || 'generated-image.png';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  return (
    // Container with padding and vertical scroll if content overflows
    <div className="h-full p-6 overflow-y-auto bg-white dark:bg-gray-800 w-full rounded-xl">
      {imagePath ? (
        // Display the generated image if imagePath is provided
        <div className="flex flex-col items-center justify-center">
          <div className="relative group">
            <img
              src={`${BACKEND_BASE_URL}${imagePath}`}
              alt="Generated by AI"
              className="max-w-full rounded-lg shadow-md border border-gray-300 dark:border-gray-600"
            />
            <button
              onClick={(e) => handleDownloadImage(`${BACKEND_BASE_URL}${imagePath}`, e)}
              className="absolute bottom-2 right-2 bg-blue-600 hover:bg-blue-700 text-white p-2 rounded-full shadow-md opacity-80 hover:opacity-100 transition-opacity"
              title="Download Image"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" />
              </svg>
            </button>
          </div>
          {answer && (
            <div className="mt-4 w-full">
              <ReactMarkdown
                className="prose prose-base lg:prose-lg dark:prose-invert max-w-none prose-headings:text-primary-dark dark:prose-headings:text-primary-light prose-a:text-blue-600 dark:prose-a:text-blue-400"
                remarkPlugins={[remarkGfm]}
                rehypePlugins={[rehypeSanitize, rehypeHighlight]}
              >
                {answer}
              </ReactMarkdown>
            </div>
          )}
        </div>
      ) : answer ? (
        // Render answer using ReactMarkdown if 'answer' is truthy
        <ReactMarkdown
          // Apply Tailwind Typography styles for pleasant Markdown rendering
          className="prose prose-base lg:prose-lg dark:prose-invert max-w-none prose-headings:text-primary-dark dark:prose-headings:text-primary-light prose-a:text-blue-600 dark:prose-a:text-blue-400 prose-img:rounded-lg prose-img:shadow-md"
          // Plugins to process the Markdown/HTML
          remarkPlugins={[remarkGfm]} // Process Markdown features (GFM)
          rehypePlugins={[
            rehypeSanitize,   // Sanitize the resulting HTML
            rehypeHighlight   // Apply syntax highlighting to code blocks
            // Note: The order of rehype plugins can matter depending on configuration.
            // This order sanitizes first, then highlights.
          ]}
        >
          {answer}
        </ReactMarkdown>
      ) : (
        // Display a placeholder message if 'answer' is falsy (empty, null, undefined)
        <div className="flex flex-col items-center justify-center h-full py-12">
          <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12 text-gray-400 dark:text-gray-500 mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M8 10h.01M12 10h.01M16 10h.01M9 16H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-5l-5 5v-5z" />
          </svg>
          <p className="text-gray-500 dark:text-gray-400 italic text-center text-base">
            The final answer will appear here...
          </p>
        </div>
      )}
    </div>
  );
};

// Export the component wrapped in React.memo to prevent unnecessary re-renders
export default React.memo(FinalAnswerPanelComponent);