import { Request, Response } from 'express';
import ConfigurationProfile from '../models/ConfigurationProfile';
import { IUser } from '../models/User';
import fs from 'fs';
import path from 'path';

// @desc    Import a profile from a JSON file
// @route   POST /api/import-export/import
// @access  Private
export const importProfile = async (req: Request, res: Response): Promise<void> => {
  try {
    const userId = (req.user as IUser)?._id;

    if (!userId) {
      res.status(401).json({ message: 'Not authorized' });
      return;
    }

    // Get profile data from request body
    const { profile } = req.body;

    if (!profile || !profile.name || !profile.settings) {
      res.status(400).json({ message: 'Invalid profile data' });
      return;
    }

    // Check if a profile with this name already exists for this user
    const existingProfile = await ConfigurationProfile.findOne({ 
      name: profile.name, 
      userId: userId 
    });

    if (existingProfile) {
      res.status(400).json({ message: `Profile with name '${profile.name}' already exists` });
      return;
    }

    // Create a new profile
    const newProfile = new ConfigurationProfile({
      name: profile.name,
      settings: profile.settings,
      userId: userId
    });

    const savedProfile = await newProfile.save();
    res.status(201).json(savedProfile);
  } catch (error) {
    console.error('Error importing profile:', error);
    if (error instanceof Error) {
      res.status(500).json({ message: 'Server error importing profile', error: error.message });
    } else {
      res.status(500).json({ message: 'Unknown server error importing profile' });
    }
  }
};

// @desc    Export a profile to a JSON file
// @route   GET /api/import-export/export/:id
// @access  Private
export const exportProfile = async (req: Request, res: Response): Promise<void> => {
  try {
    const profileId = req.params.id;
    const userId = (req.user as IUser)?._id;

    if (!userId) {
      res.status(401).json({ message: 'Not authorized' });
      return;
    }

    // Find the profile
    const profile = await ConfigurationProfile.findOne({ 
      _id: profileId, 
      userId: userId 
    });

    if (!profile) {
      res.status(404).json({ message: 'Profile not found' });
      return;
    }

    // Return the profile data
    res.status(200).json({
      name: profile.name,
      settings: profile.settings
    });
  } catch (error) {
    console.error('Error exporting profile:', error);
    if (error instanceof Error) {
      res.status(500).json({ message: 'Server error exporting profile', error: error.message });
    } else {
      res.status(500).json({ message: 'Unknown server error exporting profile' });
    }
  }
};

// @desc    Get list of example profiles
// @route   GET /api/import-export/examples
// @access  Private
export const getExampleProfiles = async (req: Request, res: Response): Promise<void> => {
  try {
    const profilesDir = path.join(__dirname, '../../../profiles');
    const indexPath = path.join(profilesDir, 'index.json');
    
    // Check if index.json exists
    if (!fs.existsSync(indexPath)) {
      res.status(404).json({ message: 'Example profiles index not found' });
      return;
    }

    // Read and parse index.json
    const indexContent = fs.readFileSync(indexPath, 'utf8');
    const index = JSON.parse(indexContent);
    
    res.status(200).json(index);
  } catch (error) {
    console.error('Error getting example profiles:', error);
    if (error instanceof Error) {
      res.status(500).json({ message: 'Server error getting example profiles', error: error.message });
    } else {
      res.status(500).json({ message: 'Unknown server error getting example profiles' });
    }
  }
};

// @desc    Get a specific example profile
// @route   GET /api/import-export/examples/:filename
// @access  Private
export const getExampleProfile = async (req: Request, res: Response): Promise<void> => {
  try {
    const filename = req.params.filename;
    const profilesDir = path.join(__dirname, '../../../profiles');
    const profilePath = path.join(profilesDir, filename);
    
    // Check if the profile file exists
    if (!fs.existsSync(profilePath)) {
      res.status(404).json({ message: 'Example profile not found' });
      return;
    }

    // Read and parse the profile file
    const profileContent = fs.readFileSync(profilePath, 'utf8');
    const profile = JSON.parse(profileContent);
    
    res.status(200).json(profile);
  } catch (error) {
    console.error('Error getting example profile:', error);
    if (error instanceof Error) {
      res.status(500).json({ message: 'Server error getting example profile', error: error.message });
    } else {
      res.status(500).json({ message: 'Unknown server error getting example profile' });
    }
  }
};
