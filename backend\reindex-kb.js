// Script to reindex the knowledge base
const axios = require('axios');

async function reindexKnowledgeBase() {
  try {
    console.log('Reindexing knowledge base...');
    
    // First, update the RAG settings to use the correct model
    const updateSettingsResponse = await axios.post('http://localhost:3001/api/rag/update-settings', {
      embeddingModel: 'Xenova/all-MiniLM-L6-v2',
      chunkSize: 512,
      chunkOverlap: 50,
      chunkingStrategy: 'semantic',
      retrievalNResults: 10,
      retrievalThreshold: 0.1,
      useReranking: false,
      useQueryExpansion: false
    }, {
      headers: {
        'Content-Type': 'application/json'
      }
    });
    
    console.log('Updated RAG settings:', updateSettingsResponse.data);
    
    // Then reindex the knowledge base
    const reindexResponse = await axios.post('http://localhost:3001/api/rag/reindex', {}, {
      headers: {
        'Content-Type': 'application/json'
      }
    });
    
    console.log('Reindex response:', reindexResponse.data);
    console.log('Knowledge base reindexed successfully!');
  } catch (error) {
    console.error('Error reindexing knowledge base:', error.response?.data || error.message);
  }
}

reindexKnowledgeBase();
