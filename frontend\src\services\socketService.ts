import { io, Socket } from 'socket.io-client';

// Import the base URL from config
import { BACKEND_BASE_URL } from '../config';

// Define the server URL (use environment variable or default)
const SOCKET_SERVER_URL = import.meta.env.VITE_SOCKET_SERVER_URL || BACKEND_BASE_URL;

let socket: Socket | null = null;
let currentToken: string | null = null; // Store the token used for the current connection

// Modified connectSocket to accept an optional token
export const connectSocket = (token?: string | null): Socket => {
  // If trying to connect with the same token and socket exists, handle reconnection/return existing
  if (socket && token === currentToken) {
    if (socket.connected) {
      console.log('Socket already connected with the same token.');
      return socket;
    }
    // If disconnected, attempt to reconnect
    console.log('Socket exists but disconnected, attempting reconnect...');
    socket.connect();
    return socket;
  }

  // If a new token is provided or no socket exists, disconnect old one and create new
  if (socket) {
    console.log('Token changed or socket invalid, disconnecting old socket...');
    socket.disconnect();
    socket = null;
  }

  currentToken = token || null; // Store the new token (or null)

  console.log(`Attempting to connect to Socket.IO server at ${SOCKET_SERVER_URL} ${currentToken ? 'with token' : 'without token'}`);

  // Create a new socket connection with auth options if token exists
  socket = io(SOCKET_SERVER_URL, {
    reconnectionAttempts: 5,
    reconnectionDelay: 1000,
    transports: ['websocket'],
    // Add the token to the auth object for the handshake
    auth: currentToken ? { token: currentToken } : undefined,
    // autoConnect: false // Consider setting autoConnect to false if you want more control
  });

  // --- Connection event listeners (remain the same) ---
  socket.on('connect', () => {
    console.log(`Socket connected: ${socket?.id}`);
  });

  socket.on('disconnect', (reason) => {
    console.log(`Socket disconnected: ${reason}`);
    // Handle disconnection logic if needed (e.g., notify user)
    if (reason === 'io server disconnect') {
      // The server forcefully disconnected the socket; reconnection may not work
      console.warn('Server disconnected the socket.');
    }
    // else: the socket will automatically try to reconnect based on options
  });

  socket.on('connect_error', (error) => {
    console.error(`Socket connection error: ${error.message}`, error);
    // Handle connection errors (e.g., show error message to user)
  });

  return socket;
};

export const disconnectSocket = () => {
  if (socket) {
    console.log(`Disconnecting socket: ${socket.id}`);
    socket.disconnect();
    socket = null;
  }
  currentToken = null; // Clear the stored token on explicit disconnect
};

export const getSocket = (): Socket | null => {
  return socket;
};

// Example of emitting an event (will be used later)
// export const emitEvent = (eventName: string, data: any) => {
//   if (socket?.connected) {
//     socket.emit(eventName, data);
//   } else {
//     console.error('Socket not connected. Cannot emit event:', eventName);
//   }
// };

// Example of listening to an event (will be used later)
// export const listenToEvent = (eventName: string, callback: (...args: any[]) => void) => {
//   if (socket) {
//     socket.on(eventName, callback);
//     // Return a function to remove the listener
//     return () => socket?.off(eventName, callback);
//   } else {
//     console.error('Socket not initialized. Cannot listen to event:', eventName);
//     return () => {}; // Return an empty function
//   }
// };
