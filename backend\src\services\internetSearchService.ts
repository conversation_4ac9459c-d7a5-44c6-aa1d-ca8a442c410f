import axios, { AxiosInstance } from 'axios';
import * as cheerio from 'cheerio'; // Used for targeted element selection
import { JSD<PERSON> } from 'jsdom'; // For parsing HTML into a DOM structure
import { Readability } from '@mozilla/readability'; // For extracting main content
import { google } from 'googleapis';
import * as fs from 'fs/promises'; // Use promises version of fs
import * as path from 'path';
import * as os from 'os';
import * as crypto from 'crypto';
import { performance } from 'perf_hooks'; // For timing
import { SearchError } from '../utils/errors'; // <<< Import custom error

// --- Interfaces ---

// Specific type for module parameters
interface RetrievalModuleParams {
    num?: number; // Number of results for web search
    // Add other potential params here if needed
}

// Updated SearchResult interface to match Python's dataclass more closely
export interface SearchResult {
    content: string;
    url: string;
    relevance_score: number;
    timestamp: string; // ISO 8601 format
    metadata: Record<string, any>; // More flexible metadata
    is_real_time: boolean;
    source_type: 'web' | 'api' | 'cache'; // Origin of the result
    title?: string; // Keep optional title from original TS interface
    snippet?: string; // Keep optional snippet
    position?: number; // Keep optional position
    scrapedContent?: string | null; // Keep optional scrapedContent for now, might merge into 'content'
}

// --- Serper Interfaces ---
interface SerperOrganicResult {
  title: string;
  link: string;
  snippet: string;
  position: number;
}
interface SerperKnowledgeGraph {
    title?: string;
    description?: string;
    link?: string;
    // Add other KG fields if needed
}
interface SerperAnswerBox {
    answer?: string;
    link?: string;
    // Add other answer box fields if needed
}
interface SerperResponse {
  searchParameters: { q: string; type: string; engine: string };
  organic?: SerperOrganicResult[];
  knowledge_graph?: SerperKnowledgeGraph;
  answer_box?: SerperAnswerBox;
  // Add other potential fields
}

// --- Google Custom Search Interfaces ---
interface GoogleCustomSearchResultItem {
    title?: string | null;
    link?: string | null;
    snippet?: string | null;
    pagemap?: {
        metatags?: Array<Record<string, any>> | null;
    } | null;
}
interface GoogleCustomSearchResponse {
    items?: GoogleCustomSearchResultItem[] | null;
}

// --- Cache Data Structure ---
interface CacheData {
    query: string;
    timestamp: number; // Unix timestamp (seconds)
    results: SearchResult[];
}

// --- DuckDuckGo Search Interfaces ---
interface DuckDuckGoResult {
    title: string;
    href: string;
    body: string;
}

interface DuckDuckGoResponse {
    results: DuckDuckGoResult[];
}

// --- Brave Search Interfaces ---
interface BraveSearchResult {
    title: string;
    url: string;
    description: string;
}

interface BraveSearchResponse {
    web: {
        results: BraveSearchResult[];
    };
}

// --- Configuration Interface ---
interface SearchConfig {
    serperApiKey?: string;
    googleApiKey?: string;
    googleCxId?: string;
    duckduckgoApiKey?: string;
    braveApiKey?: string;
    polygonApiKey?: string; // For stocks
    openWeatherApiKey?: string; // For weather
    // Add other API keys as needed (CoinGecko doesn't strictly require one for public endpoints)
}

// --- Real-time API Endpoints ---
const REAL_TIME_APIS = {
    crypto: 'https://api.coingecko.com/api/v3',
    // exchange_rates: 'https://open.er-api.com/v6', // Example
    stocks: 'https://api.polygon.io/v2', // Requires API key
    // timezone: 'http://worldtimeapi.org/api', // Example
    // holidays: 'https://date.nager.at/api/v3', // Example
    // ip_info: 'https://ipapi.co', // Example
    // countries: 'https://restcountries.com/v3.1', // Example
    weather: 'https://api.openweathermap.org/data/2.5', // Requires API key
    // air_quality: 'https://api.waqi.info/feed' // Example, requires API key
};

// --- Constants ---
const CACHE_DIR_NAME = 'search_results_cache';
const DEFAULT_CACHE_MAX_AGE_SECONDS = 3600; // 1 hour
const TIME_SENSITIVE_CACHE_MAX_AGE_SECONDS = 60; // 1 minute
const SCRAPE_TIMEOUT_MS = 10000; // 10 seconds
const SCRAPE_RETRIES = 3;
const SCRAPE_RETRY_DELAY_MS = 2000; // 2 seconds
const EXCLUDED_SCRAPE_DOMAINS = new Set([
    'youtube.com', 'facebook.com', 'instagram.com', 'twitter.com', 'x.com',
    'tiktok.com', 'pinterest.com', 'reddit.com', 'linkedin.com'
]);


export class EnhancedSearchManager {
    private config: SearchConfig;
    private cacheDir: string;
    private axiosInstance: AxiosInstance;
    private retrievalModules: Array<{ name: string; params?: RetrievalModuleParams }> = []; // <<< Use specific type
    private defaultIsCancelled: () => boolean;

    constructor(config?: SearchConfig) {
        this.config = config || this.loadConfigFromEnv();
        this.cacheDir = path.join(os.tmpdir(), CACHE_DIR_NAME);
        this.axiosInstance = this.setupAxiosInstance();
        this.defaultIsCancelled = () => false; // Default function
        this.setupRetrievalModules();
        this.ensureCacheDirExists(); // Ensure cache dir exists on instantiation
    }

    private loadConfigFromEnv(): SearchConfig {
        // Load API keys securely from environment variables
        return {
            serperApiKey: process.env.SERPER_API_KEY,
            googleApiKey: process.env.GOOGLE_API_KEY,
            googleCxId: process.env.GOOGLE_SEARCH_ENGINE_ID,
            duckduckgoApiKey: process.env.DUCKDUCKGO_API_KEY,
            braveApiKey: process.env.BRAVE_API_KEY,
            polygonApiKey: process.env.POLYGON_API_KEY,
            openWeatherApiKey: process.env.OPENWEATHER_API_KEY,
        };
    }

    private setupAxiosInstance(): AxiosInstance {
        const instance = axios.create({
            timeout: 15000, // General timeout for API calls
            headers: {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36 ClineSearchBot/1.0', // More specific user agent
                'Accept': 'application/json, text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
                'Accept-Language': 'en-US,en;q=0.5',
                'DNT': '1', // Do Not Track
            }
        });
        return instance;
    }

    private setupRetrievalModules(): void {
        this.retrievalModules = [];
        if (this.config.serperApiKey) {
            this.retrievalModules.push({
                name: 'serper',
                params: { num: 5 } // Fetch slightly more initially
            });
        }
        if (this.config.googleApiKey && this.config.googleCxId) {
            this.retrievalModules.push({
                name: 'google',
                params: { num: 5 } // Fetch slightly more initially
            });
        }
        if (this.config.duckduckgoApiKey) {
            this.retrievalModules.push({
                name: 'duckduckgo',
                params: { num: 5 }
            });
        }
        if (this.config.braveApiKey) {
            this.retrievalModules.push({
                name: 'brave',
                params: { num: 5 }
            });
        }
        // Add other potential modules here if needed
    }

    private async ensureCacheDirExists(): Promise<void> {
        try {
            await fs.mkdir(this.cacheDir, { recursive: true });
        } catch (error: any) {
            // Log error but don't throw, as caching is optional
            console.error(`Failed to create cache directory ${this.cacheDir}:`, error);
            this.cacheDir = ''; // Disable caching if dir creation fails
        }
    }

    private getCachePath(query: string): string {
        if (!this.cacheDir) return ''; // Return empty if cache dir failed
        const hash = crypto.createHash('md5').update(query.toLowerCase()).digest('hex');
        return path.join(this.cacheDir, `${hash}.json`);
    }

    private async cacheResults(query: string, results: SearchResult[]): Promise<void> {
        const cachePath = this.getCachePath(query);
        if (!cachePath) return; // Don't cache if dir failed

        try {
            const cacheData: CacheData = {
                query: query,
                timestamp: Math.floor(Date.now() / 1000), // Store Unix timestamp
                results: results
            };
            await fs.writeFile(cachePath, JSON.stringify(cacheData, null, 2)); // Pretty print for readability
        } catch (error: any) {
            // Log error but don't throw, caching failure is not critical
            console.error(`Error caching results for query "${query}":`, error);
        }
    }

    private async getCachedResults(query: string, maxAgeSeconds: number): Promise<SearchResult[] | null> {
        const cachePath = this.getCachePath(query);
        if (!cachePath) return null;

        try {
            await fs.access(cachePath); // Check if file exists
            const fileContent = await fs.readFile(cachePath, 'utf-8');
            const cacheData: CacheData = JSON.parse(fileContent);

            const now = Math.floor(Date.now() / 1000);
            if (now - cacheData.timestamp > maxAgeSeconds) {
                console.log(`Cache expired for query: "${query}"`);
                // Optionally delete expired cache file
                // await fs.unlink(cachePath).catch(err => console.error(`Failed to delete expired cache: ${err}`));
                return null;
            }

             // For time-sensitive queries, validate result timestamps from cache
            if (this.isTimeSensitiveQuery(query)) {
                const validResults = cacheData.results.filter(result => {
                    try {
                        // Check if timestamp is valid ISO and within maxAge
                        const resultTimestamp = new Date(result.timestamp).getTime() / 1000;
                        return !isNaN(resultTimestamp) && (now - resultTimestamp <= maxAgeSeconds);
                    } catch (e) {
                        return false; // Invalid timestamp format
                    }
                });
                if (validResults.length === 0) {
                     console.log(`All cached results are too old for time-sensitive query: "${query}"`);
                     return null;
                }
                 console.log(`Returning ${validResults.length} validated time-sensitive results from cache.`);
                return validResults.map(r => ({ ...r, source_type: 'cache' }));
            }


            // Add source_type to cached results
            return cacheData.results.map(r => ({ ...r, source_type: 'cache' }));
        } catch (error: any) {
            if (error.code !== 'ENOENT') { // Ignore file not found errors
                // Log error but don't throw, treat as cache miss
                console.error(`Error reading cache for query "${query}":`, error);
            }
            return null; // Treat error as cache miss
        }
    }

    private isTimeSensitiveQuery(query: string): boolean {
        const lowerQuery = query.toLowerCase();
        const timeSensitiveKeywords: Set<string> = new Set([
            'price', 'current', 'today', 'now', 'latest', 'live',
            'weather', 'temperature', 'stock', 'crypto', 'bitcoin',
            'exchange rate', 'score', 'game', 'match', 'time', 'date',
            'how long ago', 'when was', 'breaking news'
        ]);
        return Array.from(timeSensitiveKeywords).some(keyword => lowerQuery.includes(keyword));
    }

    // --- Real-time Data Fetching ---

    private _extractSearchTerms(query: string): { type: 'market' | 'weather' | null; symbol?: string; location?: string } {
        const terms: { type: 'market' | 'weather' | null; symbol?: string; location?: string } = {
            type: null,
        };
        const queryLower = query.toLowerCase();

        // Simple keyword matching and extraction logic (can be expanded)
        const marketKeywords = ['stock', 'price', 'market', 'crypto', 'bitcoin', 'ethereum', 'btc', 'eth'];
        const weatherKeywords = ['weather', 'temperature', 'forecast'];

        if (marketKeywords.some(k => queryLower.includes(k))) {
            terms.type = 'market';
            // Try to extract a potential stock symbol (e.g., AAPL, GOOG)
            const symbolMatch = query.match(/\b([A-Z]{1,5})\b/); // Match 1-5 uppercase letters as a word
            if (symbolMatch) {
                terms.symbol = symbolMatch[1];
            } else if (queryLower.includes('bitcoin') || queryLower.includes('btc')) {
                terms.symbol = 'bitcoin'; // Specific handling for common crypto
            } else if (queryLower.includes('ethereum') || queryLower.includes('eth')) {
                terms.symbol = 'ethereum';
            }
            // Add more specific symbol extraction if needed
        } else if (weatherKeywords.some(k => queryLower.includes(k))) {
            terms.type = 'weather';
            // Try to extract location (text after "in", "at", "for")
            const locationMatch = queryLower.match(/(?:in|at|for)\s+(.+)/);
            if (locationMatch && locationMatch[1]) {
                // Basic cleanup: remove trailing punctuation or "today", "now"
                terms.location = locationMatch[1].replace(/(?:\s+(?:today|now|currently))?\s*[?.!]*$/, '').trim();
            } else {
                 // If no preposition, assume the last part of the query might be the location
                 const words = queryLower.split(' ');
                 const lastWord = words[words.length - 1];
                 if (!weatherKeywords.includes(lastWord)) {
                     terms.location = query.split(' ').slice(words.findIndex(w => weatherKeywords.includes(w)) + 1).join(' ').trim();
                 }
            }
        }

        return terms;
    }


    private async _getRealTimeData(query: string, isCancelled: () => boolean): Promise<SearchResult[] | null> {
        if (isCancelled()) { console.log("[Real Time] Cancelled before fetching."); return null; }
        console.log(`Attempting to fetch real-time data for query: "${query}"`);
        const searchTerms = this._extractSearchTerms(query);

        try {
            if (isCancelled()) return null;
            if (searchTerms.type === 'market' && searchTerms.symbol) {
                return await this._getMarketData(searchTerms.symbol, isCancelled);
            } else if (searchTerms.type === 'weather' && searchTerms.location) {
                return await this._getWeatherData(searchTerms.location, isCancelled);
            }
            return null;
        } catch (error: any) {
            // Log the error but return null, let the main search continue
            console.error(`Error getting real-time data for "${query}":`, error.message);
            return null; // Don't throw here, allow web search fallback
        }
    }

    private async _getMarketData(symbol: string, isCancelled: () => boolean): Promise<SearchResult[] | null> {
        if (isCancelled()) { console.log("[Market Data] Cancelled before fetching."); return null; }
        const nowTimestamp = new Date().toISOString();
        const symbolLower = symbol.toLowerCase();

        // --- Crypto (CoinGecko - No API Key needed for simple price) ---
        if (['bitcoin', 'btc', 'ethereum', 'eth'].includes(symbolLower)) {
            const cryptoId = symbolLower === 'btc' ? 'bitcoin' : symbolLower === 'eth' ? 'ethereum' : symbolLower;
            try {
                const url = `${REAL_TIME_APIS.crypto}/simple/price`;
                const params = {
                    ids: cryptoId,
                    vs_currencies: 'usd',
                    include_24hr_change: 'true',
                    include_market_cap: 'true'
                };
                console.log(`Fetching crypto data for ${cryptoId} from CoinGecko`);
                if (isCancelled()) return null;
                const abortController = new AbortController();
                const timeoutId = setTimeout(() => { if (isCancelled()) abortController.abort(); }, 100);
                const response = await this.axiosInstance.get(url, { params, signal: abortController.signal });
                clearTimeout(timeoutId);
                const data = response.data[cryptoId];

                if (!data) return null;

                const price = data.usd ?? 0;
                const change = data.usd_24h_change ?? 0;
                const marketCap = data.usd_market_cap ?? 0;

                const formattedContent = `${symbol.toUpperCase()} Price: $${price.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 })}\n`
                                       + `24h Change: ${change.toFixed(2)}%\n`
                                       + `Market Cap: $${marketCap.toLocaleString(undefined, { maximumFractionDigits: 0 })}`;

                return [{
                    content: formattedContent,
                    url: `${REAL_TIME_APIS.crypto}/coins/${cryptoId}`, // Link to coin page
                    relevance_score: 1.0,
                    timestamp: nowTimestamp,
                    metadata: { type: 'market', symbol: symbol.toUpperCase(), source: 'CoinGecko' },
                    is_real_time: true,
                    source_type: 'api',
                    title: `${symbol.toUpperCase()} Market Data`,
                }];
            } catch (error: any) {
                if (axios.isCancel(error)) { console.log(`[Market Data - CoinGecko] Request cancelled for ${symbol}.`); return null; } // Don't throw cancellation
                console.error(`CoinGecko API error for ${symbol}:`, error.response?.data || error.message);
                return null; // Don't throw, allow fallback
            }
        }

        // --- Stocks (Polygon.io - Requires API Key) ---
        else if (this.config.polygonApiKey) {
             // Use Aggregates endpoint for previous day's close, high, low, volume
             // Or Tickers endpoint for more details - choosing Aggregates for simplicity like Python example
            try {
                // Get previous day's date in YYYY-MM-DD format
                const yesterday = new Date();
                yesterday.setDate(yesterday.getDate() - 1);
                // Adjust for weekends - if yesterday was Sun, get Fri. If Sat, get Fri.
                if (yesterday.getDay() === 0) yesterday.setDate(yesterday.getDate() - 2);
                else if (yesterday.getDay() === 6) yesterday.setDate(yesterday.getDate() - 1);
                const dateStr = yesterday.toISOString().split('T')[0];

                 const url = `${REAL_TIME_APIS.stocks}/v2/aggs/ticker/${symbol.toUpperCase()}/prev`; // Previous day OHLCV
                  const params = { apiKey: this.config.polygonApiKey };
                  console.log(`Fetching stock data for ${symbol} from Polygon.io (Previous Day)`);
                  if (isCancelled()) return null;
                  const abortControllerPrev = new AbortController();
                  const timeoutIdPrev = setTimeout(() => { if (isCancelled()) abortControllerPrev.abort(); }, 100);
                  const response = await this.axiosInstance.get(url, { params, signal: abortControllerPrev.signal });
                  clearTimeout(timeoutIdPrev);
                  const data = response.data;

                 if (!data || data.status !== 'OK' || !data.results || data.results.length === 0) {
                     console.warn(`No previous day stock data found for ${symbol} on Polygon.io`);
                      // Fallback: Try to get latest trade (might be delayed depending on subscription)
                      try {
                          const lastTradeUrl = `${REAL_TIME_APIS.stocks}/v2/last/trade/${symbol.toUpperCase()}`;
                          console.log(`Fetching last trade data for ${symbol} from Polygon.io`);
                          if (isCancelled()) return null;
                          const abortControllerLast = new AbortController();
                          const timeoutIdLast = setTimeout(() => { if (isCancelled()) abortControllerLast.abort(); }, 100);
                          const lastTradeResponse = await this.axiosInstance.get(lastTradeUrl, { params, signal: abortControllerLast.signal });
                          clearTimeout(timeoutIdLast);
                          const lastTradeData = lastTradeResponse.data.results;
                          if (lastTradeData?.p) {
                               const lastPrice = lastTradeData.p;
                              const lastTimestamp = lastTradeData.t ? new Date(lastTradeData.t).toISOString() : nowTimestamp;
                              const formattedContent = `${symbol.toUpperCase()} Last Trade: $${lastPrice.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 })}\n`
                                                     + `Timestamp: ${lastTimestamp}`;
                              return [{
                                  content: formattedContent,
                                  url: `https://polygon.io/quote/${symbol.toUpperCase()}`,
                                  relevance_score: 0.95, // Slightly lower than full daily data
                                  timestamp: lastTimestamp,
                                  metadata: { type: 'market', symbol: symbol.toUpperCase(), source: 'Polygon.io (Last Trade)' },
                                  is_real_time: true,
                                  source_type: 'api',
                                  title: `${symbol.toUpperCase()} Last Trade Data`,
                              }];
                          }
                      } catch (lastTradeError: any) {
                          if (axios.isCancel(lastTradeError)) { console.log(`[Market Data - Polygon Last Trade] Request cancelled for ${symbol}.`); return null; } // Don't throw cancellation
                          console.warn(`Failed to get last trade data for ${symbol}: ${lastTradeError.message}`);
                          // Don't throw, just log warning
                      }
                      return null; // Return null if previous day and last trade fail
                  }

                 const result = data.results[0];
                 const closePrice = result.c ?? 0;
                 const highPrice = result.h ?? 0;
                 const lowPrice = result.l ?? 0;
                 const volume = result.v ?? 0;

                 const formattedContent = `${symbol.toUpperCase()} (Prev. Day ${dateStr}):\n`
                                        + `Close: $${closePrice.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 })}\n`
                                        + `High: $${highPrice.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 })}\n`
                                        + `Low: $${lowPrice.toLocaleString(undefined, { minimumFractionDigits: 2, maximumFractionDigits: 2 })}\n`
                                        + `Volume: ${volume.toLocaleString(undefined, { maximumFractionDigits: 0 })}`;

                 return [{
                     content: formattedContent,
                     url: `https://polygon.io/quote/${symbol.toUpperCase()}`, // Link to Polygon quote page
                     relevance_score: 1.0,
                     timestamp: new Date(result.t).toISOString(), // Timestamp from API result
                     metadata: { type: 'market', symbol: symbol.toUpperCase(), source: 'Polygon.io (Previous Day)' },
                     is_real_time: true, // Considered real-time in context
                     source_type: 'api',
                  title: `${symbol.toUpperCase()} Previous Day Market Data`,
              }];
            } catch (error: any) {
                  if (axios.isCancel(error)) { console.log(`[Market Data - Polygon Prev Day] Request cancelled for ${symbol}.`); return null; } // Don't throw cancellation
                  console.error(`Polygon.io API error for ${symbol}:`, error.response?.data || error.message);
                  return null; // Don't throw, allow fallback
            }
        } else if (!this.config.polygonApiKey && !['bitcoin', 'btc', 'ethereum', 'eth'].includes(symbolLower)) {
             console.warn(`Polygon API key not configured. Cannot fetch stock data for ${symbol}.`);
             return null;
        }

        return null; // No matching market type found
    }

    private async _getWeatherData(location: string, isCancelled: () => boolean): Promise<SearchResult[] | null> {
        if (isCancelled()) { console.log("[Weather Data] Cancelled before fetching."); return null; }
        if (!this.config.openWeatherApiKey) {
            console.warn("OpenWeatherMap API key not configured. Cannot fetch weather data.");
            return null; // Don't throw if key is missing, just skip
        }
        const nowTimestamp = new Date().toISOString();
        try {
            const url = `${REAL_TIME_APIS.weather}/weather`;
            const params = {
                q: location,
                appid: this.config.openWeatherApiKey,
                units: 'metric' // Or 'imperial' for Fahrenheit
            };
            console.log(`Fetching weather data for ${location} from OpenWeatherMap`);
            if (isCancelled()) return null;
            const abortController = new AbortController();
            const timeoutId = setTimeout(() => { if (isCancelled()) abortController.abort(); }, 100);
            const response = await this.axiosInstance.get(url, { params, signal: abortController.signal });
            clearTimeout(timeoutId);
            const data = response.data;

            if (!data || !data.main || !data.weather || data.weather.length === 0) return null;

            const temp = data.main.temp;
            const feelsLike = data.main.feels_like;
            const description = data.weather[0].description;
            const humidity = data.main.humidity;
            const windSpeed = data.wind.speed; // meters/sec

            const formattedContent = `Weather in ${data.name}, ${data.sys.country}:\n`
                                   + `Conditions: ${description}\n`
                                   + `Temperature: ${temp}°C (Feels like ${feelsLike}°C)\n`
                                   + `Humidity: ${humidity}%\n`
                                   + `Wind: ${windSpeed} m/s`;

            return [{
                content: formattedContent,
                url: `https://openweathermap.org/city/${data.id}`, // Link to OpenWeatherMap city page
                relevance_score: 1.0,
                timestamp: nowTimestamp, // Use current time
                metadata: { type: 'weather', location: data.name, source: 'OpenWeatherMap' },
                is_real_time: true,
                source_type: 'api',
                title: `Weather in ${data.name}`,
            }];
        } catch (error: any) {
            if (axios.isCancel(error)) { console.log(`[Weather Data] Request cancelled for ${location}.`); return null; } // Don't throw cancellation
            console.error(`OpenWeatherMap API error for ${location}:`, error.response?.data || error.message);
            return null; // Don't throw, allow fallback
        }
    }

    // --- End Real-time Data Fetching ---


    // --- Web Search Implementation ---
    private async _performWebSearch(query: string, isCancelled: () => boolean): Promise<SearchResult[]> {
        if (isCancelled()) { console.log("[Web Search] Cancelled before starting."); return []; }
        console.log(`Performing concurrent web search for query: "${query}"`);
        const searchPromises: Promise<SearchResult[]>[] = [];

        for (const module of this.retrievalModules) {
            if (isCancelled()) break; // Stop adding promises if cancelled
            if (module.name === 'serper' && this.config.serperApiKey) {
                searchPromises.push(this._searchSerper(query, module, isCancelled));
            } else if (module.name === 'google' && this.config.googleApiKey && this.config.googleCxId) {
                searchPromises.push(this._searchGoogle(query, module, isCancelled));
            } else if (module.name === 'duckduckgo' && this.config.duckduckgoApiKey) {
                searchPromises.push(this._searchDuckDuckGo(query, module, isCancelled));
            } else if (module.name === 'brave' && this.config.braveApiKey) {
                searchPromises.push(this._searchBrave(query, module, isCancelled));
            }
        }

        if (searchPromises.length === 0) {
            console.warn("No web search modules configured or API keys available.");
            return []; // Return empty, not an error
        }

        const resultsSettled = await Promise.allSettled(searchPromises);
        const allResults: SearchResult[] = [];

        resultsSettled.forEach((result, index) => {
            const moduleName = this.retrievalModules[index]?.name || 'unknown';
            if (result.status === 'fulfilled') {
                console.log(`Received ${result.value.length} results from ${moduleName}`);
                allResults.push(...result.value);
            } else {
                console.error(`Error searching with ${moduleName}:`, result.reason);
            }
        });

        console.log(`Total raw results from web search: ${allResults.length}`);
        return allResults;
    }

    private async _searchSerper(query: string, module: { name: string; params?: RetrievalModuleParams }, isCancelled: () => boolean): Promise<SearchResult[]> { // <<< Use specific type
        if (isCancelled()) { console.log("[Serper] Cancelled before request."); return []; }
        if (!this.config.serperApiKey) return [];
        console.log(`Searching Serper for: "${query}"`);
        const abortController = new AbortController();
        const timeoutId = setTimeout(() => { if (isCancelled()) abortController.abort(); }, 100);
        try {
            const headers = {
                'X-API-KEY': this.config.serperApiKey,
                'Content-Type': 'application/json'
            };
            const payload = {
                q: query,
                num: module.params?.num || 5,
                autocorrect: true,
                gl: 'us', // Geo-location (United States)
                hl: 'en'  // Host language (English)
            };
            if (isCancelled()) { clearTimeout(timeoutId); return []; }
            const response = await this.axiosInstance.post<SerperResponse>(
                'https://google.serper.dev/search',
                payload,
                { headers, signal: abortController.signal }
            );
            clearTimeout(timeoutId); // Clear timeout if request succeeds

            const data = response.data;
            const results: SearchResult[] = [];
            const nowTimestamp = new Date().toISOString();

            // Process knowledge graph
            if (data.knowledge_graph?.title && data.knowledge_graph?.description) {
                results.push({
                    content: `${data.knowledge_graph.title}\n\n${data.knowledge_graph.description}`,
                    url: data.knowledge_graph.link || '',
                    relevance_score: 0.9, // High relevance for KG
                    timestamp: nowTimestamp,
                    metadata: { type: 'knowledge_graph', source: 'Serper' },
                    is_real_time: true, // KG often contains up-to-date info
                    source_type: 'api',
                    title: data.knowledge_graph.title,
                });
            }

             // Process answer box
            if (data.answer_box?.answer) {
                results.push({
                    content: data.answer_box.answer,
                    url: data.answer_box.link || '',
                    relevance_score: 0.95, // Very high relevance for direct answers
                    timestamp: nowTimestamp,
                    metadata: { type: 'answer_box', source: 'Serper' },
                    is_real_time: true, // Answers are usually current
                    source_type: 'api',
                    title: `Answer for "${query}"`, // Generic title for answer box
                });
            }

            // Process organic results with scraping
            const organicResults = data.organic || [];
            const scrapePromises = organicResults.map(async (item): Promise<SearchResult | null> => {
                if (isCancelled()) return null; // Check before starting scrape
                const url = item.link;
                if (!url || !this._isValidUrl(url)) return null;

                const scrapedContent = await this._extractContent(url, isCancelled); // Pass cancellation down
                if (!scrapedContent) return null; // Skip if scraping fails or returns nothing (or was cancelled)

                return {
                    content: scrapedContent, // Use scraped content
                    url: url,
                    relevance_score: 0.8, // Base score for organic results
                    timestamp: nowTimestamp, // Use current time as fetch time
                    metadata: { source: 'Serper', original_snippet: item.snippet },
                    is_real_time: false,
                    source_type: 'web',
                    title: item.title,
                    snippet: item.snippet,
                    position: item.position,
                    scrapedContent: scrapedContent // Keep for potential separate display if needed
                };
            });

            const scrapedOrganicResults = (await Promise.all(scrapePromises)).filter((r): r is SearchResult => r !== null);
            results.push(...scrapedOrganicResults);

            return results;

        } catch (error: any) {
            clearTimeout(timeoutId); // Clear timeout on error
            if (axios.isCancel(error)) {
                console.log(`[Serper] Request cancelled for query "${query}".`);
                return []; // Return empty on cancellation
            }
            // Log error but don't throw, allow other modules to potentially succeed
            console.error('Serper API search failed:', error.response?.data || error.message);
            return [];
        }
    }

    private async _searchDuckDuckGo(query: string, module: { name: string; params?: RetrievalModuleParams }, isCancelled: () => boolean): Promise<SearchResult[]> {
        if (isCancelled()) { console.log("[DuckDuckGo] Cancelled before request."); return []; }
        if (!this.config.duckduckgoApiKey) {
            console.warn("DuckDuckGo API key missing.");
            return []; // Don't throw if key missing
        }
        console.log(`Searching DuckDuckGo for: "${query}"`);
        const abortController = new AbortController();
        const timeoutId = setTimeout(() => { if (isCancelled()) abortController.abort(); }, 100);
        try {
            const headers = {
                'Authorization': `Bearer ${this.config.duckduckgoApiKey}`,
                'Content-Type': 'application/json'
            };
            if (isCancelled()) { clearTimeout(timeoutId); return []; }
            const response = await this.axiosInstance.get<DuckDuckGoResponse>(
                `https://api.duckduckgo.com/?q=${encodeURIComponent(query)}&format=json&t=MAIAChat`,
                { headers, signal: abortController.signal }
            );
            clearTimeout(timeoutId); // Clear timeout if request succeeds

            const data = response.data;
            const results: SearchResult[] = [];
            const nowTimestamp = new Date().toISOString();

            // Process results
            if (data.results && Array.isArray(data.results)) {
                const scrapePromises = data.results.map(async (item, index): Promise<SearchResult | null> => {
                    if (isCancelled()) return null; // Check before starting scrape
                    const url = item.href;
                    if (!url || !this._isValidUrl(url)) return null;

                    const scrapedContent = await this._extractContent(url, isCancelled); // Pass cancellation down
                    if (!scrapedContent) return null; // Skip if scraping fails or returns nothing (or was cancelled)

                    return {
                        content: scrapedContent, // Use scraped content
                        url: url,
                        relevance_score: 0.8, // Base score for results
                        timestamp: nowTimestamp, // Use current time as fetch time
                        metadata: { source: 'DuckDuckGo', original_snippet: item.body },
                        is_real_time: false,
                        source_type: 'web',
                        title: item.title,
                        snippet: item.body,
                        position: index + 1,
                        scrapedContent: scrapedContent
                    };
                });

                const scrapedResults = (await Promise.all(scrapePromises)).filter((r): r is SearchResult => r !== null);
                results.push(...scrapedResults);
            }

            return results;

        } catch (error: any) {
            clearTimeout(timeoutId); // Clear timeout on error
            if (axios.isCancel(error)) {
                console.log(`[DuckDuckGo] Request cancelled for query "${query}".`);
                return []; // Return empty on cancellation
            }
            // Log error but don't throw, allow other modules to potentially succeed
            console.error('DuckDuckGo API search failed:', error.response?.data || error.message);
            return [];
        }
    }

    private async _searchBrave(query: string, module: { name: string; params?: RetrievalModuleParams }, isCancelled: () => boolean): Promise<SearchResult[]> {
        if (isCancelled()) { console.log("[Brave] Cancelled before request."); return []; }
        if (!this.config.braveApiKey) {
            console.warn("Brave Search API key missing.");
            return []; // Don't throw if key missing
        }
        console.log(`Searching Brave for: "${query}"`);
        const abortController = new AbortController();
        const timeoutId = setTimeout(() => { if (isCancelled()) abortController.abort(); }, 100);
        try {
            const headers = {
                'X-Subscription-Token': this.config.braveApiKey,
                'Accept': 'application/json'
            };
            if (isCancelled()) { clearTimeout(timeoutId); return []; }
            const response = await this.axiosInstance.get<BraveSearchResponse>(
                `https://api.search.brave.com/res/v1/web/search?q=${encodeURIComponent(query)}&count=${module.params?.num || 5}`,
                { headers, signal: abortController.signal }
            );
            clearTimeout(timeoutId); // Clear timeout if request succeeds

            const data = response.data;
            const results: SearchResult[] = [];
            const nowTimestamp = new Date().toISOString();

            // Process web results
            if (data.web && data.web.results && Array.isArray(data.web.results)) {
                const scrapePromises = data.web.results.map(async (item, index): Promise<SearchResult | null> => {
                    if (isCancelled()) return null; // Check before starting scrape
                    const url = item.url;
                    if (!url || !this._isValidUrl(url)) return null;

                    const scrapedContent = await this._extractContent(url, isCancelled); // Pass cancellation down
                    if (!scrapedContent) return null; // Skip if scraping fails or returns nothing (or was cancelled)

                    return {
                        content: scrapedContent, // Use scraped content
                        url: url,
                        relevance_score: 0.85, // Base score for results
                        timestamp: nowTimestamp, // Use current time as fetch time
                        metadata: { source: 'Brave', original_snippet: item.description },
                        is_real_time: false,
                        source_type: 'web',
                        title: item.title,
                        snippet: item.description,
                        position: index + 1,
                        scrapedContent: scrapedContent
                    };
                });

                const scrapedResults = (await Promise.all(scrapePromises)).filter((r): r is SearchResult => r !== null);
                results.push(...scrapedResults);
            }

            return results;

        } catch (error: any) {
            clearTimeout(timeoutId); // Clear timeout on error
            if (axios.isCancel(error)) {
                console.log(`[Brave] Request cancelled for query "${query}".`);
                return []; // Return empty on cancellation
            }
            // Log error but don't throw, allow other modules to potentially succeed
            console.error('Brave API search failed:', error.response?.data || error.message);
            return [];
        }
    }

    private async _searchGoogle(query: string, module: { name: string; params?: RetrievalModuleParams }, isCancelled: () => boolean): Promise<SearchResult[]> { // <<< Use specific type
         if (isCancelled()) { console.log("[Google] Cancelled before request."); return []; }
         if (!this.config.googleApiKey || !this.config.googleCxId) {
             console.warn("Google Custom Search API key or CX ID missing.");
             return []; // Don't throw if keys missing
         }
         console.log(`Searching Google Custom Search for: "${query}"`);
         const customsearch = google.customsearch('v1');
         try {
             // Check cancellation right before the API call
             if (isCancelled()) return [];
             const res = await customsearch.cse.list({
                 auth: this.config.googleApiKey,
                 cx: this.config.googleCxId,
                 q: query,
                 num: module.params?.num || 5,
                 gl: 'us', // Geo-location
                 lr: 'lang_en' // Language restriction
             });
             // Check cancellation immediately after the API call returns (before processing/scraping)
             if (isCancelled()) return [];

             const googleItems = res.data.items || [];
             const nowTimestamp = new Date().toISOString();

             const resultsPromises = googleItems.map(async (item, index): Promise<SearchResult | null> => {
                 if (isCancelled()) return null; // Check before starting scrape
                 const url = item.link;
                 if (!url || !item.title || !item.snippet || !this._isValidUrl(url)) return null;

                 const scrapedContent = await this._extractContent(url, isCancelled); // Pass cancellation down
                 if (!scrapedContent) return null; // Skip if scraping fails or returns nothing (or was cancelled)

                 return {
                     content: scrapedContent,
                     url: url,
                     relevance_score: 0.85, // Slightly higher base score for Google?
                     timestamp: nowTimestamp,
                     metadata: { source: 'Google', original_snippet: item.snippet },
                     is_real_time: false,
                     source_type: 'web',
                     title: item.title,
                     snippet: item.snippet,
                     position: index + 1,
                     scrapedContent: scrapedContent
                 };
             });

             const results = (await Promise.all(resultsPromises)).filter((r): r is SearchResult => r !== null);
             return results;

         } catch (error: any) {
             let errorMessage = error.message;
             if (error.response?.data?.error?.message) {
                 errorMessage = error.response.data.error.message;
             } else if (error.errors && Array.isArray(error.errors) && error.errors.length > 0) {
                 // Use unknown and type check properties before accessing
                 errorMessage = error.errors.map((e: unknown) => {
                     if (typeof e === 'object' && e !== null && 'reason' in e && 'message' in e) {
                         return `${e.reason}: ${e.message}`;
                     }
                     return 'Unknown error detail';
                 }).join(', ');
             }
             console.error(`Google Custom Search API failed: ${errorMessage}`);
             // Log error but don't throw, allow other modules
             console.error(`Google Custom Search API failed: ${errorMessage}`);
             return [];
         }
    }
    // --- End Web Search Implementation ---


    // --- Result Processing Implementation ---
    private _processResults(results: SearchResult[], query: string, isCancelled: () => boolean): SearchResult[] {
        if (isCancelled()) { console.log("[Process Results] Cancelled."); return []; }
        console.log(`Processing ${results.length} raw results for query: "${query}"`);

        // 1. Deduplication based on content hash
        const seenContentHashes = new Set<string>();
        const uniqueResults: SearchResult[] = [];

        // Prioritize real-time/API results during deduplication
        const realTimeApiResults = results.filter(r => r.is_real_time || r.source_type === 'api');
        const webResults = results.filter(r => !r.is_real_time && r.source_type === 'web');

        for (const result of [...realTimeApiResults, ...webResults]) {
             // Create a hash of the main content (first ~200 chars for efficiency)
             const contentSample = result.content.substring(0, 200);
             const contentHash = crypto.createHash('md5').update(contentSample).digest('hex');

             if (!seenContentHashes.has(contentHash)) {
                 seenContentHashes.add(contentHash);
                 uniqueResults.push(result);
             } else {
                 console.log(`Skipping duplicate content result: ${result.title || result.url}`);
             }
        }
        console.log(`Reduced to ${uniqueResults.length} unique results after deduplication.`);


        // 2. Adjust relevance scores
        const now = Date.now(); // Milliseconds
        const processedResults = uniqueResults.map(result => {
            if (isCancelled()) return null; // Check within map
            let adjustedScore = result.relevance_score;

            // Time-based adjustment (more recent = higher score)
            try {
                const resultTime = new Date(result.timestamp).getTime(); // Milliseconds
                if (!isNaN(resultTime)) {
                    const timeDiffHours = (now - resultTime) / (1000 * 60 * 60);
                    // Apply decay factor, ensure it doesn't drop score too drastically
                    const freshnessFactor = Math.exp(-0.05 * Math.max(0, timeDiffHours)); // Slower decay than Python example
                    adjustedScore *= (0.7 + 0.3 * freshnessFactor); // Blend base score with freshness
                }
            } catch (e) {
                 console.warn(`Invalid timestamp format for score adjustment: ${result.timestamp}`);
            }


            // Content quality adjustment (longer content generally better, up to a point)
            const contentLength = result.content.length;
            const qualityFactor = Math.min(1.0, Math.max(0.5, contentLength / 1500)); // Prefer content > ~750 chars, max boost at 1500
            adjustedScore *= (0.8 + 0.2 * qualityFactor); // Blend base score with quality

            // Ensure score stays within a reasonable range (e.g., 0 to 1.1 to allow slight boosts)
            result.relevance_score = Math.max(0, Math.min(1.1, adjustedScore));

            return result;
        }).filter((r): r is SearchResult => r !== null); // Filter out nulls from cancellation checks

        // 3. Sort final results
        processedResults.sort((a, b) => {
            // Prioritize real-time results
            if (a.is_real_time !== b.is_real_time) {
                return a.is_real_time ? -1 : 1;
            }
            // Then sort by adjusted relevance score (descending)
            return b.relevance_score - a.relevance_score;
        });

        console.log(`Returning ${processedResults.length} processed and sorted results.`);
        if (isCancelled()) return []; // Check after processing loop
        return processedResults;
    }
    // --- End Result Processing ---


    // --- Enhanced Scraping Implementation ---
    /**
     * Attempts to extract meaningful text content from a given URL.
     * Includes retries, basic anti-scraping header mimicry, and removal of common non-content elements.
     * NOTE: Web scraping is inherently fragile. Sites may block requests (e.g., 401/403),
     * use dynamic JavaScript rendering not handled here, change layouts, or employ advanced bot detection.
     * Failures (returning null) are expected for some sites. Uses JSDOM and Mozilla Readability.
     */
    private async _extractContent(url: string, isCancelled: () => boolean): Promise<string | null> {
        if (isCancelled()) { console.log(`[Scrape] Cancelled before starting scrape for ${url}`); return null; }
        if (!this._isValidUrl(url)) {
            console.log(`Skipping scrape for invalid or excluded domain: ${url}`);
            return null;
        }
        console.log(`Attempting to scrape content from: ${url}`);

        // Define headers similar to Python version
        const headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'en-US,en;q=0.5',
            'DNT': '1',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1'
        };

        for (let attempt = 0; attempt < SCRAPE_RETRIES; attempt++) {
            if (isCancelled()) { console.log(`[Scrape] Cancelled before attempt ${attempt + 1} for ${url}`); return null; }
            const abortController = new AbortController();
            const timeoutId = setTimeout(() => { if (isCancelled()) abortController.abort(); }, 100);
            try {
                const response = await this.axiosInstance.get(url, {
                    timeout: SCRAPE_TIMEOUT_MS,
                    headers: headers,
                    signal: abortController.signal, // Pass signal
                    // Axios handles redirects by default, maxRedirects can be configured if needed
                });
                clearTimeout(timeoutId); // Clear timeout if request succeeds

                // Check for non-successful status codes that aren't automatically thrown (e.g., 4xx client errors)
                if (response.status >= 400) {
                    // Handle rate limiting (429) specifically
                    if (response.status === 429 && attempt < SCRAPE_RETRIES - 1) {
                        const delay = SCRAPE_RETRY_DELAY_MS * Math.pow(2, attempt); // Exponential backoff
                        console.warn(`Rate limit hit for ${url}. Retrying in ${delay}ms...`);
                        await new Promise(resolve => setTimeout(resolve, delay));
                        continue; // Retry
                    } else {
                        console.warn(`Scraping failed for ${url} with status ${response.status}.`);
                        return null; // Don't retry other client/server errors immediately
                    }
                } // End of status code check

                if (isCancelled()) return null; // Check after successful response before parsing

                // --- HYBRID APPROACH: Use both Cheerio for targeted extraction and Readability for fallback ---
                try {
                    // First try targeted extraction with Cheerio (similar to Python's BeautifulSoup approach)
                    const $ = cheerio.load(response.data);

                    // Remove unwanted elements that typically contain non-content
                    $('script, style, nav, footer, header, aside, noscript, iframe, .cookie-banner, .ad, .ads, .advertisement').remove();

                    // Try to find main content area using common selectors
                    const contentSelectors = [
                        'main', 'article', 'div[role="main"]', 'div[class*="content"]',
                        'div[class*="article"]', 'div[class*="post"]', 'div[id*="content"]',
                        'div[id*="article"]', 'div[id*="post"]'
                    ];

                    let mainContent = null;
                    for (const selector of contentSelectors) {
                        if ($(selector).length > 0) {
                            mainContent = $(selector).first();
                            break;
                        }
                    }

                    // If no main content area found, use the body
                    if (!mainContent) {
                        mainContent = $('body');
                    }

                    // Extract content elements
                    const contentElements: string[] = [];

                    // Add title if available
                    const title = $('title').text().trim();
                    if (title) {
                        contentElements.push(title);
                    }

                    // Add meta description if available
                    const metaDesc = $('meta[name="description"]').attr('content');
                    if (metaDesc) {
                        contentElements.push(metaDesc);
                    }

                    // Extract text from main content elements
                    mainContent.find('p, h1, h2, h3, h4, h5, h6, li, blockquote').each((_, elem) => {
                        const text = $(elem).text().trim();
                        if (text && text.length > 10) { // Skip very short elements
                            contentElements.push(text);
                        }
                    });

                    // If we found meaningful content with Cheerio
                    if (contentElements.length > 0) {
                        // Clean and normalize content
                        let content = contentElements.join('\n\n');

                        // Remove any remaining HTML tags
                        content = content.replace(/<[^>]+>/g, '');

                        // Normalize whitespace
                        content = content.replace(/\s+/g, ' ').trim();

                        // Restore paragraph breaks for readability
                        content = content.replace(/\. /g, '.\n\n');

                        // Truncate if too long
                        const truncatedContent = content.length > 7000
                            ? content.substring(0, 7000) + '...'
                            : content;

                        return `${truncatedContent}\n\nSource: ${url}`;
                    }

                    // If Cheerio extraction didn't yield good results, fall back to Readability
                    console.log(`Cheerio extraction didn't yield good results for ${url}, falling back to Readability`);
                } catch (cheerioError: any) {
                    console.warn(`Cheerio extraction failed for ${url}, falling back to Readability:`, cheerioError?.message || 'Unknown error');
                }

                // --- FALLBACK: Use Readability if Cheerio approach didn't work ---
                if (isCancelled()) return null;

                const dom = new JSDOM(response.data, { url: url });
                const reader = new Readability(dom.window.document);
                const article = reader.parse();

                if (!article || !article.textContent) {
                    console.warn(`Readability could not parse meaningful content from ${url}`);
                    return null;
                }

                // Combine title and text content
                let combinedContent = `Title: ${article.title}\n\n${article.textContent}`;

                // Clean up whitespace
                combinedContent = combinedContent.replace(/\s{3,}/g, '\n\n'); // Normalize multiple spaces/newlines
                combinedContent = combinedContent.replace(/^\s*[\r\n]/gm, ''); // Remove empty lines

                // Remove CSS variables and styling
                combinedContent = combinedContent.replace(/--[a-zA-Z0-9-]+:[^;]+;/g, '');
                combinedContent = combinedContent.replace(/\{[^\}]+\}/g, '');

                // Remove HTML tags
                combinedContent = combinedContent.replace(/<[^>]+>/g, '');

                // Remove CSS classes and IDs
                combinedContent = combinedContent.replace(/\.[a-zA-Z0-9_-]+/g, '');
                combinedContent = combinedContent.replace(/#[a-zA-Z0-9_-]+/g, '');

                // Remove any remaining CSS/HTML artifacts
                combinedContent = combinedContent.replace(/(@media|\{|\}|\.dcr-|--[a-zA-Z-]+:)/g, '');

                // Remove multiple spaces created by the cleaning
                combinedContent = combinedContent.replace(/\s+/g, ' ').trim();

                // Restore paragraph breaks for readability
                combinedContent = combinedContent.replace(/\. /g, '.\n\n');

                const truncatedContent = combinedContent.length > 7000
                    ? combinedContent.substring(0, 7000) + '...'
                    : combinedContent;

                return `${truncatedContent}\n\nSource: ${url}`;

            } catch (error: any) {
                clearTimeout(timeoutId); // Clear timeout on error
                if (axios.isCancel(error)) { console.log(`[Scrape] Request cancelled for ${url}.`); return null; }
                console.warn(`Scraping attempt ${attempt + 1}/${SCRAPE_RETRIES} failed for ${url}: ${error.message}`);
                if (attempt < SCRAPE_RETRIES - 1) {
                    const delay = SCRAPE_RETRY_DELAY_MS * Math.pow(2, attempt); // Exponential backoff
                    // Check cancellation before sleeping
                    if (isCancelled()) { console.log(`[Scrape] Cancelled during retry delay for ${url}`); return null; }
                    await new Promise(resolve => setTimeout(resolve, delay));
                } else {
                    console.error(`Scraping failed definitively for ${url} after ${SCRAPE_RETRIES} attempts.`);
                    // Don't throw, just return null to indicate failure for this URL
                    return null;
                }
            }
        }
        // Should not be reachable if loop completes, but return null just in case
        return null;
    }
    // --- End Enhanced Scraping ---

    private _isValidUrl(url: string): boolean {
        try {
            const parsedUrl = new URL(url);
            const domain = parsedUrl.hostname.replace(/^www\./, '');
            // Basic check for file extensions often not useful for scraping text content
            if (/\.(pdf|jpg|jpeg|png|gif|svg|zip|exe|mp3|mp4|css|js)$/i.test(parsedUrl.pathname)) {
                return false;
            }
            return !EXCLUDED_SCRAPE_DOMAINS.has(domain);
        } catch (e) {
            return false; // Invalid URL format
        }
    }


    // Main search method
    public async search(query: string, isCancelled: () => boolean = this.defaultIsCancelled): Promise<SearchResult[]> {
        const startTime = performance.now();
        console.log(`Starting enhanced search for query: "${query}"`);

        try {
            if (isCancelled()) { console.log("[Search] Cancelled at start."); return []; }

            // 1. Check for real-time data needs
            if (this.isTimeSensitiveQuery(query)) {
                const realTimeResults = await this._getRealTimeData(query, isCancelled);
                if (isCancelled()) return []; // Check after async call
                if (realTimeResults && realTimeResults.length > 0) {
                    console.log(`Returning ${realTimeResults.length} real-time results.`);
             // Optionally: Cache real-time results with very short expiry?
             // this.cacheResults(query, realTimeResults); // Be careful caching real-time data
              const endTime = performance.now();
              console.log(`Enhanced search completed in ${(endTime - startTime).toFixed(2)} ms (Real-time)`);
                    return realTimeResults;
                }
            }

            // 2. Check cache
            const cacheMaxAge = this.isTimeSensitiveQuery(query)
                ? TIME_SENSITIVE_CACHE_MAX_AGE_SECONDS
                : DEFAULT_CACHE_MAX_AGE_SECONDS;
            const cachedResults = await this.getCachedResults(query, cacheMaxAge);
            if (isCancelled()) return []; // Check after async call
             if (cachedResults) {
                 console.log(`Returning ${cachedResults.length} results from cache.`);
                  const endTime = performance.now();
                  console.log(`Enhanced search completed in ${(endTime - startTime).toFixed(2)} ms (Cache)`);
                return cachedResults;
            }

            // 3. Perform web search (if no real-time or cache hit)
            if (isCancelled()) return [];
            const webResults = await this._performWebSearch(query, isCancelled);
            if (isCancelled()) return []; // Check after async call
             if (!webResults || webResults.length === 0) {
                 console.log("No results found from web search.");
                  const endTime = performance.now();
                  console.log(`Enhanced search completed in ${(endTime - startTime).toFixed(2)} ms (No web results)`);
                return [];
            }

            // 4. Process results (deduplicate, score, sort)
            if (isCancelled()) return [];
            const finalResults = this._processResults(webResults, query, isCancelled);
            if (isCancelled()) return []; // Check after processing

            // 5. Cache the final web results
            if (finalResults.length > 0) {
                await this.cacheResults(query, finalResults);
             }

             const endTime = performance.now();
             console.log(`Enhanced search completed in ${(endTime - startTime).toFixed(2)} ms (Web search)`);
             return finalResults;

         } catch (error: any) {
             // Catch unexpected errors during the main search flow
             console.error(`Unexpected error during enhanced search for query "${query}":`, error);
             // Throw a specific SearchError for unexpected issues
             throw new SearchError(`Unexpected error during search: ${error.message}`, 'UNEXPECTED_SEARCH_ERROR', error);
         }
    }

    // Method to clean up the cache directory (optional)
    public async cleanupCache(): Promise<void> {
        if (!this.cacheDir) return;
        try {
            const files = await fs.readdir(this.cacheDir);
            for (const file of files) {
                await fs.unlink(path.join(this.cacheDir, file));
            }
            console.log(`Cache directory ${this.cacheDir} cleaned.`);
         } catch (error: any) {
             // Log error but don't throw, cleanup failure is not critical
             console.error(`Error cleaning cache directory ${this.cacheDir}:`, error);
         }
    }
}

// --- Exporting an instance or the class ---
// Option 1: Export the class for instantiation elsewhere
// export { EnhancedSearchManager };

// Option 2: Export a singleton instance (if appropriate for your backend structure)
// const searchManager = new EnhancedSearchManager();
// export default searchManager;

// Option 3: Keep the original function signature for compatibility, using the class internally
export const performInternetSearch = async (
  query: string,
  apiKey: string, // API key for the selected provider
  provider: string = 'Serper', // Selected provider (can be 'All' for using all available providers)
  googleCx?: string, // Google CX ID
  isCancelled?: () => boolean // Cancellation function
): Promise<SearchResult[]> => {
    // Create a config object with all available API keys
    // If provider is 'All', we'll use all available API keys
    // Otherwise, we'll only use the API key for the selected provider
    const config: SearchConfig = {
        // Use environment variables as fallbacks
        serperApiKey: (provider.toLowerCase() === 'serper' || provider.toLowerCase() === 'all') ? apiKey : process.env.SERPER_API_KEY,
        googleApiKey: (provider.toLowerCase() === 'google' || provider.toLowerCase() === 'all') ? apiKey : process.env.GOOGLE_API_KEY,
        googleCxId: (provider.toLowerCase() === 'google' || provider.toLowerCase() === 'all') ? googleCx : process.env.GOOGLE_SEARCH_ENGINE_ID,
        duckduckgoApiKey: (provider.toLowerCase() === 'duckduckgo' || provider.toLowerCase() === 'all') ? apiKey : process.env.DUCKDUCKGO_API_KEY,
        braveApiKey: (provider.toLowerCase() === 'brave' || provider.toLowerCase() === 'all') ? apiKey : process.env.BRAVE_API_KEY,
        polygonApiKey: process.env.POLYGON_API_KEY,
        openWeatherApiKey: process.env.OPENWEATHER_API_KEY,
    };

    // Ensure the manager uses the potentially overridden config
    const manager = new EnhancedSearchManager(config);
     // The manager's search method now handles provider logic internally based on its config
     // Pass the cancellation function down
     try {
         return await manager.search(query, isCancelled);
     } catch (error) {
         // Catch errors thrown by manager.search and log them, return empty array
         console.error(`[performInternetSearch] Error caught from EnhancedSearchManager:`, error);
         // Optionally, re-throw specific critical errors if needed, otherwise return empty
         // if (error instanceof SearchError && error.code === 'SOME_CRITICAL_CODE') { throw error; }
         return [];
     }
 };
