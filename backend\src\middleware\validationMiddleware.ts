import { body, param, query, validationResult } from 'express-validator';
import { Request, Response, NextFunction } from 'express';

// Middleware to handle validation errors
export const handleValidationErrors = (req: Request, res: Response, next: NextFunction) => {
  const errors = validationResult(req);
  if (!errors.isEmpty()) {
    return res.status(400).json({
      error: 'Validation failed',
      details: errors.array()
    });
  }
  next();
};

// User registration validation
export const validateUserRegistration = [
  body('email')
    .isEmail()
    .normalizeEmail()
    .withMessage('Please provide a valid email address'),
  body('password')
    .isLength({ min: 8, max: 128 })
    .withMessage('Password must be between 8 and 128 characters')
    .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/)
    .withMessage('Password must contain at least one lowercase letter, one uppercase letter, and one number'),
  handleValidationErrors
];

// User login validation
export const validateUserLogin = [
  body('email')
    .isEmail()
    .normalizeEmail()
    .withMessage('Please provide a valid email address'),
  body('password')
    .notEmpty()
    .withMessage('Password is required'),
  handleValidationErrors
];

// Configuration profile validation
export const validateConfigurationProfile = [
  body('name')
    .isLength({ min: 1, max: 100 })
    .trim()
    .withMessage('Profile name must be between 1 and 100 characters'),
  body('settings.agentCount')
    .isInt({ min: 1, max: 10 })
    .withMessage('Agent count must be between 1 and 10'),
  body('settings.generalInstructions')
    .optional()
    .isLength({ max: 5000 })
    .withMessage('General instructions must not exceed 5000 characters'),
  body('settings.agentConfigurations')
    .isArray({ min: 1, max: 10 })
    .withMessage('Must have between 1 and 10 agent configurations'),
  body('settings.agentConfigurations.*.provider')
    .isIn(['openai', 'anthropic', 'google', 'groq', 'ollama', 'lmstudio', 'deepseek'])
    .withMessage('Invalid provider'),
  body('settings.agentConfigurations.*.model')
    .isLength({ min: 1, max: 100 })
    .withMessage('Model name is required and must not exceed 100 characters'),
  handleValidationErrors
];

// API key validation
export const validateApiKey = [
  body('serviceName')
    .isLength({ min: 1, max: 50 })
    .trim()
    .withMessage('Service name must be between 1 and 50 characters'),
  body('apiKey')
    .isLength({ min: 1, max: 500 })
    .withMessage('API key is required and must not exceed 500 characters'),
  handleValidationErrors
];

// Prompt validation
export const validatePrompt = [
  body('prompt')
    .isLength({ min: 1, max: 50000 })
    .trim()
    .withMessage('Prompt must be between 1 and 50000 characters'),
  body('config')
    .isObject()
    .withMessage('Configuration object is required'),
  body('conversationId')
    .optional()
    .isMongoId()
    .withMessage('Invalid conversation ID format'),
  handleValidationErrors
];

// File upload validation
export const validateFileUpload = [
  body('fileName')
    .optional()
    .isLength({ min: 1, max: 255 })
    .withMessage('File name must not exceed 255 characters'),
  handleValidationErrors
];

// MongoDB ObjectId validation
export const validateObjectId = [
  param('id')
    .isMongoId()
    .withMessage('Invalid ID format'),
  handleValidationErrors
];

// Pagination validation
export const validatePagination = [
  query('page')
    .optional()
    .isInt({ min: 1, max: 1000 })
    .withMessage('Page must be between 1 and 1000'),
  query('limit')
    .optional()
    .isInt({ min: 1, max: 100 })
    .withMessage('Limit must be between 1 and 100'),
  handleValidationErrors
];

// Search query validation
export const validateSearchQuery = [
  query('q')
    .isLength({ min: 1, max: 1000 })
    .trim()
    .withMessage('Search query must be between 1 and 1000 characters'),
  handleValidationErrors
];

// RAG settings validation
export const validateRagSettings = [
  body('enabled')
    .isBoolean()
    .withMessage('Enabled must be a boolean'),
  body('chunkSize')
    .optional()
    .isInt({ min: 100, max: 2000 })
    .withMessage('Chunk size must be between 100 and 2000'),
  body('chunkOverlap')
    .optional()
    .isInt({ min: 0, max: 500 })
    .withMessage('Chunk overlap must be between 0 and 500'),
  body('retrievalNResults')
    .optional()
    .isInt({ min: 1, max: 20 })
    .withMessage('Retrieval results must be between 1 and 20'),
  body('retrievalThreshold')
    .optional()
    .isFloat({ min: 0, max: 1 })
    .withMessage('Retrieval threshold must be between 0 and 1'),
  handleValidationErrors
];

// Internet settings validation
export const validateInternetSettings = [
  body('enabled')
    .isBoolean()
    .withMessage('Enabled must be a boolean'),
  body('searchProvider')
    .optional()
    .isIn(['serper', 'google'])
    .withMessage('Invalid search provider'),
  body('includedDomains')
    .optional()
    .isArray()
    .withMessage('Included domains must be an array'),
  body('excludedDomains')
    .optional()
    .isArray()
    .withMessage('Excluded domains must be an array'),
  handleValidationErrors
];

// Custom sanitization middleware
export const sanitizeInput = (req: Request, res: Response, next: NextFunction) => {
  // Recursively sanitize all string inputs
  const sanitizeObject = (obj: any): any => {
    if (typeof obj === 'string') {
      // Remove potentially dangerous characters
      return obj
        .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
        .replace(/javascript:/gi, '')
        .replace(/vbscript:/gi, '')
        .replace(/onload=/gi, '')
        .replace(/onerror=/gi, '')
        .trim();
    } else if (Array.isArray(obj)) {
      return obj.map(sanitizeObject);
    } else if (obj && typeof obj === 'object') {
      const sanitized: any = {};
      for (const key in obj) {
        if (obj.hasOwnProperty(key)) {
          sanitized[key] = sanitizeObject(obj[key]);
        }
      }
      return sanitized;
    }
    return obj;
  };

  if (req.body) {
    req.body = sanitizeObject(req.body);
  }
  if (req.query) {
    req.query = sanitizeObject(req.query);
  }
  if (req.params) {
    req.params = sanitizeObject(req.params);
  }

  next();
};
