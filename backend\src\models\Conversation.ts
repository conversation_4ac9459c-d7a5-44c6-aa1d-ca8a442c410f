import mongoose, { Schema, Document } from 'mongoose';

// Re-use or define the message structure
interface ConversationMessage {
    role: 'user' | 'assistant' | 'system' | 'tool';
    content: string;
    agentName?: string;
    timestamp: string; // Store timestamp as ISO string
    localImagePath?: string; // Optional path for image messages
    imageUrl?: string; // Optional: Store original URL for reference
    imageDataUrl?: string; // Optional: Store original data URL for reference (can be very long)
    hasImage?: boolean; // Flag to indicate if message has an image
}

const MessageSchema = new Schema<ConversationMessage>({
    role: { type: String, required: true, enum: ['user', 'assistant', 'system', 'tool'] },
    content: { type: String, required: true, default: ' ' }, // Default to space to avoid validation errors
    agentName: { type: String },
    timestamp: { type: String, required: true },
    localImagePath: { type: String }, // Path for locally saved images
    imageUrl: { type: String }, // Optional original URL
    imageDataUrl: { type: String }, // Optional data URL (can be very large)
}, { _id: false }); // No separate _id for messages within the array

// Interface for the Conversation Document
export interface IConversation extends Document {
  userId: mongoose.Schema.Types.ObjectId; // Link to the User model
  title: string; // e.g., first user message snippet or user-defined title
  messages: ConversationMessage[];
  metadata?: Record<string, any>; // Store related config snapshot or other info
  createdAt: Date;
  updatedAt: Date;
}

const ConversationSchema = new Schema<IConversation>(
  {
    userId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
      required: true, // Make required
      index: true, // Add index for user-specific queries
    },
    title: {
      type: String,
      required: true,
      trim: true,
      default: 'New Conversation'
    },
    messages: {
      type: [MessageSchema],
      required: true,
      default: [],
    },
     metadata: {
      type: Schema.Types.Mixed, // Store arbitrary data like config snapshot
      required: false,
    },
  },
  {
    timestamps: true,
  }
);

// Add index on userId and updatedAt for efficient querying per user, sorted by recency
ConversationSchema.index({ userId: 1, updatedAt: -1 });

const Conversation = mongoose.model<IConversation>(
  'Conversation',
  ConversationSchema
);

export default Conversation;
