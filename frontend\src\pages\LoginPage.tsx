import React, { useState } from 'react';
import { Link, useNavigate } from 'react-router-dom'; // Import Link and useNavigate
import { useConfigStore } from '../store/configStore'; // Import the store
import { BACKEND_BASE_URL } from '../config'; // Import backend URL

const LoginPage: React.FC = () => {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const login = useConfigStore((state) => state.login); // Get login action
  const authLoading = useConfigStore((state) => state.authLoading); // Get loading state
  const authError = useConfigStore((state) => state.globalError); // Use global error state for auth errors
  const setAuthError = useConfigStore((state) => state.setGlobalError); // Action to set/clear error
  const navigate = useNavigate(); // Hook for navigation

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setAuthError(null); // Clear previous errors
    try {
      await login({ email, password });
      navigate('/app'); // Redirect directly to app on successful login
    } catch (err) {
      // Error is already set in the store by the login action
      console.error("Login failed:", err);
    }
  };

  return (
    <div className="flex items-center justify-center min-h-screen bg-gray-100 dark:bg-gray-900">
      <div className="w-full max-w-md p-8 space-y-6 bg-white rounded-lg shadow-md dark:bg-gray-800">
        <div className="flex justify-between items-center">
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
            Login to MAIA
          </h2>
          <Link to="/home" className="text-sm font-medium text-indigo-600 hover:text-indigo-500 dark:text-indigo-400 dark:hover:text-indigo-300">
            Back to Home
          </Link>
        </div>
        <form className="space-y-6" onSubmit={handleSubmit}>
          <div>
            <label
              htmlFor="email"
              className="block text-sm font-medium text-gray-700 dark:text-gray-300"
            >
              Email address
            </label>
            <input
              id="email"
              name="email"
              type="email"
              autoComplete="email"
              required
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              className="block w-full px-3 py-2 mt-1 text-gray-900 bg-gray-50 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-indigo-500 dark:focus:border-indigo-500"
              placeholder="<EMAIL>"
            />
          </div>

          <div>
            <label
              htmlFor="password"
              className="block text-sm font-medium text-gray-700 dark:text-gray-300"
            >
              Password
            </label>
            <input
              id="password"
              name="password"
              type="password"
              autoComplete="current-password"
              required
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              className="block w-full px-3 py-2 mt-1 text-gray-900 bg-gray-50 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400 dark:text-white dark:focus:ring-indigo-500 dark:focus:border-indigo-500"
              placeholder="********"
            />
          </div>

          {authError && (
            <p className="text-sm text-red-600 dark:text-red-400">{authError}</p>
          )}

          <div>
            <button
              type="submit"
              disabled={authLoading}
              className="flex justify-center w-full px-4 py-2 text-sm font-medium text-white bg-indigo-600 border border-transparent rounded-md shadow-sm hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 disabled:opacity-50 dark:focus:ring-offset-gray-800"
            >
              {authLoading ? 'Logging in...' : 'Login'}
            </button>
          </div>
        </form>
        <p className="text-sm text-center text-gray-600 dark:text-gray-400">
          Don't have an account?{' '}
          <Link to="/register" className="font-medium text-indigo-600 hover:text-indigo-500 dark:text-indigo-400 dark:hover:text-indigo-300">
            Register here
          </Link>
        </p>

        {/* Divider */}
        <div className="my-6 flex items-center justify-center">
          <span className="h-px flex-1 bg-gray-300"></span>
          <span className="px-4 text-sm text-gray-500">OR</span>
          <span className="h-px flex-1 bg-gray-300"></span>
        </div>

        {/* Google Sign-in Button */}
        <a
          href={`${BACKEND_BASE_URL}/api/auth/google`}
          className="w-full flex items-center justify-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500"
        >
          {/* Optional: Add Google Icon here */}
          <svg className="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.04 7.58C8.36 8.28 8 9.15 8 10.12c0 .97.36 1.84 1.04 ******** 1.63 1.04 2.67 1.04.85 0 1.6-.25 2.2-.74l-1.04-1.04c-.27.2-.6.3-.96.3-.6 0-1.12-.2-1.54-.62-.42-.42-.63-.95-.63-1.58s.2-1.16.63-1.58c.42-.42.94-.63 1.54-.63.36 0 .68.1.96.3l1.04-1.04c-.6-.5-1.35-.74-2.2-.74-.94 0-1.77.35-2.47 1.04z" clipRule="evenodd"></path></svg>
          Sign in with Google
        </a>

      </div>
    </div>
  );
};

export default LoginPage;
