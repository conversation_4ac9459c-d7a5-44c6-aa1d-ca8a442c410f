# Agent Profiles for MAIAChat

This directory contains example agent profiles that you can import into MAIAChat. These profiles are designed for different use cases and team sizes, ranging from single agents to teams of four agents.

## How to Use These Profiles

1. In the MAIAChat interface, navigate to the Configuration panel
2. Look for an option to import or load a profile
3. Select the JSON file for the profile you want to use
4. Adjust any settings as needed for your specific use case

## Available Profiles

### Single Agent Profiles

- **Research Assistant**: A comprehensive research agent that can find, analyze, and summarize information on any topic.
- **Code Reviewer**: An agent specialized in reviewing code for bugs, security vulnerabilities, and adherence to best practices.

### Two Agent Profiles

- **Content Creation Team**: A writer and editor team that works together to create high-quality content.
- **Debate Partners**: Two agents that present different perspectives on a topic to provide a balanced view.

### Three Agent Profiles

- **Software Development Team**: A team consisting of an architect, developer, and tester that works together to design, implement, and test software solutions.
- **Travel Planning Team**: A team of destination researcher, itinerary planner, and budget analyst that creates comprehensive travel plans.

### Four Agent Profiles

- **Business Strategy Team**: A team of market analyst, financial advisor, product strategist, and risk assessor that develops comprehensive business strategies.
- **Fantasy Adventure Party**: A fun role-playing team consisting of a wizard, warrior, rogue, and healer that approaches problems from different perspectives.

## Customizing Profiles

Feel free to modify these profiles to better suit your needs:

- Change the provider and model based on your available API keys
- Adjust the instructions to focus on specific aspects of the task
- Modify the internet and RAG settings based on your requirements
- Adjust temperature and token settings to control the output style and length

## Notes

- These profiles assume you have appropriate API keys configured for the specified providers
- Internet search functionality requires a configured search provider API key
- For optimal results, consider enabling RAG (Retrieval-Augmented Generation) and providing relevant documents
