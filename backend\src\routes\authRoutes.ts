import express from 'express';
import passport from 'passport';
import { registerUser, loginUser, getCurrentUser, updateLastUsedProfile, googleCallback } from '../controllers/authController';
// Import the authentication middleware
import { protect } from '../middleware/authMiddleware';

const router = express.Router();

// --- Email/Password Routes ---

// @route   POST /api/auth/register
// @desc    Register a new user
// @access  Public
router.post('/register', registerUser);

// @route   POST /api/auth/login
// @desc    Authenticate user & get token
// @access  Public
router.post('/login', loginUser);

// @route   GET /api/auth/me
// @desc    Get current user data
// @access  Private
router.get('/me', protect, getCurrentUser); // Apply the protect middleware

// @route   PUT /api/auth/lastUsedProfile
// @desc    Update the user's last used profile ID
// @access  Private
router.put('/lastUsedProfile', protect, updateLastUsedProfile);

// --- Google OAuth Routes ---

// @route   GET /api/auth/google
// @desc    Initiate Google OAuth authentication
// @access  Public
router.get('/google', passport.authenticate('google', { scope: ['profile', 'email'] }));

// @route   GET /api/auth/google/callback
// @desc    Google OAuth callback URL
// @access  Public
router.get(
  '/google/callback',
  passport.authenticate('google', {
    failureRedirect: `${process.env.FRONTEND_URL || 'http://localhost:5173'}/login?error=google_failed`,
    session: false // We are not using session storage, JWT will be issued
  }),
  googleCallback // Controller function to handle successful auth
);

export default router;
