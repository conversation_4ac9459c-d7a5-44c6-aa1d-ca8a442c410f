import React from 'react';
import { Link } from 'react-router-dom';

const PrivacyPolicyPage: React.FC = () => {
  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        {/* Header */}
        <div className="text-center mb-8">
          <Link 
            to="/" 
            className="inline-flex items-center text-primary hover:text-primary-dark mb-4"
          >
            <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 19l-7-7m0 0l7-7m-7 7h18" />
            </svg>
            Back to MAIAChat
          </Link>
          <h1 className="text-4xl font-bold text-gray-900 dark:text-white">
            Privacy Policy
          </h1>
          <p className="text-gray-600 dark:text-gray-400 mt-2">
            Last Updated: August 3, 2025
          </p>
        </div>

        {/* Content */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-8">
          <div className="prose prose-lg dark:prose-invert max-w-none">
            
            <h2>1. Introduction</h2>
            <p>
              MAIAChat ("we," "our," or "us") is committed to protecting your privacy. This Privacy Policy explains how we collect, use, disclose, and safeguard your information when you use our multi-agent AI assistant web application ("the Service").
            </p>

            <h2>2. Information We Collect</h2>
            <h3>2.1 Personal Information</h3>
            <ul>
              <li><strong>Account Information</strong>: Email address, password (hashed), profile preferences</li>
              <li><strong>Authentication Data</strong>: Login timestamps, IP addresses, session information</li>
              <li><strong>Google OAuth</strong>: If you use Google login, we receive your Google profile information</li>
            </ul>

            <h3>2.2 Usage Information</h3>
            <ul>
              <li><strong>Conversation Data</strong>: Messages, prompts, and AI responses</li>
              <li><strong>Configuration Data</strong>: Agent settings, preferences, and custom configurations</li>
              <li><strong>File Uploads</strong>: Documents uploaded for RAG processing</li>
              <li><strong>Generated Content</strong>: Images and text generated by AI models</li>
            </ul>

            <h3>2.3 Technical Information</h3>
            <ul>
              <li><strong>Device Information</strong>: Browser type, operating system, device identifiers</li>
              <li><strong>Log Data</strong>: IP addresses, access times, pages viewed, errors encountered</li>
              <li><strong>Cookies</strong>: Session cookies for authentication and preferences</li>
            </ul>

            <h2>3. How We Use Your Information</h2>
            <h3>3.1 Service Provision</h3>
            <ul>
              <li>Provide and maintain the AI assistant service</li>
              <li>Process your requests and generate AI responses</li>
              <li>Store and retrieve your conversation history</li>
              <li>Manage your account and preferences</li>
            </ul>

            <h3>3.2 Service Improvement</h3>
            <ul>
              <li>Analyze usage patterns to improve the service</li>
              <li>Debug and fix technical issues</li>
              <li>Develop new features and capabilities</li>
            </ul>

            <h2>4. Information Sharing and Disclosure</h2>
            <h3>4.1 Third-Party AI Services</h3>
            <ul>
              <li>We send your prompts to third-party AI services using your API keys</li>
              <li>These services have their own privacy policies and data handling practices</li>
              <li>We do not share your API keys with anyone other than the intended service providers</li>
            </ul>

            <h3>4.2 Legal Requirements</h3>
            <p>We may disclose your information if required by law or in response to:</p>
            <ul>
              <li>Valid legal process (subpoenas, court orders)</li>
              <li>Government requests for national security purposes</li>
              <li>Protection of our rights, property, or safety</li>
            </ul>

            <h2>5. Data Security</h2>
            <h3>5.1 Security Measures</h3>
            <ul>
              <li><strong>Encryption</strong>: All data is encrypted in transit and at rest</li>
              <li><strong>API Key Protection</strong>: API keys are encrypted using strong encryption algorithms</li>
              <li><strong>Access Controls</strong>: Strict access controls and authentication requirements</li>
              <li><strong>Regular Audits</strong>: Regular security audits and vulnerability assessments</li>
            </ul>

            <h2>6. Data Retention</h2>
            <h3>6.1 Account Data</h3>
            <ul>
              <li>Account information is retained while your account is active</li>
              <li>You can delete your account at any time, which will remove your personal information</li>
            </ul>

            <h3>6.2 Conversation Data</h3>
            <ul>
              <li>Conversation history is retained to provide the service</li>
              <li>You can delete individual conversations or all conversation data</li>
              <li>Deleted data is permanently removed from our systems within 30 days</li>
            </ul>

            <h2>7. Your Rights and Choices</h2>
            <h3>7.1 Access and Control</h3>
            <ul>
              <li><strong>View Data</strong>: Access your personal information through your account settings</li>
              <li><strong>Update Information</strong>: Modify your account information and preferences</li>
              <li><strong>Delete Data</strong>: Delete specific conversations or your entire account</li>
              <li><strong>Export Data</strong>: Request a copy of your data in a portable format</li>
            </ul>

            <h2>8. International Data Transfers</h2>
            <ul>
              <li>Our servers may be located in different countries</li>
              <li>We ensure appropriate safeguards are in place for international data transfers</li>
              <li>We comply with applicable data protection laws in all jurisdictions</li>
            </ul>

            <h2>9. Children's Privacy</h2>
            <ul>
              <li>Our service is not intended for children under 13 years of age</li>
              <li>We do not knowingly collect personal information from children under 13</li>
              <li>If we become aware of such collection, we will delete the information immediately</li>
            </ul>

            <h2>10. Changes to This Privacy Policy</h2>
            <ul>
              <li>We may update this Privacy Policy from time to time</li>
              <li>We will notify you of significant changes via email or through the service</li>
              <li>Continued use of the service after changes constitutes acceptance</li>
            </ul>

            <h2>11. Contact Us</h2>
            <p>If you have questions about this Privacy Policy or our data practices, contact us:</p>
            <ul>
              <li><strong>Email</strong>: <a href="mailto:<EMAIL>" className="text-primary hover:text-primary-dark"><EMAIL></a></li>
            </ul>

            <p>For EU residents, you also have the right to lodge a complaint with your local data protection authority.</p>

          </div>
        </div>

        {/* Footer */}
        <div className="text-center mt-8">
          <Link 
            to="/terms-of-service" 
            className="text-primary hover:text-primary-dark mr-6"
          >
            Terms of Service
          </Link>
          <Link 
            to="/" 
            className="text-primary hover:text-primary-dark"
          >
            Back to MAIAChat
          </Link>
        </div>
      </div>
    </div>
  );
};

export default PrivacyPolicyPage;
