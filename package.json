{"name": "maia-chat", "version": "1.0.0", "scripts": {"build": "npm install -g typescript && cd frontend && npm install --include=dev && npm install --save-dev @types/node && npm run build", "start": "cd backend && npm install && npm start"}, "dependencies": {"axios": "^1.8.4", "csv-parse": "^5.6.0", "form-data": "^4.0.2", "mammoth": "^1.9.0", "node-html-parser": "^7.0.1", "pdf-parse": "^1.1.1", "xlsx": "^0.18.5"}, "engines": {"node": ">=18.0.0"}}