import axios, { AxiosError } from 'axios'; // Import AxiosError type
import { useConfigStore } from '../store/configStore'; // Import store hook

// Import the base URL from config
import { BACKEND_BASE_URL } from '../config';

// Use the centralized config for API base URL
const API_BASE_URL = BACKEND_BASE_URL;

// Create an Axios instance for API calls
// The interceptor will handle adding the /auth prefix for auth-specific routes if needed,
// but generally, we want the base URL to be just the API root.
// Let's redefine the instance with just the base URL.
const apiClient = axios.create({
  baseURL: API_BASE_URL, // Use the base API URL
});

// Interface for user data returned from API
export interface UserData {
  id: string;
  email: string;
  subscriptionTier: string;
  lastUsedProfileId?: string; // Optional because it might be null for new users
}

// Interface for the auth response
interface AuthResponse {
  token: string;
  user: UserData;
}

// Function to register a user
export const registerUser = async (credentials: { email: string; password: string }): Promise<AuthResponse> => {
  try {
    // Use the specific path including /api/auth
    const response = await apiClient.post<AuthResponse>('/api/auth/register', credentials);
    return response.data;
  } catch (error: any) {
    // Log the error and reject the promise with the original error
    console.error('Registration API Error:', error.response?.data || error.message);
    return Promise.reject(error); // Re-throw original error
  }
};

// Function to log in a user
export const loginUser = async (credentials: { email: string; password: string }): Promise<AuthResponse> => {
  try {
    // Use the specific path including /api/auth
    const response = await apiClient.post<AuthResponse>('/api/auth/login', credentials);
    return response.data;
  } catch (error: any) {
    // Log the error and reject the promise with the original error
    console.error('Login API Error:', error.response?.data || error.message);
    return Promise.reject(error); // Re-throw original error
  }
};

// Function to get current user data (requires token)
export const fetchCurrentUser = async (token: string): Promise<UserData> => {
    try {
        // Use the specific path including /api/auth
        const response = await apiClient.get<UserData>('/api/auth/me', {
            headers: {
                Authorization: `Bearer ${token}`, // Interceptor will also add this, but explicit doesn't hurt
            },
        });
        return response.data;
    } catch (error: any) {
        // Log the error and reject the promise with the original error
        console.error('Fetch Current User API Error:', error.response?.data || error.message);
        // The interceptor will handle 401/403, but other errors should still be rejected
        return Promise.reject(error); // Re-throw original error
    }
};

// Function to update the user's last used profile ID
export const updateLastUsedProfile = async (profileId: string): Promise<{ message: string, lastUsedProfileId: string }> => {
    try {
        const response = await apiClient.put<{ message: string, lastUsedProfileId: string }>('/api/auth/lastUsedProfile', { profileId });
        return response.data;
    } catch (error: any) {
        console.error('Update Last Used Profile API Error:', error.response?.data || error.message);
        return Promise.reject(error);
    }
};

// --- JWT Storage ---

const TOKEN_KEY = 'maia_user_token';

export const storeToken = (token: string): void => {
  localStorage.setItem(TOKEN_KEY, token);
};

export const getToken = (): string | null => {
  return localStorage.getItem(TOKEN_KEY);
};

export const removeToken = (): void => {
  localStorage.removeItem(TOKEN_KEY);
};

// --- Axios Interceptor ---

// Add a request interceptor to include the token for *all* apiClient requests
apiClient.interceptors.request.use(
  (config) => {
    const token = getToken();
    if (token) {
      // Ensure headers object exists
      config.headers = config.headers || {};
      config.headers.Authorization = `Bearer ${token}`;
      console.log('Adding auth token to request:', config.url);
    } else {
      console.warn('No auth token available for request:', config.url);
    }
    return config;
  },
  (error) => {
    console.error('Request interceptor error:', error);
    return Promise.reject(error);
  }
);

// Add a response interceptor to handle global errors like 401 Unauthorized and 403 Forbidden
apiClient.interceptors.response.use(
  (response) => response, // Simply return successful responses
  (error: AxiosError) => { // Use AxiosError type for better type checking
    // Check if the error has a response and a status code
    if (error.response && (error.response.status === 401 || error.response.status === 403)) {
      console.warn(`Axios Interceptor: ${error.response.status} Error. Logging out.`);
      // Access the logout action directly from the store's state
      // This avoids potential circular dependency issues if importing useConfigStore directly at the top level
      useConfigStore.getState().logout();
      // Optionally, redirect to login page after logout
      // window.location.href = '/login'; // Use with caution, might be disruptive
    }
    // For all errors (including 401/403 after handling), reject the promise so calling code can react
    return Promise.reject(error);
  }
);

export default apiClient; // Export the configured instance
