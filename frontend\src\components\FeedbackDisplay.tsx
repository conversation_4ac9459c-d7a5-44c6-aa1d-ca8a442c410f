import React, { useEffect } from 'react';
import { useConfigStore } from '../store/configStore';

const FeedbackDisplay: React.FC = () => {
  const { 
    isGlobalLoading, 
    globalError, 
    globalSuccess, 
    clearGlobalMessages 
  } = useConfigStore((state) => ({
    isGlobalLoading: state.isGlobalLoading,
    globalError: state.globalError,
    globalSuccess: state.globalSuccess,
    clearGlobalMessages: state.clearGlobalMessages,
  }));

  // Automatically clear messages after a delay
  useEffect(() => {
    let timer: NodeJS.Timeout | null = null;
    if (globalError || globalSuccess) {
      timer = setTimeout(() => {
        clearGlobalMessages();
      }, 3000); // Clear after 3 seconds
    }
    // Cleanup timer on unmount or if messages change before timeout
    return () => {
      if (timer) clearTimeout(timer);
    };
  }, [globalError, globalSuccess, clearGlobalMessages]);

  // Determine message and styling based on state
  let message: string | null = null;
  let messageClass = 'text-gray-500 dark:text-gray-400'; // Default/loading style

  if (isGlobalLoading) {
    message = 'Processing...';
  } else if (globalError) {
    message = globalError;
    messageClass = 'text-red-600 dark:text-red-400';
  } else if (globalSuccess) {
    message = globalSuccess;
    messageClass = 'text-green-600 dark:text-green-400';
  }

  // Render only if there's a message to display
  if (!message) {
    return <div className="h-6"></div>; // Maintain height even when empty
  }

  return (
    <div className={`h-6 text-xs text-center transition-opacity duration-300 ${message ? 'opacity-100' : 'opacity-0'}`}>
      <span className={messageClass}>{message}</span>
    </div>
  );
};

export default FeedbackDisplay;
