import OpenAI from 'openai';
import Anthropic from '@anthropic-ai/sdk';
// Import necessary types from @google/generative-ai - Using GoogleGenerativeAI
import { GoogleGenerativeAI, HarmCategory, HarmBlockThreshold, Content, Part, GenerationConfig, SafetySetting, GenerateContentRequest } from '@google/generative-ai';
import Groq from 'groq-sdk';
import axios from 'axios';
import fs from 'fs/promises'; // Use promises version of fs
import path from 'path';
import { v4 as uuidv4 } from 'uuid'; // For unique filenames
import { IncomingAgentConfig } from '../server'; // Import agent config type
import { LlmError } from '../utils/errors'; // <<< Import custom error

// --- Interfaces for Text Generation ---
export interface LLMServiceInput extends IncomingAgentConfig {
    apiKey?: string;
    systemPrompt?: string;
    // Allow 'tool' role in messages array
    messages: Array<{ role: 'user' | 'assistant' | 'system' | 'tool'; content: string }>;
    imageDataUrl?: string | null;

    // OpenRouter advanced parameters (all optional)
    topK?: number;
    repetitionPenalty?: number;
    minP?: number;
    topA?: number;
    seed?: number;
    logitBias?: Record<string, number>;
    logprobs?: boolean;
    topLogprobs?: number;
    responseFormat?: Record<string, any>;
    structuredOutputs?: boolean;
    stop?: string[] | null;
    tools?: any[];
    toolChoice?: any;
    maxPrice?: Record<string, any>;
}

// Reverted the union type for now to fix compatibility issues
// type FormattedMessage = ... (removed)

export interface LLMStreamChunk {
    type: 'content' | 'error' | 'end'; // Type of chunk
    content?: string; // Text content for 'content' type
    error?: string; // Error message for 'error' type
}

// --- Interfaces for Image Generation ---
export interface ImageGenerationInput {
    provider: string;
    model?: string; // Optional, provider might ignore or use for selection (e.g., Google Imagen vs Gemini)
    prompt: string;
    apiKey?: string;
    isCancelled?: () => boolean; // Optional cancellation check function
    size?: string; // Optional image size (e.g., '1024x1024', '512x512')
    quality?: string; // Optional quality setting (e.g., 'standard', 'hd')
    style?: string; // Optional style setting (e.g., 'vivid', 'natural')
    n?: number; // Optional number of images to generate
}

// --- UPDATE ImageGenerationResult ---
// Note: On error, generateImageWithLLM will now THROW an LlmError instead of returning an error property.
export interface ImageGenerationResult {
  imageUrl?: string; // Original URL (optional, for reference)
  imageDataUrl?: string; // Original Data URL (optional, for reference)
  localImagePath?: string; // <<< --- ADDED: Path relative to the static serving root
  // error?: string; // Removed - errors will be thrown
}


/**
 * Helper function to extract base64 data and mime type from data URL.
 * @param dataUrl - The data URL string (e.g., "data:image/jpeg;base64,...").
 * @returns An object with mimeType and base64Data, or null if parsing fails.
 */
const parseDataUrl = (dataUrl: string): { mimeType: string; base64Data: string } | null => {
    const match = dataUrl.match(/^data:(.+);base64,(.*)$/);
    if (!match) {
        console.warn("[parseDataUrl] Failed to parse data URL format.");
        return null;
    }
    return { mimeType: match[1], base64Data: match[2] };
};

// --- Define Image Save Directory ---
const IMAGE_SAVE_DIR = path.resolve(__dirname, '../../public/generated_images');
const IMAGE_PUBLIC_PATH_PREFIX = '/generated_images';

// Ensure the directory exists on startup or before first save
fs.mkdir(IMAGE_SAVE_DIR, { recursive: true })
  .then(() => console.log(`[Image Service] Ensured image save directory exists: ${IMAGE_SAVE_DIR}`))
  .catch(err => console.error(`[Image Service] Failed to create image save directory ${IMAGE_SAVE_DIR}:`, err));


// --- Helper to Save Image Data ---
async function saveImageData(
  imageData: Buffer,
  mimeType: string
): Promise<string | null> {
  try {
    const extension = mimeType?.split('/')[1]?.toLowerCase() || 'png';
    const validExtensions = ['png', 'jpeg', 'jpg', 'gif', 'webp'];
    const finalExtension = validExtensions.includes(extension) ? extension : 'png';

    const filename = `${uuidv4()}.${finalExtension}`;
    const savePath = path.join(IMAGE_SAVE_DIR, filename);
    await fs.writeFile(savePath, imageData);
    console.log(`[Image Service] Image saved to: ${savePath}`);
    return `${IMAGE_PUBLIC_PATH_PREFIX}/${filename}`;
  } catch (error) {
    console.error('[Image Service] Error saving image data:', error);
    return null; // Return null, let caller handle/throw specific error
  }
}


/**
 * Calls the appropriate LLM API based on the provider and streams the response for text generation.
 * Handles text and vision models where supported.
 *
 * @param input - The input data including provider, model, messages, API key, config, and optional image data.
 * @yields {LLMStreamChunk} Chunks of the response (content or end signal).
 * @throws {LlmError} Throws custom LlmError on failure (API key missing, API call failed, invalid history, etc.).
 */
export async function* callLLMApi(input: LLMServiceInput): AsyncGenerator<LLMStreamChunk> {
    console.log(`[LLM Service] Attempting text generation (streaming): ${input.provider} - ${input.model}`);

    // Log the number of messages by role for debugging
    const toolMessages = input.messages.filter(m => m.role === 'tool').length;
    console.log(`[LLM Service] Input messages: ${input.messages.length} total, ${toolMessages} tool messages`);

    const provider = input.provider.toLowerCase();
    const {
        model,
        messages,
        systemPrompt,
        apiKey: inputApiKey,
        temperature,
        maxTokens,
        topP,
        presencePenalty,
        frequencyPenalty,
        imageDataUrl // Use the renamed property
    } = input;

    // Helper function to format messages for API, including vision data and tool roles
    const formatMessagesForApi = (
        providerName: 'openai' | 'google' | 'anthropic' | 'ollama' | 'other',
        baseMessages: Array<{ role: 'user' | 'assistant' | 'system' | 'tool'; content: string }>,
        imgDataUrl: string | null | undefined
    ): any[] => { // <<< Reverted return type to any[]
        console.log(`[${providerName}] Formatting ${baseMessages.length} messages, including ${baseMessages.filter(m => m.role === 'tool').length} tool messages`);

        // --- Image Handling (Common Logic) ---
        let lastUserMessageIndex = -1;
        if (imgDataUrl) {
            console.log(`[${providerName}] Image data provided for formatting. Length: ${imgDataUrl.length}`);
            console.log(`[${providerName}] Image data first 100 chars: ${imgDataUrl.substring(0, 100)}...`);

            for (let i = baseMessages.length - 1; i >= 0; i--) {
                if (baseMessages[i].role === 'user') {
                    lastUserMessageIndex = i;
                    console.log(`[${providerName}] Found last user message at index ${i}`);
                    break;
                }
            }
            if (lastUserMessageIndex === -1) {
                console.warn(`[${providerName}] Image data provided, but no user message found to attach it to. Sending text only.`);
                imgDataUrl = null; // Reset imgDataUrl if no user message found
            } else {
                console.log(`[${providerName}] Will attach image to user message at index ${lastUserMessageIndex}`);
            }
        } else {
            console.log(`[${providerName}] No image data provided for formatting.`);
        }

        // --- Provider-Specific Formatting ---
        switch (providerName) {
            case 'openai': // Also covers Groq, DeepSeek, LM Studio
                const formattedMessages = baseMessages
                    .map(msg => {
                        // Convert 'tool' role to 'user' role to include search results
                        if (msg.role === 'tool') {
                            return { ...msg, role: 'user' };
                        }
                        return msg;
                    })
                    // Remove this filter to ensure all messages are included
                    // .filter(msg => msg.role !== 'tool') // Filter out any remaining tool messages
                    .map((msg, index) => {
                        const messagePayload: OpenAI.Chat.Completions.ChatCompletionMessageParam = {
                            role: msg.role as 'user' | 'assistant' | 'system',
                            content: msg.content,
                        };
                        // Attach image if this is the last user message and image data exists
                        if (msg.role === 'user' && index === lastUserMessageIndex && imgDataUrl) {
                            console.log(`[${providerName}] Attaching image to message at index ${index}`);
                            messagePayload.content = [
                                { type: 'text', text: msg.content },
                                { type: 'image_url', image_url: { url: imgDataUrl } }
                            ];
                            console.log(`[${providerName}] Successfully formatted message with image`);
                        }
                        return messagePayload;
                    });

                console.log(`[${providerName}] Formatted ${formattedMessages.length} messages for API call`);
                return formattedMessages;

            case 'google':
                const googleMessages = baseMessages
                    .map(msg => {
                        // Convert 'tool' role to 'user' role to include search results
                        if (msg.role === 'tool') {
                            return { ...msg, role: 'user' };
                        }
                        return msg;
                    })
                    // Keep system messages but filter out any remaining tool messages
                    .filter(msg => msg.role === 'user' || msg.role === 'assistant' || msg.role === 'system')
                    .map((msg, index) => {
                        const parts: Part[] = [{ text: msg.content }];
                        // Attach image if this is the last user message and image data exists
                        if (msg.role === 'user' && index === lastUserMessageIndex && imgDataUrl) {
                            const parsedImage = parseDataUrl(imgDataUrl);
                            if (parsedImage) {
                                parts.push({
                                    inlineData: {
                                        mimeType: parsedImage.mimeType,
                                        data: parsedImage.base64Data
                                    }
                                });
                            } else {
                                console.warn("[Google] Could not parse image data URL for vision message.");
                            }
                        }
                        return {
                            role: msg.role === 'user' ? 'user' : 'model',
                            parts: parts
                        };
                    });

                console.log(`[${providerName}] Formatted ${googleMessages.length} messages for API call`);
                return googleMessages;

            case 'anthropic':
                const validAnthropicMimeTypes: ReadonlySet<string> = new Set(['image/jpeg', 'image/png', 'image/gif', 'image/webp']);
                // Convert 'tool' messages to 'user' messages for Anthropic to include search results
                const anthropicMessages = baseMessages
                    .map(msg => {
                        // Convert 'tool' role to 'user' role to include search results
                        if (msg.role === 'tool') {
                            return { ...msg, role: 'user' };
                        }
                        return msg;
                    })
                    // Keep only user and assistant messages as Anthropic requires
                    .filter(msg => msg.role === 'user' || msg.role === 'assistant')
                    .map((msg, index) => {
                        // Attach image if this is the last user message and image data exists
                        if (msg.role === 'user' && index === lastUserMessageIndex && imgDataUrl) {
                            const parsedImage = parseDataUrl(imgDataUrl);
                            if (parsedImage && validAnthropicMimeTypes.has(parsedImage.mimeType)) {
                                return {
                                    role: msg.role as 'user' | 'assistant',
                                    content: [
                                        { type: 'image', source: { type: 'base64', media_type: parsedImage.mimeType as Anthropic.Base64ImageSource['media_type'], data: parsedImage.base64Data } },
                                        { type: 'text', text: msg.content }
                                    ]
                                };
                            } else {
                                if (parsedImage) console.warn(`[Anthropic] Invalid image mime type: ${parsedImage.mimeType}. Sending text only.`);
                                else console.warn("[Anthropic] Could not parse image data URL. Sending text only.");
                                return { role: msg.role as 'user' | 'assistant', content: msg.content }; // Fallback to text only
                            }
                        } else {
                            return { role: msg.role as 'user' | 'assistant', content: msg.content };
                        }
                    });

                console.log(`[${providerName}] Formatted ${anthropicMessages.length} messages for API call`);
                return anthropicMessages;

            case 'ollama':
                 const ollamaMessages = baseMessages
                     .map(msg => {
                         // Convert 'tool' role to 'user' role to include search results
                         if (msg.role === 'tool') {
                             return { ...msg, role: 'user' };
                         }
                         return msg;
                     })
                     .filter(msg => ['user', 'assistant', 'system'].includes(msg.role))
                     .map(msg => ({ role: msg.role, content: msg.content }));

                 console.log(`[${providerName}] Formatted ${ollamaMessages.length} messages for API call`);
                 return ollamaMessages;

            default: // Fallback / 'other' - Treat like OpenAI
                const defaultMessages = baseMessages
                    .map(msg => {
                        // Convert 'tool' role to 'user' role to include search results
                        if (msg.role === 'tool') {
                            return { ...msg, role: 'user' };
                        }
                        return msg;
                    })
                    // Remove this filter to ensure all messages are included
                    // .filter(msg => msg.role !== 'tool')
                    .map(msg => ({
                        role: msg.role as 'user' | 'assistant' | 'system',
                        content: msg.content
                    }));

                console.log(`[${providerName}] Formatted ${defaultMessages.length} messages for API call`);
                return defaultMessages;
        }
    };

    // Helper to check if a model name suggests vision capabilities
    const supportsVision = (provider: string, modelName: string): boolean => {
        const lowerModel = modelName.toLowerCase();
        let result = false;

        switch (provider) {
            case 'openai':
                result = lowerModel.includes('vision') || lowerModel.includes('gpt-4o') || lowerModel.includes('gpt-4-turbo');
                console.log(`[${provider}] Checking vision support for model ${modelName}: ${result ? 'SUPPORTED' : 'NOT SUPPORTED'}`);
                return result;
            case 'google':
                result = lowerModel.includes('vision') || lowerModel.includes('gemini');
                console.log(`[${provider}] Checking vision support for model ${modelName}: ${result ? 'SUPPORTED' : 'NOT SUPPORTED'}`);
                return result;
            case 'anthropic':
                result = lowerModel.includes('claude-3');
                console.log(`[${provider}] Checking vision support for model ${modelName}: ${result ? 'SUPPORTED' : 'NOT SUPPORTED'}`);
                return result;
            default:
                console.log(`[${provider}] Unknown provider, assuming NO vision support for model ${modelName}`);
                return false;
        }
    };

    // --- OpenAI ---
    if (provider === 'openai') {
        const apiKey = inputApiKey || process.env.OPENAI_API_KEY;
        if (!apiKey) {
             throw new LlmError('OpenAI API Key is missing.', 'API_KEY_MISSING');
        }
        const openai = new OpenAI({ apiKey });

        try {
            let messagesForApi = formatMessagesForApi('openai', messages, imageDataUrl);

            // Type-safe check for system prompt existence
            if (systemPrompt && !messagesForApi.some(m => m?.role === 'system')) {
                 messagesForApi.unshift({ role: 'system', content: systemPrompt });
            }
            if (imageDataUrl && !supportsVision('openai', model)) {
                 console.warn(`[OpenAI] Image data provided for non-vision model (${model}). Sending text only.`);
                 messagesForApi = formatMessagesForApi('openai', messages, null);
                 // Type-safe check for system prompt existence again after reformatting
                 if (systemPrompt && !messagesForApi.some(m => m?.role === 'system')) {
                       messagesForApi.unshift({ role: 'system', content: systemPrompt });
                  }
            }

            // Log the final messages being sent to the API
            console.log(`[OpenAI] Sending ${messagesForApi.length} messages to API, including ${messagesForApi.filter(m => m.role === 'system').length} system messages`);

            const stream = await openai.chat.completions.create({
                model: model,
                messages: messagesForApi as OpenAI.Chat.Completions.ChatCompletionMessageParam[],
                stream: true,
                temperature: temperature ?? undefined,
                max_tokens: maxTokens ?? undefined,
                top_p: topP ?? undefined,
                presence_penalty: presencePenalty ?? undefined,
                frequency_penalty: frequencyPenalty ?? undefined,
            });

            for await (const chunk of stream) {
                const content = chunk.choices[0]?.delta?.content || '';
                if (content) {
                    yield { type: 'content', content: content };
                }
            }
            yield { type: 'end' };

        } catch (error: any) {
            console.error('[OpenAI] API stream failed:', error);
            throw new LlmError(`OpenAI API call failed: ${error.response?.data?.error?.message || error.message}`, 'API_CALL_FAILED', error);
        }
    }

    // --- OpenRouter ---
    if (provider === 'openrouter') {
        const apiKey = inputApiKey;
        if (!apiKey) {
            throw new LlmError('OpenRouter API Key is missing.', 'API_KEY_MISSING');
        }
        try {
            let messagesForApi = formatMessagesForApi('openai', messages, imageDataUrl);
            if (systemPrompt && !messagesForApi.some(m => m?.role === 'system')) {
                messagesForApi.unshift({ role: 'system', content: systemPrompt });
            }
            // Collect all extra parameters from input
            const {
                topK, // top_k
                repetitionPenalty,
                minP,
                topA,
                seed,
                logitBias,
                logprobs,
                topLogprobs,
                responseFormat,
                structuredOutputs,
                stop,
                tools,
                toolChoice,
                maxPrice
                // Removed unused rest parameter
            } = input;

            // Build request body with only allowed OpenAI/OpenRouter fields
            const requestBody: any = {
                model,
                messages: messagesForApi,
                stream: true,
                temperature: temperature ?? undefined,
                max_tokens: maxTokens ?? undefined,
                top_p: topP ?? undefined,
                presence_penalty: presencePenalty ?? undefined,
                frequency_penalty: frequencyPenalty ?? undefined,
                // OpenRouter advanced params
                top_k: topK ?? undefined,
                repetition_penalty: repetitionPenalty ?? undefined,
                min_p: minP ?? undefined,
                top_a: topA ?? undefined,
                seed: seed ?? undefined,
                logit_bias: logitBias ?? undefined,
                logprobs: logprobs ?? undefined,
                top_logprobs: topLogprobs ?? undefined,
                response_format: responseFormat ?? undefined,
                structured_outputs: structuredOutputs ?? undefined,
                stop: stop ?? undefined,
                tools: tools ?? undefined,
                tool_choice: toolChoice ?? undefined,
                max_price: maxPrice ?? undefined
            };

            const response = await axios.post(
                'https://openrouter.ai/api/v1/chat/completions',
                requestBody,
                {
                    headers: {
                        'Authorization': `Bearer ${apiKey}`,
                        'Content-Type': 'application/json',
                        'Accept': 'text/event-stream'
                    },
                    responseType: 'stream'
                }
            );

            // Stream SSE response
            // Robust SSE stream parser: handle multiple lines and ignore blank lines
            let buffer = '';
            for await (const chunk of response.data) {
                buffer += chunk.toString('utf-8');
                let lines = buffer.split('\n');
                // Keep the last line in buffer if not complete
                buffer = lines.pop() ?? '';
                for (const line of lines) {
                    const trimmed = line.trim();
                    if (!trimmed.startsWith('data: ')) continue;
                    const jsonData = trimmed.substring(6);
                    if (jsonData === '[DONE]') {
                        yield { type: 'end' };
                        return;
                    }
                    try {
                        const chunkObj = JSON.parse(jsonData);
                        const content = chunkObj.choices?.[0]?.delta?.content || '';
                        if (content) {
                            yield { type: 'content', content: content };
                        }
                    } catch (parseError) {
                        console.warn('[OpenRouter] Stream: Failed to parse JSON chunk:', jsonData, parseError);
                    }
                }
                // Add a small delay to prevent overwhelming the server
                await new Promise(resolve => setTimeout(resolve, 10));
            }
            yield { type: 'end' };
        } catch (error: any) {
            console.error('[OpenRouter] API stream failed:', error?.response?.data || error.message);
            throw new LlmError(`OpenRouter API call failed: ${error.response?.data?.error?.message || error.message}`, 'API_CALL_FAILED', error);
        }
    }

    // --- Anthropic ---
    else if (provider === 'anthropic') {
        const apiKey = inputApiKey || process.env.ANTHROPIC_API_KEY;
        if (!apiKey) {
            throw new LlmError('Anthropic API Key is missing.', 'API_KEY_MISSING');
        }
        const anthropic = new Anthropic({ apiKey });

        try {
            let messagesForApi = formatMessagesForApi('anthropic', messages, imageDataUrl); // Pass imageDataUrl

             messagesForApi = messagesForApi
                 .map(msg => ({ ...msg, content: typeof msg.content === 'string' ? msg.content.trim() : msg.content }))
                 .filter(msg => typeof msg.content === 'string' ? msg.content : (Array.isArray(msg.content) && msg.content.length > 0));

            if (messagesForApi.length > 0 && messagesForApi[0].role !== 'user') {
                const firstUserIndex = messagesForApi.findIndex(m => m.role === 'user');
                if (firstUserIndex !== -1) {
                    console.log(`[Anthropic History Fix] Removing ${firstUserIndex} leading assistant message(s).`);
                    messagesForApi.splice(0, firstUserIndex);
                } else {
                    console.warn("[Anthropic History Fix] No user messages found after filtering. History is invalid.");
                    messagesForApi = [];
                }
            }

             if (!messagesForApi.length) {
                  throw new LlmError("Anthropic requires at least one message, and the history must start with a 'user' role after filtering.", 'INVALID_HISTORY');
             }

             if (imageDataUrl && !supportsVision('anthropic', model)) { // Check imageDataUrl
                  console.warn(`[Anthropic] Image data provided for non-vision model (${model}). Sending text only.`);
                  messagesForApi = formatMessagesForApi('anthropic', messages, null);
                  if (messagesForApi.length > 0 && messagesForApi[0].role !== 'user') {
                       const firstUserIndex = messagesForApi.findIndex(m => m.role === 'user');
                       if (firstUserIndex !== -1) messagesForApi.splice(0, firstUserIndex);
                       else messagesForApi = [];
                  }
                  if (!messagesForApi.length) {
                       throw new LlmError("Anthropic history became invalid after removing image data for non-vision model.", 'INVALID_HISTORY');
                  }
             }

             let systemParam: string | undefined = systemPrompt;
             console.log(`[Anthropic] Using system parameter for system prompt (if provided).`);

            // Log the final messages being sent to the API
            console.log(`[Anthropic] Sending ${messagesForApi.length} messages to API with system prompt: ${systemParam ? 'present' : 'not present'}`);

            const stream = await anthropic.messages.create({
                model: model,
                system: systemParam,
                messages: messagesForApi as Anthropic.Messages.MessageParam[],
                max_tokens: maxTokens ?? 4096,
                temperature: temperature ?? undefined,
                top_p: topP ?? undefined,
                stream: true,
            });

            for await (const event of stream) {
                if (event.type === 'content_block_delta' && event.delta.type === 'text_delta') {
                    yield { type: 'content', content: event.delta.text };
                } else if (event.type === 'message_delta' && event.delta.stop_reason) {
                     console.log(`[Anthropic] Stream finished. Reason: ${event.delta.stop_reason}`);
                } else if (event.type === 'message_start') {
                     console.log(`[Anthropic] Stream started. Role: ${event.message.role}`);
                }
            }
             yield { type: 'end' };

        } catch (error: any) {
            console.error('[Anthropic] API stream failed:', error);
            throw new LlmError(`Anthropic API call failed: ${error.message}`, 'API_CALL_FAILED', error);
        }
    }

    // --- Google ---
     else if (provider === 'google') {
        const apiKey = inputApiKey || process.env.GOOGLE_API_KEY;
         if (!apiKey) {
            throw new LlmError('Google API Key is missing.', 'API_KEY_MISSING');
        }
        const genAI = new GoogleGenerativeAI(apiKey);
        const generationConfig: GenerationConfig = {
            maxOutputTokens: maxTokens ?? undefined,
            temperature: temperature ?? undefined,
            topP: topP ?? undefined,
        };

        try {
             let historyForApi: Content[] = formatMessagesForApi('google', messages, imageDataUrl); // Pass imageDataUrl

             if (imageDataUrl && !supportsVision('google', model)) { // Check imageDataUrl
                  console.warn(`[Google] Image data provided for potentially non-vision model (${model}). Sending text only.`);
                  historyForApi = formatMessagesForApi('google', messages, null);
             }

            if (!historyForApi || historyForApi.length === 0) {
                 const hasUserOrAssistantContent = messages.some(m => (m.role === 'user' || m.role === 'assistant') && m.content.trim());
                 let errorMsg = "Google GenAI: Cannot start chat with empty message history.";
                 if (hasUserOrAssistantContent) {
                    errorMsg = "Google GenAI requires at least one user or assistant message.";
                 } else if (messages.length > 0) {
                      errorMsg = "Google GenAI: Cannot start chat with only a system prompt via history. Use systemInstruction.";
                 }
                 throw new LlmError(errorMsg, 'INVALID_HISTORY');
            }

            const lastMessage = historyForApi.pop();
             if (!lastMessage) {
                  throw new LlmError("Google GenAI: Failed to extract last message for sending.", 'INTERNAL_ERROR');
             }
            const lastMessageParts = lastMessage.parts;

            // Log the final messages being sent to the API
            console.log(`[Google] Sending ${historyForApi.length} history messages to API with system prompt: ${systemPrompt ? 'present' : 'not present'}`);

            const googleModel = genAI.getGenerativeModel({
                model: model,
                ...(systemPrompt && { systemInstruction: systemPrompt }),
                generationConfig: generationConfig,
            });

            const chat = googleModel.startChat({
                history: historyForApi,
            });

            const result = await chat.sendMessageStream(lastMessageParts);

            for await (const chunk of result.stream) {
                 const chunkText = chunk.text();
                 if (chunkText) {
                     yield { type: 'content', content: chunkText };
                 }
            }
             yield { type: 'end' };

        } catch (error: any) {
            console.error('[Google GenAI] API stream failed:', error);
            const message = error.message || 'Unknown Google GenAI error';
            const details = error.details || (error.response?.data?.error?.message);
             throw new LlmError(`Google GenAI API call failed: ${message}${details ? ` (${details})` : ''}`, 'API_CALL_FAILED', error);
        }
    }

    // --- Groq ---
     else if (provider === 'groq') {
        const apiKey = inputApiKey || process.env.GROQ_API_KEY;
         if (!apiKey) {
            throw new LlmError('Groq API Key is missing.', 'API_KEY_MISSING');
        }
        const groq = new Groq({ apiKey });

        try {
             if (imageDataUrl) {
                 console.warn("[Groq] Image data provided but Groq does not support vision. Sending text only.");
             }
            let messagesForApi = formatMessagesForApi('openai', messages, null); // Use let

            // Type-safe check for system prompt existence
            if (systemPrompt && !messagesForApi.some(m => m?.role === 'system')) {
                 messagesForApi.unshift({ role: 'system', content: systemPrompt });
            }

             if (messagesForApi.length === 0 || !messagesForApi.some(m => m.role === 'user')) {
                  throw new LlmError("Groq requires messages and at least one user message.", 'INVALID_HISTORY');
             }

            const stream = await groq.chat.completions.create({
                model: model,
                messages: messagesForApi,
                temperature: temperature ?? undefined,
                max_tokens: maxTokens ?? 8192,
                top_p: topP ?? undefined,
                stream: true,
            });

             for await (const chunk of stream) {
                const content = chunk.choices[0]?.delta?.content || '';
                if (content) {
                    yield { type: 'content', content: content };
                }
            }
             yield { type: 'end' };

        } catch (error: any) {
            console.error('[Groq] API stream failed:', error);
             throw new LlmError(`Groq API call failed: ${error.response?.data?.error?.message || error.message}`, 'API_CALL_FAILED', error);
        }
    }

    // --- Ollama ---
    else if (provider === 'ollama') {
        // Use IPv4 address explicitly to avoid IPv6 connection issues
        const ollamaBaseUrl = process.env.OLLAMA_BASE_URL || 'http://127.0.0.1:11434';
        // Get the timeout setting from environment variable or use default (60 seconds)
        const ollamaTimeout = parseInt(process.env.OLLAMA_REQUEST_TIMEOUT || '60000', 10);

        console.log(`[Ollama] Using request timeout of ${ollamaTimeout}ms for model: ${model}`);

        try {
             if (imageDataUrl) { // Check imageDataUrl
                 console.warn("[Ollama] Image data provided. Vision support depends on the specific Ollama model and configuration. Attempting text-only for now.");
             }

             let messagesForApi = formatMessagesForApi('ollama', messages, null); // Use let

             // Type-safe check for system prompt existence
             if (systemPrompt && !messagesForApi.some(m => m?.role === 'system')) {
                  messagesForApi.unshift({ role: 'system', content: systemPrompt });
             }

             if (messagesForApi.length === 0) {
                  throw new LlmError("Ollama requires messages.", 'INVALID_HISTORY');
             }

            const ollamaOptions: Record<string, any> = {};
            if (temperature !== undefined) ollamaOptions.temperature = temperature;
            if (topP !== undefined) ollamaOptions.top_p = topP;
            if (maxTokens !== undefined) ollamaOptions.num_predict = maxTokens;
            if (presencePenalty !== undefined) ollamaOptions.presence_penalty = presencePenalty;
            if (frequencyPenalty !== undefined) ollamaOptions.frequency_penalty = frequencyPenalty;

            const response = await axios.post(`${ollamaBaseUrl}/api/chat`, {
                model: model,
                messages: messagesForApi,
                stream: true,
                options: ollamaOptions
            }, {
                responseType: 'stream',
                timeout: ollamaTimeout // Apply the timeout setting
            });

            let accumulatedError = '';
            for await (const line of response.data) {
                 try {
                     const lineStr = line.toString('utf-8').trim();
                     if (lineStr) {
                         const jsonChunk = JSON.parse(lineStr);
                         if (jsonChunk.error) {
                             console.error('[Ollama] Stream returned an error:', jsonChunk.error);
                             accumulatedError += (accumulatedError ? '\n' : '') + jsonChunk.error;
                         }
                         const content = jsonChunk?.message?.content || '';
                         if (content) {
                             yield { type: 'content', content: content };
                         }
                         if (jsonChunk?.done) {
                             console.log('[Ollama] Stream indicated done.');
                             break;
                         }
                     }
                 } catch (parseError) {
                      console.warn('[Ollama] Stream: Failed to parse JSON chunk:', line.toString('utf-8'), parseError);
                 }
            }

             if (accumulatedError) {
                  throw new LlmError(`Ollama stream error: ${accumulatedError}`, 'STREAM_ERROR');
             }
             yield { type: 'end' };

        } catch (error: any) {
            console.error('[Ollama] API request failed:', error.response?.data || error.message);
            let errorToThrow: LlmError;
            if (axios.isAxiosError(error) && error.code === 'ECONNREFUSED') {
                 errorToThrow = new LlmError(`Ollama connection failed. Ensure Ollama is running at ${ollamaBaseUrl}`, 'CONNECTION_FAILED', error);
            } else if (axios.isAxiosError(error) && error.code === 'ETIMEDOUT' || error.message?.includes('timeout')) {
                 // Handle timeout errors specifically
                 const timeoutMs = parseInt(process.env.OLLAMA_REQUEST_TIMEOUT || '60000', 10);
                 const timeoutSec = Math.round(timeoutMs / 1000);
                 errorToThrow = new LlmError(
                    `Ollama model loading timed out after ${timeoutSec} seconds. This may happen when switching to a new model that needs to be downloaded or loaded into memory. Try increasing the OLLAMA_REQUEST_TIMEOUT setting in your .env file.`,
                    'MODEL_LOADING_TIMEOUT',
                    error
                 );
            } else if (axios.isAxiosError(error)) {
                 const ollamaError = error.response?.data?.error || error.message;
                 errorToThrow = new LlmError(`Ollama API call failed: ${ollamaError}`, 'API_CALL_FAILED', error);
            } else {
                 errorToThrow = new LlmError(`Ollama API call failed: ${error.message}`, 'API_CALL_FAILED', error);
            }
            throw errorToThrow;
        }
    }
    // --- DeepSeek ---
    else if (provider === 'deepseek') {
        const apiKey = inputApiKey || process.env.DEEPSEEK_API_KEY;
        const deepseekApiUrl = 'https://api.deepseek.com/chat/completions';

        if (!apiKey) {
            throw new LlmError('DeepSeek API Key is missing.', 'API_KEY_MISSING');
        }

        try {
            if (imageDataUrl) { // Check imageDataUrl
                 console.warn("[DeepSeek] Image data provided but DeepSeek does not support vision. Sending text only.");
            }
            let messagesForApi = formatMessagesForApi('openai', messages, null); // Use let

            // Type-safe check for system prompt existence
            if (systemPrompt && !messagesForApi.some(m => m?.role === 'system')) {
                 messagesForApi.unshift({ role: 'system', content: systemPrompt });
            }

             if (messagesForApi.length === 0) {
                  throw new LlmError("DeepSeek requires messages.", 'INVALID_HISTORY');
             }

            const response = await axios.post(deepseekApiUrl, {
                model: model,
                messages: messagesForApi,
                temperature: temperature ?? 0.7,
                max_tokens: maxTokens ?? undefined,
                top_p: topP ?? undefined,
                presence_penalty: presencePenalty ?? undefined,
                frequency_penalty: frequencyPenalty ?? undefined,
                stream: true,
            }, {
                headers: {
                    'Authorization': `Bearer ${apiKey}`,
                    'Content-Type': 'application/json',
                    'Accept': 'text/event-stream',
                },
                responseType: 'stream'
            });

            // Stream SSE response with buffer-based approach for robust parsing
            let buffer = '';
            for await (const chunk of response.data) {
                buffer += chunk.toString('utf-8');
                let lines = buffer.split('\n');
                // Keep the last line in buffer if not complete
                buffer = lines.pop() ?? '';

                for (const line of lines) {
                    const trimmed = line.trim();
                    if (!trimmed.startsWith('data: ')) continue;

                    const jsonData = trimmed.substring(6);
                    if (jsonData === '[DONE]') {
                        console.log('[DeepSeek] Stream indicated [DONE].');
                        yield { type: 'end' };
                        return;
                    }

                    try {
                        const chunk = JSON.parse(jsonData);
                        const content = chunk.choices?.[0]?.delta?.content || '';
                        if (content) {
                            yield { type: 'content', content: content };
                        }
                    } catch (parseError) {
                        console.warn('[DeepSeek] Stream: Failed to parse JSON chunk:', jsonData, parseError);
                    }
                }
            }

            // Process any remaining data in the buffer
            if (buffer.trim()) {
                const trimmed = buffer.trim();
                if (trimmed.startsWith('data: ')) {
                    const jsonData = trimmed.substring(6);
                    if (jsonData !== '[DONE]') {
                        try {
                            const chunk = JSON.parse(jsonData);
                            const content = chunk.choices?.[0]?.delta?.content || '';
                            if (content) {
                                yield { type: 'content', content: content };
                            }
                        } catch (parseError) {
                            console.warn('[DeepSeek] Stream: Failed to parse final JSON chunk:', jsonData, parseError);
                        }
                    }
                }
            }
             yield { type: 'end' };

        } catch (error: any) {
            console.error('[DeepSeek] API stream failed:', error.response?.data || error.message);
            let errorMessage = 'Unknown error';

            if (axios.isAxiosError(error)) {
                if (error.code === 'ECONNREFUSED') {
                    errorMessage = 'Connection to DeepSeek API failed. Please check your internet connection.';
                } else if (error.response?.data?.error?.message) {
                    errorMessage = error.response.data.error.message;
                } else if (error.message) {
                    errorMessage = error.message;
                }
            } else if (error.message) {
                errorMessage = error.message;
            }

            throw new LlmError(`DeepSeek API call failed: ${errorMessage}`, 'API_CALL_FAILED', error);
        }
    }
    // --- LM Studio (Local) ---
     else if (provider === 'lm studio') {
        // Use IPv4 address explicitly to avoid IPv6 connection issues
        const lmStudioBaseUrl = process.env.LMSTUDIO_BASE_URL || 'http://127.0.0.1:1234/v1';
        // Get the timeout setting from environment variable or use default (30 seconds)
        const lmStudioTimeout = parseInt(process.env.LMSTUDIO_REQUEST_TIMEOUT || '30000', 10);

        console.log(`[LM Studio] Using request timeout of ${lmStudioTimeout}ms for model: ${model}`);

        try {
            if (imageDataUrl) { // Check imageDataUrl
                 console.warn("[LM Studio] Image data provided. Vision support depends on the loaded model. Sending text only for now.");
            }
            let messagesForApi = formatMessagesForApi('openai', messages, null); // Use let

            // Type-safe check for system prompt existence
            if (systemPrompt && !messagesForApi.some(m => m?.role === 'system')) {
                 messagesForApi.unshift({ role: 'system', content: systemPrompt });
            }

             if (messagesForApi.length === 0) {
                  throw new LlmError("LM Studio requires messages.", 'INVALID_HISTORY');
             }

            const response = await axios.post(`${lmStudioBaseUrl}/chat/completions`, {
                messages: messagesForApi,
                temperature: temperature ?? 0.7,
                max_tokens: maxTokens ?? -1,
                top_p: topP ?? undefined,
                presence_penalty: presencePenalty ?? undefined,
                frequency_penalty: frequencyPenalty ?? undefined,
                stream: true,
            }, {
                 headers: {
                     'Content-Type': 'application/json',
                     'Accept': 'text/event-stream',
                 },
                 responseType: 'stream',
                 timeout: lmStudioTimeout // Apply the timeout setting
             });

            // Stream SSE response with buffer-based approach for robust parsing
            let buffer = '';
            for await (const chunk of response.data) {
                buffer += chunk.toString('utf-8');
                let lines = buffer.split('\n');
                // Keep the last line in buffer if not complete
                buffer = lines.pop() ?? '';

                for (const line of lines) {
                    const trimmed = line.trim();
                    if (!trimmed.startsWith('data: ')) continue;

                    const jsonData = trimmed.substring(6);
                    if (jsonData === '[DONE]') {
                        console.log('[LM Studio] Stream indicated [DONE].');
                        yield { type: 'end' };
                        return;
                    }

                    try {
                        const chunk = JSON.parse(jsonData);
                        const content = chunk.choices?.[0]?.delta?.content || '';
                        if (content) {
                            yield { type: 'content', content: content };
                        }
                    } catch (parseError) {
                        console.warn('[LM Studio] Stream: Failed to parse JSON chunk:', jsonData, parseError);
                    }
                }
            }

            // Process any remaining data in the buffer
            if (buffer.trim()) {
                const trimmed = buffer.trim();
                if (trimmed.startsWith('data: ')) {
                    const jsonData = trimmed.substring(6);
                    if (jsonData !== '[DONE]') {
                        try {
                            const chunk = JSON.parse(jsonData);
                            const content = chunk.choices?.[0]?.delta?.content || '';
                            if (content) {
                                yield { type: 'content', content: content };
                            }
                        } catch (parseError) {
                            console.warn('[LM Studio] Stream: Failed to parse final JSON chunk:', jsonData, parseError);
                        }
                    }
                }
            }
             yield { type: 'end' };

        } catch (error: any) {
            console.error('[LM Studio] API request failed:', error.response?.data || error.message);
             let errorToThrow: LlmError;
             if (axios.isAxiosError(error) && error.code === 'ECONNREFUSED') {
                 errorToThrow = new LlmError(`LM Studio connection failed. Ensure LM Studio server is running and accessible at ${lmStudioBaseUrl}`, 'CONNECTION_FAILED', error);
            } else if (axios.isAxiosError(error) && error.code === 'ETIMEDOUT' || error.message?.includes('timeout')) {
                 // Handle timeout errors specifically
                 const timeoutMs = parseInt(process.env.LMSTUDIO_REQUEST_TIMEOUT || '30000', 10);
                 const timeoutSec = Math.round(timeoutMs / 1000);
                 errorToThrow = new LlmError(
                    `LM Studio model loading timed out after ${timeoutSec} seconds. This may happen when switching to a new model that needs to be loaded into memory. Try increasing the LMSTUDIO_REQUEST_TIMEOUT setting in your .env file.`,
                    'MODEL_LOADING_TIMEOUT',
                    error
                 );
            } else if (axios.isAxiosError(error)) {
                 errorToThrow = new LlmError(`LM Studio API call failed: ${error.response?.data?.error?.message || error.message}`, 'API_CALL_FAILED', error);
            } else {
                 errorToThrow = new LlmError(`LM Studio API call failed: ${error.message}`, 'API_CALL_FAILED', error);
            }
            throw errorToThrow;
        }
    }

    // --- Unsupported Provider ---
    else {
        console.error(`[LLM Service] Unsupported provider: ${input.provider}`);
        throw new LlmError(`Unsupported LLM provider: ${input.provider}`, 'UNSUPPORTED_PROVIDER');
     }
};


/**
 * Generates an image using the specified provider (currently OpenAI DALL-E or Google Imagen/Gemini).
 *
 * @param input - The input data including provider, prompt, API key, and optionally model.
 * @returns A promise resolving to an object containing image URL/data or an error message.
 * @throws {LlmError} Throws custom LlmError on failure.
 */
export async function generateImageWithLLM(input: ImageGenerationInput): Promise<ImageGenerationResult> {
    console.log(`[Image Service] Attempting image generation: ${input.provider} - Model: ${input.model || 'Default'} - Prompt: ${input.prompt.substring(0, 50)}...`);
    const provider = input.provider.toLowerCase();
    const {
        prompt,
        apiKey: inputApiKey,
        model: inputModel,
        isCancelled = () => false,
        size = '1024x1024',
        quality = 'standard',
        style = 'vivid',
        n = 1
    } = input;

    // --- OpenAI DALL-E ---
    if (provider === 'openai') {
        const apiKey = inputApiKey || process.env.OPENAI_API_KEY;
        if (!apiKey) {
            throw new LlmError('OpenAI API Key is missing for image generation.', 'API_KEY_MISSING');
        }
        const openai = new OpenAI({ apiKey });
        try {
            const modelToUse = "dall-e-3";
            console.log(`[OpenAI Image Gen] Using model: ${modelToUse}. (Input model '${inputModel}' ignored)`);

            if (isCancelled()) throw new LlmError('Image generation cancelled by user.', 'CANCELLED');

            const response = await openai.images.generate({
                model: modelToUse,
                prompt: prompt,
                n: n,
                size: size as '1024x1024' | '1024x1792' | '1792x1024' | '512x512' | '256x256',
                quality: quality as 'standard' | 'hd',
                style: style as 'vivid' | 'natural',
                response_format: "url",
            });

            console.log(`[OpenAI Image Gen] Generated image with size: ${size}, quality: ${quality}, style: ${style}`);

            const imageUrl = response.data[0]?.url;
            if (imageUrl) {
                console.log(`[OpenAI Image Gen] Success. Image URL generated: ${imageUrl}`);
                const abortController = new AbortController();
                const timeoutId = setTimeout(() => {
                    if (isCancelled()) {
                        console.log('[OpenAI Image Download] Aborting download due to cancellation signal.');
                        abortController.abort();
                    }
                }, 100);

                try {
                    if (isCancelled()) { clearTimeout(timeoutId); throw new LlmError('Image generation cancelled by user before download.', 'CANCELLED'); }

                    const imageResponse = await axios.get(imageUrl, {
                        responseType: 'arraybuffer',
                        signal: abortController.signal
                    });
                    clearTimeout(timeoutId);

                    const imageData = Buffer.from(imageResponse.data);
                    const mimeType = imageResponse.headers['content-type'] || 'image/png';

                    const localPath = await saveImageData(imageData, mimeType);
                    if (localPath) {
                        return { imageUrl, localImagePath: localPath };
                    } else {
                        console.error('[OpenAI Image Gen] Failed to save downloaded image locally.');
                        throw new LlmError('Failed to save downloaded image locally.', 'IMAGE_SAVE_FAILED');
                    }
                } catch (downloadError: any) {
                     clearTimeout(timeoutId);
                     if (axios.isCancel(downloadError)) {
                         console.log('[OpenAI Image Download] Download cancelled.');
                         throw new LlmError('Image download cancelled by user.', 'DOWNLOAD_CANCELLED');
                     }
                     console.error('[OpenAI Image Gen] Failed to download image from URL:', imageUrl, downloadError);
                     throw new LlmError(`Failed to download image from URL: ${downloadError.message}`, 'DOWNLOAD_FAILED', downloadError);
                }
            } else {
                console.error('[OpenAI Image Gen] API call succeeded but no image URL was returned.', response);
                throw new LlmError('OpenAI image generation did not return an image URL.', 'API_NO_URL');
            }
        } catch (error: any) {
             if (error instanceof LlmError) throw error; // Re-throw specific errors
            console.error('[OpenAI Image Gen] Failed:', error);
            throw new LlmError(`OpenAI image generation failed: ${error.response?.data?.error?.message || error.message}`, 'API_CALL_FAILED', error);
        }
    }

    // --- Google (Imagen / Gemini) ---
    else if (provider === 'google') {
        const apiKey = inputApiKey || process.env.GOOGLE_API_KEY;
        if (!apiKey) {
            throw new LlmError('Google API Key is missing for image generation.', 'API_KEY_MISSING');
        }
        const genAI = new GoogleGenerativeAI(apiKey);

        const modelToUse = inputModel || 'imagen-3.0-generate-002';
        const isImagenModel = modelToUse.startsWith('imagen');
        console.log(`[Google Image Gen] Using model: ${modelToUse} (${isImagenModel ? 'Imagen API' : 'Gemini API'})`);

        try {
            const safetySettings: SafetySetting[] = [
                { category: HarmCategory.HARM_CATEGORY_HARASSMENT, threshold: HarmBlockThreshold.BLOCK_NONE },
                { category: HarmCategory.HARM_CATEGORY_HATE_SPEECH, threshold: HarmBlockThreshold.BLOCK_NONE },
                { category: HarmCategory.HARM_CATEGORY_SEXUALLY_EXPLICIT, threshold: HarmBlockThreshold.BLOCK_NONE },
                { category: HarmCategory.HARM_CATEGORY_DANGEROUS_CONTENT, threshold: HarmBlockThreshold.BLOCK_NONE },
            ];

            const modelInstance = genAI.getGenerativeModel({
                model: modelToUse,
                safetySettings: safetySettings
            });

            let finalPrompt = prompt;
            if (!isImagenModel) {
                 const lowerPrompt = prompt.toLowerCase().trim();
                 const needsPrefix = !lowerPrompt.startsWith('generate') && !lowerPrompt.startsWith('create') && !lowerPrompt.startsWith('draw') && !lowerPrompt.startsWith('image of');
                 if (needsPrefix) {
                     finalPrompt = `Generate an image of: ${prompt}`;
                     console.log(`[Google Image Gen - Gemini] Prepended instruction to prompt.`);
                 }
            }

            if (isCancelled()) throw new LlmError('Image generation cancelled by user.', 'CANCELLED');

            // Parse size for Google models
            let width = 1024;
            let height = 1024;
            if (size && size.includes('x')) {
                const [w, h] = size.split('x').map(dim => parseInt(dim, 10));
                if (!isNaN(w) && !isNaN(h)) {
                    width = w;
                    height = h;
                }
            }

            const request: GenerateContentRequest = {
                contents: [{ role: "user", parts: [{ text: finalPrompt }] }],
                ...( !isImagenModel && {
                    generationConfig: {
                        responseModalities: ["Text", "Image"],
                        candidateCount: n || 1
                    } as any
                }),
                ...( isImagenModel && {
                    generationConfig: {
                        // For Imagen models, set the image dimensions
                        imageGenerationConfig: {
                            width: width,
                            height: height
                        }
                    } as any
                })
            };

            console.log(`[Google Image Gen] Sending generateContent request with config:`, request.generationConfig);
            const result = await modelInstance.generateContent(request);
            const response = result.response;

            const imagePart = response.candidates?.[0]?.content?.parts?.find((part: Part) => part.inlineData);

            if (imagePart?.inlineData) {
                const { mimeType, data: base64Data } = imagePart.inlineData;
                const imageData = Buffer.from(base64Data, 'base64');
                console.log(`[Google Image Gen] Success. MimeType: ${mimeType}, Base64 Data Length: ${base64Data.length}`);

                const localPath = await saveImageData(imageData, mimeType);
                if (localPath) {
                  const imageDataUrl = `data:${mimeType};base64,${base64Data}`;
                  return { imageDataUrl, localImagePath: localPath };
                } else {
                  console.error('[Google Image Gen] Failed to save generated image locally.');
                  throw new LlmError('Failed to save generated image locally.', 'IMAGE_SAVE_FAILED');
                }
            } else {
                const blockReason = response.promptFeedback?.blockReason;
                const safetyRatings = response.candidates?.[0]?.safetyRatings;
                const finishReason = response.candidates?.[0]?.finishReason;
                const textResponse = response.text ? response.text() : '(No text response)';

                console.error(`[Google Image Gen] Failed for model ${modelToUse}. No image data returned.`, {
                     blockReason, finishReason, safetyRatings, textResponse: textResponse.substring(0, 200),
                     fullResponse: JSON.stringify(response).substring(0, 500) + '...'
                });

                let errorMsg = `Google image generation failed (${modelToUse}).`;
                if (blockReason) {
                    errorMsg += ` Reason: Blocked (${blockReason}).`;
                    if(response.promptFeedback?.blockReasonMessage) errorMsg += ` Message: ${response.promptFeedback.blockReasonMessage}`;
                } else if (finishReason && finishReason !== 'STOP') {
                    errorMsg += ` Reason: Finished with status ${finishReason}.`;
                } else {
                    errorMsg += ` No image data received. Text response: ${textResponse.substring(0, 100)}...`;
                }
                if (safetyRatings) errorMsg += ` Safety Ratings: ${JSON.stringify(safetyRatings)}.`;

                throw new LlmError(errorMsg, 'API_RESPONSE_ERROR');
            }

        } catch (error: any) {
             if (error instanceof LlmError) throw error; // Re-throw specific errors
            console.error(`[Google Image Gen] Failed (${modelToUse}):`, error);
            const message = error.message || 'An unknown error occurred.';
            let detailedError = message;
            if (error.response?.data?.error?.message) {
                 detailedError = error.response.data.error.message;
            } else if (error.details) {
                 detailedError = error.details;
            }
            throw new LlmError(`Google image generation failed: ${detailedError}`, 'API_CALL_FAILED', error);
        }
    }
    else {
        console.warn(`[Image Service] Image generation not implemented for provider: ${provider}`);
        throw new LlmError(`Image generation is not supported for the provider '${provider}'.`, 'UNSUPPORTED_PROVIDER');
    }
}
