import mongoose, { Document, Schema } from 'mongoose';

// Interface representing the document structure in MongoDB
export interface IApiKey extends Document {
  userId: mongoose.Schema.Types.ObjectId;
  serviceName: string;
  apiKey: string; // This will store the ENCRYPTED key
  createdAt: Date;
  updatedAt: Date;
}

// Mongoose Schema definition
const ApiKeySchema: Schema = new Schema(
  {
    userId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User', // Reference to the User model
      required: true,
      index: true, // Index for faster lookups by user
    },
    serviceName: {
      type: String,
      required: true,
      trim: true,
      // Ensure a user cannot have multiple keys for the same service
      // unique: true, // This would make it unique across ALL users, which is not intended
    },
    apiKey: {
      type: String,
      required: true,
      // Do NOT trim API keys, whitespace might be significant
    },
  },
  {
    timestamps: true, // Automatically add createdAt and updatedAt fields
    // Add a compound index to ensure uniqueness per user per service
    indexes: [{ fields: { userId: 1, serviceName: 1 }, unique: true }],
  }
);

// Create and export the Mongoose model
const ApiKey = mongoose.model<IApiKey>('ApiKey', ApiKeySchema);

export default ApiKey;
