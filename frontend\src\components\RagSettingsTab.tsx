import React, { useState, useEffect, useRef, useCallback } from 'react';
import { useConfigStore } from '../store/configStore';
import apiClient from '../services/authApiService'; // Import authenticated API client
import Modal from './Modal';
import DirectoryBrowser from './DirectoryBrowser';
import { VscFolder, VscFolderOpened, VscNewFolder, VscRefresh, VscTrash, VscCloudUpload, VscSync, VscError, VscCheck, VscEllipsis, VscFile } from 'react-icons/vsc';

// --- Helper Components (Keep existing ones: FormControl, ToggleSwitch) ---
// ... (FormControl and ToggleSwitch code remains the same) ...
const FormControl: React.FC<{ label: string; htmlFor: string; children: React.ReactNode; className?: string }> = ({ label, htmlFor, children, className = '' }) => (
    <div className={`space-y-1 ${className}`}>
      <label htmlFor={htmlFor} className="block text-sm font-medium text-gray-700 dark:text-gray-300">
        {label}
      </label>
      {children}
    </div>
  );

  const ToggleSwitch: React.FC<{ id: string; checked: boolean; onChange: (checked: boolean) => void; label: string }> = ({ id, checked, onChange, label }) => (
    <div className="flex items-center justify-between p-3 bg-gray-100 dark:bg-gray-700 rounded-md shadow-sm">
      <label htmlFor={id} className="font-medium text-gray-700 dark:text-gray-300">
        {label}
      </label>
      <button
        id={id}
        onClick={() => onChange(!checked)}
        className={`relative inline-flex items-center h-6 rounded-full w-11 transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 ${
          checked ? 'bg-indigo-600' : 'bg-gray-300 dark:bg-gray-600'
        }`}
      >
        <span
          className={`inline-block w-4 h-4 transform bg-white rounded-full transition-transform duration-200 ease-in-out ${
            checked ? 'translate-x-6' : 'translate-x-1'
          }`}
        />
      </button>
    </div>
  );

// --- Main Component ---
const RagSettingsTab: React.FC = () => {
  // --- Zustand State ---
  const {
    ragSettings,
    setRagEnabled,
    setChunkingStrategy,
    setChunkSize,
    setChunkOverlap,
    setEmbeddingModel,
    setRetrievalNResults,
    setRetrievalThreshold,
    setUseReranking,
    setUseQueryExpansion,
  } = useConfigStore((state) => ({
    ragSettings: state.ragSettings,
    setRagEnabled: state.setRagEnabled,
    setChunkingStrategy: state.setChunkingStrategy,
    setChunkSize: state.setChunkSize,
    setChunkOverlap: state.setChunkOverlap,
    setEmbeddingModel: state.setEmbeddingModel,
    setRetrievalNResults: state.setRetrievalNResults,
    setRetrievalThreshold: state.setRetrievalThreshold,
    setUseReranking: state.setUseReranking,
    setUseQueryExpansion: state.setUseQueryExpansion,
  }));

  // --- Component State ---
  const [currentKbPath, setCurrentKbPath] = useState<string | null>(null); // Path currently used by backend
  const [knowledgeBases, setKnowledgeBases] = useState<string[]>([]); // List of known KB paths/names from backend list
  const [indexedFiles, setIndexedFiles] = useState<string[]>([]);
  const [statusMessage, setStatusMessage] = useState<{ type: 'idle' | 'loading' | 'success' | 'error'; message: string }>({ type: 'idle', message: 'Ready.' });
  const [isLoading, setIsLoading] = useState(false);

  // File/KB Deletion Modals
  const [isDeleteFileModalOpen, setIsDeleteFileModalOpen] = useState(false);
  const [fileToDelete, setFileToDelete] = useState<string | null>(null);
  const [isDeleteKbModalOpen, setIsDeleteKbModalOpen] = useState(false);
  const [kbToDeleteInfo, setKbToDeleteInfo] = useState<{ path: string | null, name: string | null }>({ path: null, name: null }); // Store both path and display name

  // Directory Browser State
  const [showDirectoryBrowser, setShowDirectoryBrowser] = useState(false);
  const [directoryBrowserMode, setDirectoryBrowserMode] = useState<'selectKb' | 'createKb' | 'selectUploadDir'>('selectKb');

  // Upload State
  const fileInputRef = useRef<HTMLInputElement>(null); // For single document upload
  const [directoryPathForUpload, setDirectoryPathForUpload] = useState<string>("");
  const [recursiveUpload, setRecursiveUpload] = useState<boolean>(true);

  // Debounce timeouts for settings updates
  const chunkSizeUpdateTimeout = useRef<NodeJS.Timeout | null>(null);
  const chunkOverlapUpdateTimeout = useRef<NodeJS.Timeout | null>(null);
  const retrievalNResultsUpdateTimeout = useRef<NodeJS.Timeout | null>(null);
  const retrievalThresholdUpdateTimeout = useRef<NodeJS.Timeout | null>(null);

  // --- Utility ---
  const getShortPath = (fullPath: string | null): string => {
    if (!fullPath) return 'N/A';
    const parts = fullPath.replace(/\\/g, '/').split('/');
    const name = parts[parts.length - 1] || parts[parts.length - 2]; // Get last part (file/dir name)
    return name || fullPath; // Return name or full path if name is empty
  };

  // --- API Interaction Functions ---

  // Fetch the *current* KB path from the backend (as originally used)
  const fetchCurrentKbPath = useCallback(async (showLoading = false) => {
    if (showLoading) setIsLoading(true);
    // Don't necessarily show status message for this background fetch unless loading
    if (showLoading) setStatusMessage({ type: 'loading', message: 'Fetching current KB path...' });
    try {
      // Using the endpoint with correct path
      const response = await apiClient.get('/api/rag/kb-path');
      const backendPath = response.data.path;
      setCurrentKbPath(backendPath);
      if (backendPath) {
        fetchIndexedFiles(backendPath, false); // Fetch files for the active KB
        if (showLoading) setStatusMessage({ type: 'idle', message: `Current KB: ${getShortPath(backendPath)}` });
      } else {
        setIndexedFiles([]);
        if (showLoading) setStatusMessage({ type: 'idle', message: 'No active KB set.' });

        // If no KB is set, try to create and set the default one
        try {
          console.log('No active KB, creating default...');
          await apiClient.post('/api/rag/kb-create', { name: 'default_kb', storageType: 'server' });
          console.log('Default knowledge base created successfully');
          // Set the default KB as active
          await apiClient.post('/api/rag/kb-path', { path: 'default_kb', storageType: 'server' });
          console.log('Default knowledge base set as active');
          // Fetch the current KB path again
          const updatedResponse = await apiClient.get('/api/rag/kb-path');
          const updatedPath = updatedResponse.data.path || null;
          setCurrentKbPath(updatedPath);
          if (updatedPath) {
            fetchIndexedFiles(updatedPath, false);
            if (showLoading) setStatusMessage({ type: 'idle', message: `Current KB: ${getShortPath(updatedPath)}` });
          }
        } catch (createErr: any) {
          console.error('Error creating/setting default knowledge base:', createErr);
          // Don't show error if it's just a conflict (already exists)
          if (createErr.response?.status !== 409) {
            setStatusMessage({
              type: 'error',
              message: `Failed to create default knowledge base: ${createErr.response?.data?.message || createErr.message}`
            });
          }
        }
      }
    } catch (err: any) {
      console.error('Error fetching current knowledge base path:', err);
      // Only show error if explicitly loading or if it's a significant error
      if (showLoading || !currentKbPath) { // Show error if manually triggered or no KB was known
         setStatusMessage({ type: 'error', message: `Failed to fetch current KB path: ${err.response?.data?.message || err.message}` });
      }
      setCurrentKbPath(null); // Assume no path on error
      setIndexedFiles([]);

      // If we get a 404, it might be because the knowledge base directory doesn't exist yet
      // Let's try to create it
      if (err.response?.status === 404) {
        try {
          console.log('Knowledge base directory not found, creating default...');
          await apiClient.post('/api/rag/kb-create', { name: 'default_kb', storageType: 'server' });
          console.log('Default knowledge base created successfully');
          // Set the default KB as active
          await apiClient.post('/api/rag/kb-path', { path: 'default_kb', storageType: 'server' });
          console.log('Default knowledge base set as active');
          // Try fetching again
          const retryResponse = await apiClient.get('/api/rag/kb-path');
          const retryPath = retryResponse.data.path || null;
          setCurrentKbPath(retryPath);
          if (retryPath) {
            fetchIndexedFiles(retryPath, false);
            setStatusMessage({ type: 'idle', message: `Current KB: ${getShortPath(retryPath)}` });
          }
        } catch (createErr: any) {
          console.error('Error creating default knowledge base:', createErr);
          setStatusMessage({
            type: 'error',
            message: `Failed to create default knowledge base: ${createErr.response?.data?.message || createErr.message}`
          });
        }
      }
    } finally {
      if (showLoading) setIsLoading(false);
    }
  }, [currentKbPath]); // Add currentKbPath dependency


  // Fetch the list of *available* knowledge bases (as originally used)
  const fetchKnowledgeBases = useCallback(async (showLoading = true) => {
    if (showLoading) setIsLoading(true);
    setStatusMessage({ type: 'loading', message: 'Fetching knowledge base list...' });
    try {
      // Using the endpoint from the original code
      const response = await apiClient.get('/api/rag/kb-list');
      // Assuming the response is { knowledgeBases: ["kb_name1", "kb_name2", "/full/path/kb3"] }
      setKnowledgeBases(response.data.knowledgeBases || []);
      setStatusMessage({ type: 'idle', message: 'Knowledge base list refreshed.' });

      // If we have a successful response but no knowledge bases, create the default one
      if (response.data.knowledgeBases?.length === 0) {
        console.log('No knowledge bases found, creating default...');
        try {
          await apiClient.post('/api/rag/kb-create', { name: 'default_kb', storageType: 'server' });
          console.log('Default knowledge base created successfully');
          // Fetch the list again after creating the default
          const updatedResponse = await apiClient.get('/api/rag/kb-list');
          setKnowledgeBases(updatedResponse.data.knowledgeBases || []);
        } catch (createErr: any) {
          console.error('Error creating default knowledge base:', createErr);
          // Don't show error if it's just a conflict (already exists)
          if (createErr.response?.status !== 409) {
            setStatusMessage({
              type: 'error',
              message: `Failed to create default knowledge base: ${createErr.response?.data?.message || createErr.message}`
            });
          }
        }
      }
    } catch (err: any) {
      console.error('Error fetching knowledge bases:', err);
      setStatusMessage({ type: 'error', message: `Failed to fetch knowledge bases: ${err.response?.data?.message || err.message}` });
      setKnowledgeBases([]);

      // If we get a 404, it might be because the knowledge base directory doesn't exist yet
      // Let's try to create it
      if (err.response?.status === 404) {
        try {
          console.log('Knowledge base directory not found, creating default...');
          await apiClient.post('/api/rag/kb-create', { name: 'default_kb', storageType: 'server' });
          console.log('Default knowledge base created successfully');
          // Try fetching again
          const retryResponse = await apiClient.get('/api/rag/kb-list');
          setKnowledgeBases(retryResponse.data.knowledgeBases || []);
          setStatusMessage({ type: 'idle', message: 'Knowledge base list refreshed.' });
        } catch (createErr: any) {
          console.error('Error creating default knowledge base:', createErr);
          setStatusMessage({
            type: 'error',
            message: `Failed to create default knowledge base: ${createErr.response?.data?.message || createErr.message}`
          });
        }
      }
    } finally {
      if (showLoading) setIsLoading(false);
    }
  }, []);

  // Fetch the list of indexed files for the *current* KB (backend knows context)
   const fetchIndexedFiles = useCallback(async (kbPath: string | null = currentKbPath, showLoading = true) => {
    if (!kbPath) {
      setIndexedFiles([]);
      // console.log("Skipping file fetch: No active KB path.");
      return;
    }
    if (showLoading) setIsLoading(true);
    setStatusMessage({ type: 'loading', message: 'Fetching indexed files...' });
    try {
      // Backend should return files for the currently set KB path
      const response = await apiClient.get('/api/rag/files');
      setIndexedFiles(response.data?.files || []);
      setStatusMessage({ type: 'idle', message: `Files loaded for KB: ${getShortPath(kbPath)}` });
    } catch (err: any) {
      console.error("Error fetching indexed files:", err);
      // Avoid overwriting more important KB path errors if file fetch fails
      if(statusMessage.type !== 'error') {
          setStatusMessage({ type: 'error', message: `Failed to fetch files: ${err.response?.data?.message || err.message}` });
      }
      setIndexedFiles([]);
    } finally {
      if (showLoading) setIsLoading(false);
    }
  }, [currentKbPath, statusMessage.type]); // Depend on current path and status type

  // --- KB Management Handlers ---

  // Set the backend's active KB path (as originally used)
  const handleSetKbPath = async (path: string, isCreating = false) => {
    setIsLoading(true);
    const actionVerb = isCreating ? "Activating newly created" : "Setting active";
    setStatusMessage({ type: 'loading', message: `${actionVerb} KB: ${getShortPath(path)}...` });
    try {
      // Using the endpoint from the original code to *set* the path
      // The original seemed to handle both simple names and full paths, backend needs to resolve
      const response = await apiClient.post('/api/rag/kb-path', { path });
      const confirmedPath = response.data.path || path; // Use path from response if available
      setCurrentKbPath(confirmedPath);
      setStatusMessage({ type: 'success', message: `Knowledge base ${getShortPath(confirmedPath)} is now active.` });
      fetchIndexedFiles(confirmedPath, false); // Fetch files for the new KB
      fetchKnowledgeBases(false); // Refresh KB list in background
    } catch (err: any) {
      console.error(`Error setting knowledge base path to ${path}:`, err);
      setStatusMessage({ type: 'error', message: `Failed to set KB path: ${err.response?.data?.message || err.message}` });
    } finally {
      setIsLoading(false);
    }
  };

  // Create *and then set* a new knowledge base
   const handleCreateAndSetKb = async (nameOrPath: string) => {
    setIsLoading(true);
    setStatusMessage({ type: 'loading', message: `Creating KB: ${getShortPath(nameOrPath)}...` });
    try {
      // 1. Create the KB using the original endpoint
      // Backend needs to handle creation based on the provided name/path
      const createResponse = await apiClient.post('/api/rag/kb-create', { name: nameOrPath });
      const createdName = createResponse.data.name || nameOrPath; // Get confirmed name/path if backend returns it
      setStatusMessage({ type: 'success', message: `Knowledge base '${getShortPath(createdName)}' created.` });

      // 2. Explicitly set the newly created KB as active using the SET endpoint
      await handleSetKbPath(createdName, true); // Pass the confirmed name/path

    } catch (err: any)
    {
      console.error(`Error creating knowledge base '${nameOrPath}':`, err);
       // Check if error occurred during creation or activation step
       if (statusMessage.message.includes("Creating")) { // Error likely during creation
         setStatusMessage({ type: 'error', message: `Failed to create KB: ${err.response?.data?.message || err.message}` });
       } else { // Error likely during activation (already handled by handleSetKbPath)
           console.error("Error occurred after creation, during activation step.");
       }
       setIsLoading(false); // Ensure loading stops on error
    }
    // setIsLoading(false) is handled by the final handleSetKbPath call
  };

  // Initiate KB deletion
  const handleDeleteKbClick = (kbIdentifier: string) => {
    // We might only have a name from the dropdown, or a full path
    // We need to show the identifier but potentially delete using a path
    // For now, assume identifier can be used by backend delete endpoint
    // Or modify backend to accept name and resolve path
    setKbToDeleteInfo({ path: kbIdentifier, name: getShortPath(kbIdentifier) });
    setIsDeleteKbModalOpen(true);
  };

  // Confirm KB Deletion
  const confirmDeleteKb = async () => {
    if (!kbToDeleteInfo.path) return;

    const pathToDelete = kbToDeleteInfo.path;
    const nameToDelete = kbToDeleteInfo.name;
    setIsDeleteKbModalOpen(false);
    setKbToDeleteInfo({ path: null, name: null });
    setIsLoading(true);
    setStatusMessage({ type: 'loading', message: `Removing KB ${nameToDelete}...` });

    try {
      // Using the original endpoint, backend needs to resolve path if name is given
      await apiClient.delete('/api/rag/kb-remove', { data: { path: pathToDelete } }); // Or use { name: pathToDelete } if backend expects name
      setStatusMessage({ type: 'success', message: `Knowledge base ${nameToDelete} removed.` });
      fetchKnowledgeBases(false); // Refresh list

      // If the deleted KB was the active one, fetch the current path again
      if (currentKbPath === pathToDelete) {
          fetchCurrentKbPath(false); // This will update currentKbPath and files
      }
    } catch (err: any) {
      console.error(`Error removing knowledge base ${pathToDelete}:`, err);
      setStatusMessage({ type: 'error', message: `Failed to remove KB ${nameToDelete}: ${err.response?.data?.message || err.message}` });
    } finally {
      setIsLoading(false);
    }
  };


  // --- File Management Handlers ---

   // **** RESTORED: Handle single document upload ****
   const handleUploadFile = async (file: File) => {
    if (!currentKbPath) {
      setStatusMessage({ type: 'error', message: "No active knowledge base selected for upload." });
      return;
    }
    setIsLoading(true);
    setStatusMessage({ type: 'loading', message: `Uploading ${file.name}...` });
    const formData = new FormData();
    formData.append('ragFile', file); // Backend expects 'ragFile'

    try {
      // Using the original endpoint
      const response = await apiClient.post('/api/rag/upload', formData, {
        headers: { 'Content-Type': 'multipart/form-data' },
      });
      setStatusMessage({ type: 'success', message: response.data.message || `Successfully processed ${file.name}.` });
      fetchIndexedFiles(currentKbPath, false); // Refresh file list
    } catch (err: any) {
      console.error('Upload failed:', err);
      const errorMsg = err.response?.data?.message || err.message || `Failed to upload or process ${file.name}.`;
      setStatusMessage({ type: 'error', message: `Upload Error: ${errorMsg}` });
    } finally {
       setIsLoading(false);
       // Reset file input to allow selecting the same file again
       if(fileInputRef.current) {
           fileInputRef.current.value = '';
       }
    }
  };

  // **** RESTORED: Handle file input change for single upload ****
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
     if (e.target.files && e.target.files.length > 0) {
         handleUploadFile(e.target.files[0]);
     }
  };

  // Handle directory upload processing
  const handleDirectoryUpload = async (dirPath: string) => {
    if (!currentKbPath) {
      setStatusMessage({ type: 'error', message: "No active knowledge base selected for upload." });
      return;
    }
     if (!dirPath || !dirPath.trim()) return;

    setIsLoading(true);
    setStatusMessage({ type: 'loading', message: `Processing directory ${getShortPath(dirPath)}...` });

    try {
      // Using the original endpoint
      const response = await apiClient.post('/api/rag/upload-directory', {
        directoryPath: dirPath,
        recursive: recursiveUpload,
      });

      setStatusMessage({ type: 'success', message: response.data.message || `Directory processing complete. Processed ${response.data.successCount || 0} files.` });
      fetchIndexedFiles(currentKbPath, false); // Refresh file list
      setDirectoryPathForUpload(''); // Clear the selected directory path

    } catch (err: any) {
      console.error(`Error processing directory '${dirPath}':`, err);
      setStatusMessage({ type: 'error', message: `Failed to process directory ${getShortPath(dirPath)}: ${err.response?.data?.message || err.message}` });
    } finally {
      setIsLoading(false);
    }
  };

  // Initiate file deletion
  const handleDeleteFileClick = (filename: string) => {
    setFileToDelete(filename);
    setIsDeleteFileModalOpen(true);
  };

  // Confirm file deletion
  const confirmDeleteFile = async () => {
    if (!fileToDelete || !currentKbPath) return;

    const filename = fileToDelete;
    setIsDeleteFileModalOpen(false);
    setFileToDelete(null);
    setIsLoading(true);
    setStatusMessage({ type: 'loading', message: `Deleting ${filename}...` });

     try {
         // Using the original endpoint
         const response = await apiClient.delete(`/api/rag/files/${encodeURIComponent(filename)}`);
         setStatusMessage({ type: 'success', message: response.data.message || `Successfully deleted ${filename}.` });
         fetchIndexedFiles(currentKbPath, false); // Refresh file list
     } catch (err: any) {
         console.error(`Error deleting file ${filename}:`, err);
         setStatusMessage({ type: 'error', message: `Failed to delete ${filename}: ${err.response?.data?.message || err.message}` });
     } finally {
         setIsLoading(false);
     }
  };

  // Re-index all files in the current KB
  const handleReindexAll = async () => {
    if (!currentKbPath) {
       setStatusMessage({ type: 'error', message: "No active knowledge base to re-index." });
       return;
    }
    setIsLoading(true);
    setStatusMessage({ type: 'loading', message: 'Re-indexing all files...' });

    try {
      // Using the original endpoint
      const response = await apiClient.post('/api/rag/reindex');
      setStatusMessage({ type: 'success', message: response.data.message || 'Re-indexing initiated successfully.' });
      fetchIndexedFiles(currentKbPath, false); // Refresh file list async
    } catch (err: any) {
      console.error('Error re-indexing knowledge base:', err);
      setStatusMessage({ type: 'error', message: `Failed to start re-indexing: ${err.response?.data?.message || err.message}` });
    } finally {
      setIsLoading(false);
    }
  };


  // --- Effects ---

  // Fetch initial data on mount
  useEffect(() => {
    fetchCurrentKbPath(false); // Fetch silently initially
    fetchKnowledgeBases(false);
  }, [fetchCurrentKbPath, fetchKnowledgeBases]); // Use the memoized functions

   // Clear non-loading status messages after a delay
   useEffect(() => {
    if (statusMessage.type === 'success' || statusMessage.type === 'error') {
      const timer = setTimeout(() => {
        // Only reset if the status hasn't changed to loading again
        setStatusMessage(prev => (prev.type !== 'loading' ? { type: 'idle', message: 'Ready.' } : prev));
      }, 5000);
      return () => clearTimeout(timer);
    }
  }, [statusMessage]);

  // --- Directory Browser Handlers ---
  const openDirectoryBrowser = (mode: 'selectKb' | 'createKb' | 'selectUploadDir') => {
    setDirectoryBrowserMode(mode);
    setShowDirectoryBrowser(true);
  };

  const handleDirectorySelected = (path: string) => {
    setShowDirectoryBrowser(false);
    if (directoryBrowserMode === 'selectKb') {
      // Use the SET endpoint to make this path active
      handleSetKbPath(path);
    } else if (directoryBrowserMode === 'createKb') {
      // Use the CREATE endpoint, which should ideally also set it active,
      // or follow up with a SET call. handleCreateAndSetKb does this.
      handleCreateAndSetKb(path);
    } else if (directoryBrowserMode === 'selectUploadDir') {
      setDirectoryPathForUpload(path);
      // Optional: Automatically start upload? Maybe not, let user click Process.
      // setTimeout(() => handleDirectoryUpload(path), 100);
    }
  };

   // --- RAG Settings Handlers ---

    // Update RAG settings on the backend
    const updateRagSettings = async () => {
      setIsLoading(true);
      setStatusMessage({ type: 'loading', message: `Updating RAG settings...` });
      try {
        const response = await apiClient.post('/api/rag/update-settings', {
          embeddingModel: ragSettings.embeddingModel,
          chunkSize: ragSettings.chunkSize,
          chunkOverlap: ragSettings.chunkOverlap,
          chunkingStrategy: ragSettings.chunkingStrategy,
          retrievalNResults: ragSettings.retrievalNResults,
          retrievalThreshold: ragSettings.retrievalThreshold,
          useReranking: ragSettings.useReranking,
          useQueryExpansion: ragSettings.useQueryExpansion
        });
        setStatusMessage({
          type: 'success',
          message: response.data.message || `RAG settings updated successfully.`
        });
      } catch (err: any) {
        console.error('Error updating RAG settings:', err);
        setStatusMessage({
          type: 'error',
          message: `Failed to update RAG settings: ${err.response?.data?.message || err.message}`
        });
      } finally {
        setIsLoading(false);
      }
    };

    // Handle embedding model change
    const handleEmbeddingModelChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
      const newModel = e.target.value;
      // Update the UI state
      setEmbeddingModel(newModel);
      // Update the backend
      updateRagSettings();
    };

    // Handle chunking strategy change
    const handleChunkingStrategyChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
      const newStrategy = e.target.value;
      // Update the UI state
      setChunkingStrategy(newStrategy);
      // Update the backend
      updateRagSettings();
    };

    // Handle chunk size change
    const handleChunkSizeChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      const value = parseInt(e.target.value, 10);
      setChunkSize(isNaN(value) ? 0 : value);
      // Debounce the backend update to avoid too many requests
      if (chunkSizeUpdateTimeout.current) clearTimeout(chunkSizeUpdateTimeout.current);
      chunkSizeUpdateTimeout.current = setTimeout(() => updateRagSettings(), 1000);
    };

    // Handle chunk overlap change
    const handleChunkOverlapChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      const value = parseInt(e.target.value, 10);
      setChunkOverlap(isNaN(value) ? 0 : value);
      // Debounce the backend update to avoid too many requests
      if (chunkOverlapUpdateTimeout.current) clearTimeout(chunkOverlapUpdateTimeout.current);
      chunkOverlapUpdateTimeout.current = setTimeout(() => updateRagSettings(), 1000);
    };

    // Handle retrieval results count change
    const handleRetrievalNResultsChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      const value = parseInt(e.target.value, 10);
      setRetrievalNResults(isNaN(value) ? 0 : value);
      // Debounce the backend update to avoid too many requests
      if (retrievalNResultsUpdateTimeout.current) clearTimeout(retrievalNResultsUpdateTimeout.current);
      retrievalNResultsUpdateTimeout.current = setTimeout(() => updateRagSettings(), 1000);
    };

    // Handle retrieval threshold change
    const handleRetrievalThresholdChange = (e: React.ChangeEvent<HTMLInputElement>) => {
      const value = parseFloat(e.target.value);
      setRetrievalThreshold(isNaN(value) ? 0 : value);
      // Debounce the backend update to avoid too many requests
      if (retrievalThresholdUpdateTimeout.current) clearTimeout(retrievalThresholdUpdateTimeout.current);
      retrievalThresholdUpdateTimeout.current = setTimeout(() => updateRagSettings(), 1000);
    };

    // Handle reranking toggle
    const handleUseRerankingChange = (checked: boolean) => {
      setUseReranking(checked);
      // Update the backend
      updateRagSettings();
    };

    // Handle query expansion toggle
    const handleUseQueryExpansionChange = (checked: boolean) => {
      setUseQueryExpansion(checked);
      // Update the backend
      updateRagSettings();
    };

    // Handle RAG enabled toggle
    const handleRagEnabledChange = (checked: boolean) => {
      setRagEnabled(checked);
      // Update the backend
      updateRagSettings();
    };

  // --- Render ---
  return (
    <>
      <div className="space-y-6 p-1">
        {/* RAG Enable Toggle */}
        {/* ... (same as before) ... */}
        <div className="flex justify-between items-center p-3 bg-gray-50 dark:bg-gray-800 rounded-md shadow-sm">
            <h3 className="text-lg font-semibold text-gray-800 dark:text-gray-200">RAG Settings</h3>
            <ToggleSwitch
                id="ragEnabled"
                checked={ragSettings.enabled}
                onChange={handleRagEnabledChange}
                label="Enable RAG"
            />
        </div>

        {/* Knowledge Base Management Section */}
        <fieldset className="border border-gray-300 dark:border-gray-600 rounded-md p-4 space-y-4">
          <legend className="text-sm font-medium px-1 text-gray-700 dark:text-gray-300 flex items-center gap-1">
            <VscFolderOpened /> Knowledge Base Management
          </legend>

          {/* Status Display */}
          {/* ... (same as before) ... */}
          <div className={`p-2 rounded-md text-sm ${
              statusMessage.type === 'error' ? 'bg-red-100 dark:bg-red-900/50 text-red-700 dark:text-red-300' :
              statusMessage.type === 'success' ? 'bg-green-100 dark:bg-green-900/50 text-green-700 dark:text-green-300' :
              statusMessage.type === 'loading' ? 'bg-blue-100 dark:bg-blue-900/50 text-blue-700 dark:text-blue-300' :
              'bg-gray-100 dark:bg-gray-700/50 text-gray-600 dark:text-gray-400'
          }`}>
              {statusMessage.type === 'loading' && <VscEllipsis className="inline-block mr-1 animate-spin" />}
              {statusMessage.type === 'error' && <VscError className="inline-block mr-1" />}
              {statusMessage.type === 'success' && <VscCheck className="inline-block mr-1" />}
              {statusMessage.message}
          </div>

          {/* Current KB Display and Actions */}
          <div className="bg-gray-50 dark:bg-gray-700/50 p-3 rounded-md space-y-2">
            <div className='flex justify-between items-center'>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                    Active Knowledge Base:
                </label>
                <button
                    onClick={() => fetchCurrentKbPath(true)} // Manually refresh current path display
                    disabled={isLoading}
                    className="p-1 text-xs bg-gray-200 text-gray-600 rounded hover:bg-gray-300 dark:bg-gray-600 dark:text-gray-200 dark:hover:bg-gray-500 disabled:opacity-50"
                    title="Refresh current KB path"
                >
                    <VscRefresh />
                </button>
            </div>
            <div className="flex items-center justify-between">
                <span className="px-3 py-1.5 bg-white dark:bg-gray-800 rounded-md text-sm font-mono text-gray-800 dark:text-gray-200 truncate flex-grow mr-2" title={currentKbPath ?? 'No KB selected'}>
                    {currentKbPath ? getShortPath(currentKbPath) : <span className="italic text-gray-500">None Selected</span>}
                </span>
                <div className="flex items-center space-x-2 flex-shrink-0">
                     <button
                        onClick={() => openDirectoryBrowser('selectKb')}
                        disabled={isLoading}
                        className="px-3 py-1.5 text-sm bg-indigo-100 text-indigo-700 rounded hover:bg-indigo-200 dark:bg-indigo-900/50 dark:text-indigo-300 dark:hover:bg-indigo-800/50 disabled:opacity-50 flex items-center gap-1"
                        title="Select an existing folder to use as knowledge base"
                    >
                        <VscFolder /> Select / Change
                    </button>
                    <button
                        onClick={() => openDirectoryBrowser('createKb')}
                        disabled={isLoading}
                        className="px-3 py-1.5 text-sm bg-green-100 text-green-700 rounded hover:bg-green-200 dark:bg-green-900/50 dark:text-green-300 dark:hover:bg-green-800/50 disabled:opacity-50 flex items-center gap-1"
                        title="Create a new knowledge base folder (Requires backend support for directory creation)"
                    >
                       <VscNewFolder /> Create New
                    </button>
                    {/* Removed Delete Current - Use Dropdown Delete instead */}
                </div>
            </div>

             {/* Dropdown for available KBs + Delete + Refresh */}
             <div className="pt-2 flex items-center space-x-2">
                 <label htmlFor="kbSwitcher" className="text-sm font-medium text-gray-700 dark:text-gray-300 flex-shrink-0">Available KBs:</label>
                 <select
                     id="kbSwitcher"
                     value={currentKbPath || ""}
                     onChange={(e) => handleSetKbPath(e.target.value)} // Set selected KB as active
                     className="flex-grow px-3 py-1.5 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm sm:text-sm text-gray-800 dark:text-gray-200 bg-white dark:bg-gray-800"
                     disabled={isLoading || knowledgeBases.length === 0}
                 >
                     <option value="" disabled> -- Select to activate -- </option>
                     {knowledgeBases.map(kb => (
                         <option key={kb} value={kb}>{getShortPath(kb)}</option>
                     ))}
                 </select>
                 <button
                     onClick={() => {
                         const select = document.getElementById('kbSwitcher') as HTMLSelectElement;
                         if (select && select.value) {
                             handleDeleteKbClick(select.value);
                         } else {
                            setStatusMessage({type:'error', message:'Please select a KB from the list to delete.'})
                         }
                     }}
                     disabled={isLoading || knowledgeBases.length === 0}
                     className="p-1.5 text-sm bg-red-100 text-red-700 rounded hover:bg-red-200 dark:bg-red-900/50 dark:text-red-300 dark:hover:bg-red-800/50 disabled:opacity-50"
                     title="Delete the selected knowledge base from the list"
                 >
                    <VscTrash />
                 </button>
                  <button
                     onClick={() => fetchKnowledgeBases(true)} // Force refresh KB list
                     disabled={isLoading}
                     className="p-1.5 text-sm bg-gray-200 text-gray-700 rounded hover:bg-gray-300 dark:bg-gray-600 dark:text-gray-200 dark:hover:bg-gray-500 disabled:opacity-50"
                     title="Refresh available knowledge bases list"
                 >
                     <VscRefresh />
                 </button>
             </div>
          </div>


          {/* File Management for Active KB */}
          {/* Only show this section if a KB path is set */}
          {currentKbPath && (
            <div className="space-y-3 border-t border-gray-300 dark:border-gray-600 pt-4">
                 <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300">Files in Active KB ({getShortPath(currentKbPath)})</h4>

                 {/* File List */}
                  <div className="border rounded-md bg-gray-50 dark:bg-gray-700/50">
                     {indexedFiles.length === 0 && !isLoading && ( // Show only if not loading
                         <p className="text-sm text-gray-500 dark:text-gray-400 italic p-3 text-center">No files currently indexed in this KB.</p>
                     )}
                     {indexedFiles.length > 0 && (
                         <ul className="max-h-48 overflow-y-auto divide-y divide-gray-200 dark:divide-gray-700">
                             {indexedFiles.map((filename) => (
                                 <li key={filename} className="flex justify-between items-center text-sm p-2 hover:bg-gray-100 dark:hover:bg-gray-600">
                                     <span className="truncate text-gray-800 dark:text-gray-200 flex items-center gap-1" title={filename}>
                                         <VscFile /> {filename}
                                     </span>
                                     <button
                                         onClick={() => handleDeleteFileClick(filename)}
                                         disabled={isLoading}
                                         className="ml-2 flex-shrink-0 p-1 text-xs text-red-600 hover:text-red-800 dark:text-red-400 dark:hover:text-red-300 disabled:opacity-50"
                                         title={`Delete ${filename}`}
                                     >
                                         <VscTrash />
                                     </button>
                                 </li>
                             ))}
                         </ul>
                     )}
                     {/* Show loading indicator specifically for file list */}
                     {isLoading && statusMessage.message.includes("Fetching indexed files") && (
                          <div className="flex items-center justify-center p-3 text-gray-600 dark:text-gray-400">
                               <VscSync className="animate-spin mr-2" /> Loading files...
                           </div>
                     )}
                 </div>

                 {/* File Actions */}
                 <div className="flex flex-wrap items-center gap-2 pt-2">
                      {/* **** RESTORED: Single File Upload Button **** */}
                      <input
                         type="file"
                         ref={fileInputRef}
                         onChange={handleFileChange}
                         className="hidden"
                         accept=".pdf,.docx,.txt,.md,.xlsx,.xls,.csv,.ts,.js,.py,.java,.c,.cpp,.cs,.php,.rb,.html,.htm,.json,.xml,.yaml,.yml,.ini,.config,.sql" // Add more as needed
                         disabled={isLoading || !currentKbPath} // Disable if loading or no KB active
                     />
                     <button
                         type="button"
                         onClick={() => fileInputRef.current?.click()}
                         className="px-3 py-1.5 text-sm bg-blue-100 text-blue-700 rounded hover:bg-blue-200 dark:bg-blue-900/50 dark:text-blue-300 dark:hover:bg-blue-800/50 transition-colors disabled:opacity-50 flex items-center gap-1"
                         disabled={isLoading || !currentKbPath}
                     >
                         <VscCloudUpload /> Upload Document
                     </button>

                     {/* Directory Upload Section */}
                     <div className="flex items-center gap-2">
                         <button
                             type="button"
                             onClick={() => openDirectoryBrowser('selectUploadDir')}
                             className="px-3 py-1.5 text-sm bg-gray-200 dark:bg-gray-600 text-gray-700 dark:text-gray-200 rounded hover:bg-gray-300 dark:hover:bg-gray-500 transition-colors disabled:opacity-50 flex items-center gap-1"
                             disabled={isLoading || !currentKbPath}
                             title="Select a local directory to upload files from (Requires backend directory listing support)"
                         >
                             <VscFolder /> Process Directory...
                         </button>
                         {/* Display selected path and Process button */}
                         {directoryPathForUpload && (
                             <>
                                 <span className="text-xs text-gray-500 dark:text-gray-400 truncate max-w-[150px]" title={directoryPathForUpload}>
                                     Selected: {getShortPath(directoryPathForUpload)}
                                 </span>
                                 <button
                                     onClick={() => handleDirectoryUpload(directoryPathForUpload)}
                                     disabled={isLoading || !directoryPathForUpload.trim()}
                                     className="px-3 py-1.5 text-sm bg-blue-100 text-blue-700 rounded hover:bg-blue-200 dark:bg-blue-900/50 dark:text-blue-300 dark:hover:bg-blue-800/50 disabled:opacity-50 flex items-center gap-1"
                                     title="Upload files from the selected directory"
                                 >
                                     <VscCloudUpload /> Process
                                 </button>
                                 {/* Recursive Checkbox */}
                                 <div className="flex items-center">
                                      <input
                                          type="checkbox"
                                          id="recursiveUpload"
                                          checked={recursiveUpload}
                                          onChange={(e) => setRecursiveUpload(e.target.checked)}
                                          className="mr-1 h-4 w-4 text-indigo-600 border-gray-300 rounded focus:ring-indigo-500"
                                          disabled={isLoading}
                                      />
                                      <label htmlFor="recursiveUpload" className="text-xs text-gray-600 dark:text-gray-400">
                                          Recursive
                                      </label>
                                  </div>
                             </>
                         )}
                     </div>

                     {/* File List Refresh & Re-Index */}
                     <div className="flex items-center gap-2 ml-auto"> {/* Pushes to the right */}
                         <button
                             type="button"
                             onClick={() => fetchIndexedFiles(currentKbPath, true)} // Force refresh file list
                             className="p-1.5 text-sm bg-gray-200 dark:bg-gray-600 text-gray-700 dark:text-gray-200 rounded hover:bg-gray-300 dark:hover:bg-gray-500 transition-colors disabled:opacity-50"
                             disabled={isLoading || !currentKbPath}
                             title="Refresh File List"
                         >
                              <VscRefresh />
                         </button>
                         <button
                            onClick={handleReindexAll}
                            disabled={isLoading || !currentKbPath}
                            className="px-3 py-1.5 text-sm bg-yellow-100 text-yellow-700 rounded hover:bg-yellow-200 dark:bg-yellow-900/50 dark:text-yellow-300 dark:hover:bg-yellow-800/50 disabled:opacity-50 flex items-center gap-1"
                            title="Re-process all files in the active knowledge base"
                        >
                           <VscSync /> Re-Index All
                        </button>
                    </div>
                 </div>
             </div>
          )}

        </fieldset>


        {/* Chunking Settings */}
        {/* ... (same as before) ... */}
        <fieldset className="border border-gray-300 dark:border-gray-600 rounded-md p-4 space-y-4">
          <legend className="text-sm font-medium px-1 text-gray-700 dark:text-gray-300">Chunking</legend>
          {/* ... controls ... */}
          <FormControl label="Strategy" htmlFor="chunkingStrategy">
            <select id="chunkingStrategy" value={ragSettings.chunkingStrategy} onChange={handleChunkingStrategyChange} className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 dark:border-gray-600 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100">
              <option value="semantic">Semantic</option>
              <option value="fixed">Fixed Size</option>
            </select>
          </FormControl>
          <div className="grid grid-cols-2 gap-4">
            <FormControl label="Chunk Size" htmlFor="chunkSize">
              <input type="number" id="chunkSize" value={ragSettings.chunkSize} onChange={handleChunkSizeChange} min="1" className="mt-1 block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100" />
            </FormControl>
            <FormControl label="Chunk Overlap" htmlFor="chunkOverlap">
              <input type="number" id="chunkOverlap" value={ragSettings.chunkOverlap} onChange={handleChunkOverlapChange} min="0" className="mt-1 block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100" />
            </FormControl>
          </div>
        </fieldset>

        {/* Embedding Settings */}
        {/* ... (same as before) ... */}
        <fieldset className="border border-gray-300 dark:border-gray-600 rounded-md p-4 space-y-4">
           <legend className="text-sm font-medium px-1 text-gray-700 dark:text-gray-300">Embedding</legend>
           {/* ... controls ... */}
           <FormControl label="Embedding Model" htmlFor="embeddingModel">
            <select id="embeddingModel" value={ragSettings.embeddingModel} onChange={handleEmbeddingModelChange} className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 dark:border-gray-600 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100">
              <option value="Xenova/all-mpnet-base-v2">all-mpnet-base-v2 (Default, 768 dim)</option>
              <option value="Xenova/all-MiniLM-L6-v2">all-MiniLM-L6-v2 (Faster, 384 dim)</option>
              <option value="Xenova/bge-large-en-v1.5">bge-large-en-v1.5 (High quality, 1024 dim)</option>
              <option value="Xenova/e5-large-v2">e5-large-v2 (High quality, 1024 dim)</option>
              <option value="Xenova/paraphrase-multilingual-MiniLM-L12-v2">paraphrase-multilingual-MiniLM-L12-v2 (Multilingual, 384 dim)</option>
              <option value="Xenova/multi-qa-mpnet-base-dot-v1">multi-qa-mpnet-base-dot-v1 (Question-Answer, 768 dim)</option>
               {![ "Xenova/all-mpnet-base-v2", "Xenova/all-MiniLM-L6-v2", "Xenova/bge-large-en-v1.5", "Xenova/e5-large-v2", "Xenova/paraphrase-multilingual-MiniLM-L12-v2", "Xenova/multi-qa-mpnet-base-dot-v1" ].includes(ragSettings.embeddingModel) && ragSettings.embeddingModel && ( <option value={ragSettings.embeddingModel}>{ragSettings.embeddingModel} (Custom)</option> )}
            </select>
          </FormControl>
         </fieldset>

        {/* Retrieval Settings */}
        {/* ... (same as before) ... */}
        <fieldset className="border border-gray-300 dark:border-gray-600 rounded-md p-4 space-y-4">
          <legend className="text-sm font-medium px-1 text-gray-700 dark:text-gray-300">Retrieval</legend>
          {/* ... controls ... */}
          <div className="grid grid-cols-2 gap-4">
              <FormControl label="Results Count (n)" htmlFor="retrievalNResults">
                <input type="number" id="retrievalNResults" value={ragSettings.retrievalNResults} onChange={handleRetrievalNResultsChange} min="1" className="mt-1 block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100" />
              </FormControl>
              <FormControl label="Similarity Threshold" htmlFor="retrievalThreshold">
                <input type="number" id="retrievalThreshold" value={ragSettings.retrievalThreshold} onChange={handleRetrievalThresholdChange} min="0" max="1" step="0.01" className="mt-1 block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100" />
              </FormControl>
          </div>
          <div className="grid grid-cols-2 gap-4 pt-2">
              <ToggleSwitch id="useReranking" checked={ragSettings.useReranking} onChange={handleUseRerankingChange} label="Use Reranking" />
              <ToggleSwitch id="useQueryExpansion" checked={ragSettings.useQueryExpansion} onChange={handleUseQueryExpansionChange} label="Use Query Expansion" />
          </div>
        </fieldset>
      </div>

      {/* Modals */}
      {/* Delete File Modal */}
      {/* ... (same as before) ... */}
        <Modal isOpen={isDeleteFileModalOpen} onClose={() => { setIsDeleteFileModalOpen(false); setFileToDelete(null); }} title="Confirm File Deletion" >
            <div className="p-4">
                <p className="text-sm text-gray-600 dark:text-gray-300 mb-4"> Are you sure you want to permanently delete the file <strong className="font-medium break-all">{fileToDelete}</strong> and its associated data from the active knowledge base? This action cannot be undone. </p>
                <div className="flex justify-end space-x-3">
                    <button onClick={() => { setIsDeleteFileModalOpen(false); setFileToDelete(null); }} className="px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-gray-200 dark:bg-gray-600 rounded-md hover:bg-gray-300 dark:hover:bg-gray-500 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 dark:focus:ring-offset-gray-800"> Cancel </button>
                    <button onClick={confirmDeleteFile} disabled={isLoading} className="px-4 py-2 text-sm font-medium text-white bg-red-600 rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 dark:focus:ring-offset-gray-800 disabled:opacity-50 flex items-center gap-1"> {isLoading ? <VscEllipsis className="animate-spin"/> : <VscTrash />} {isLoading ? 'Deleting...' : 'Delete File'} </button>
                </div>
            </div>
        </Modal>

       {/* Delete KB Modal */}
       {/* ... (updated to use kbToDeleteInfo) ... */}
        <Modal isOpen={isDeleteKbModalOpen} onClose={() => { setIsDeleteKbModalOpen(false); setKbToDeleteInfo({ path: null, name: null }); }} title="Confirm Knowledge Base Deletion" >
            <div className="p-4">
                <p className="text-sm text-gray-600 dark:text-gray-300 mb-4"> Are you sure you want to permanently delete the entire knowledge base <strong className="font-medium break-all">{kbToDeleteInfo.name}</strong> ({kbToDeleteInfo.path}) and all its contents? This action cannot be undone. </p>
                <div className="flex justify-end space-x-3">
                    <button onClick={() => { setIsDeleteKbModalOpen(false); setKbToDeleteInfo({ path: null, name: null }); }} className="px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-gray-200 dark:bg-gray-600 rounded-md hover:bg-gray-300 dark:hover:bg-gray-500 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 dark:focus:ring-offset-gray-800"> Cancel </button>
                    <button onClick={confirmDeleteKb} disabled={isLoading} className="px-4 py-2 text-sm font-medium text-white bg-red-600 rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 dark:focus:ring-offset-gray-800 disabled:opacity-50 flex items-center gap-1"> {isLoading ? <VscEllipsis className="animate-spin"/> : <VscTrash />} {isLoading ? 'Deleting...' : 'Delete Knowledge Base'} </button>
                </div>
            </div>
        </Modal>


      {/* Directory Browser Modal */}
      {/* Requires Backend Implementation! */}
      {showDirectoryBrowser && (
        <DirectoryBrowser
          onSelectDirectory={handleDirectorySelected}
          onCancel={() => setShowDirectoryBrowser(false)}
          selectButtonLabel={
              directoryBrowserMode === 'createKb' ? 'Create & Select This Folder' :
              directoryBrowserMode === 'selectUploadDir' ? 'Select Directory for Upload' :
              'Set as Active Knowledge Base'
          }
          title={
              directoryBrowserMode === 'createKb' ? 'Create or Select KB Folder' :
              directoryBrowserMode === 'selectUploadDir' ? 'Select Directory to Upload From' :
              'Select Knowledge Base Folder'
          }
          // Allow creating folders only when managing KBs, not when selecting upload source
          showCreateButton={directoryBrowserMode === 'selectKb' || directoryBrowserMode === 'createKb'}
          // Start browsing from the current KB's parent or a default location
          initialPath={currentKbPath ? currentKbPath.substring(0, currentKbPath.lastIndexOf('/')) || './' : './'}
        />
      )}
    </>
  );
};

export default RagSettingsTab;