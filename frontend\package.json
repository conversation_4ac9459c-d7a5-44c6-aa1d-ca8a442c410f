{"name": "maia-web-frontend", "version": "0.1.0", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "node build.cjs", "build:standard": "tsc && vite build", "build:production": "vite build --mode production", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview"}, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@heroicons/react": "^2.2.0", "@mui/material": "^7.0.2", "@types/react-router-dom": "^5.3.3", "axios": "^1.6.2", "highlight.js": "^11.11.1", "immer": "^10.1.1", "react": "^18.2.0", "react-dom": "^18.2.0", "react-icons": "^5.5.0", "react-markdown": "^9.0.1", "react-router-dom": "^7.4.1", "rehype-highlight": "^7.0.2", "rehype-sanitize": "^6.0.0", "remark-gfm": "^4.0.1", "socket.io-client": "^4.7.2", "zustand": "^4.4.6"}, "devDependencies": {"@types/node": "^22.13.16", "@types/react": "^18.2.37", "@types/react-dom": "^18.2.15", "@typescript-eslint/eslint-plugin": "^6.10.0", "@typescript-eslint/parser": "^6.10.0", "@vitejs/plugin-react": "^4.2.0", "autoprefixer": "^10.4.16", "eslint": "^8.53.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.4", "postcss": "^8.4.31", "tailwindcss": "^3.3.5", "typescript": "^5.2.2", "vite": "^5.0.0"}}