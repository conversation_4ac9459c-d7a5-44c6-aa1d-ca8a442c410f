{"compilerOptions": {"target": "ES2020", "module": "NodeNext", "moduleResolution": "NodeNext", "esModuleInterop": true, "outDir": "./dist", "rootDir": "./src", "strict": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "declaration": true, "sourceMap": true, "typeRoots": ["./node_modules/@types", "./src/types"]}, "include": ["src/**/*"], "exclude": ["node_modules", "dist"]}