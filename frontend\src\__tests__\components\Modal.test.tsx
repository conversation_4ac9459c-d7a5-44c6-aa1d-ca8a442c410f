import { render, screen, fireEvent } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { vi } from 'vitest'
import Modal from '../../components/Modal'

describe('Modal', () => {
  const mockOnClose = vi.fn()
  const defaultProps = {
    isOpen: true,
    onClose: mockOnClose,
    title: 'Test Modal',
    children: <div>Modal content</div>
  }

  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('renders when isOpen is true', () => {
    render(<Modal {...defaultProps} />)
    
    expect(screen.getByText('Test Modal')).toBeInTheDocument()
    expect(screen.getByText('Modal content')).toBeInTheDocument()
  })

  it('does not render when isOpen is false', () => {
    render(<Modal {...defaultProps} isOpen={false} />)
    
    expect(screen.queryByText('Test Modal')).not.toBeInTheDocument()
    expect(screen.queryByText('Modal content')).not.toBeInTheDocument()
  })

  it('calls onClose when close button is clicked', async () => {
    const user = userEvent.setup()
    render(<Modal {...defaultProps} />)
    
    const closeButton = screen.getByLabelText('Close modal')
    await user.click(closeButton)
    
    expect(mockOnClose).toHaveBeenCalledTimes(1)
  })

  it('calls onClose when backdrop is clicked', async () => {
    const user = userEvent.setup()
    render(<Modal {...defaultProps} />)
    
    // Click on the backdrop (the outer div)
    const backdrop = screen.getByText('Test Modal').closest('.fixed')
    await user.click(backdrop!)
    
    expect(mockOnClose).toHaveBeenCalledTimes(1)
  })

  it('does not call onClose when modal content is clicked', async () => {
    const user = userEvent.setup()
    render(<Modal {...defaultProps} />)
    
    // Click on the modal content
    const modalContent = screen.getByText('Modal content')
    await user.click(modalContent)
    
    expect(mockOnClose).not.toHaveBeenCalled()
  })

  it('renders footer when provided', () => {
    const footer = (
      <div>
        <button>Cancel</button>
        <button>Confirm</button>
      </div>
    )
    
    render(<Modal {...defaultProps} footer={footer} />)
    
    expect(screen.getByText('Cancel')).toBeInTheDocument()
    expect(screen.getByText('Confirm')).toBeInTheDocument()
  })

  it('does not render footer when not provided', () => {
    render(<Modal {...defaultProps} />)
    
    // Check that there's no footer section
    const modalContent = screen.getByText('Test Modal').closest('.bg-white')
    const footerSection = modalContent?.querySelector('.border-t')
    
    expect(footerSection).not.toBeInTheDocument()
  })

  it('renders custom title', () => {
    render(<Modal {...defaultProps} title="Custom Title" />)
    
    expect(screen.getByText('Custom Title')).toBeInTheDocument()
    expect(screen.queryByText('Test Modal')).not.toBeInTheDocument()
  })

  it('renders custom children content', () => {
    const customChildren = (
      <div>
        <p>Custom paragraph</p>
        <button>Custom button</button>
      </div>
    )
    
    render(<Modal {...defaultProps} children={customChildren} />)
    
    expect(screen.getByText('Custom paragraph')).toBeInTheDocument()
    expect(screen.getByText('Custom button')).toBeInTheDocument()
  })

  it('has correct accessibility attributes', () => {
    render(<Modal {...defaultProps} />)
    
    const closeButton = screen.getByLabelText('Close modal')
    expect(closeButton).toHaveAttribute('aria-label', 'Close modal')
  })

  it('applies correct CSS classes for styling', () => {
    render(<Modal {...defaultProps} />)
    
    const backdrop = screen.getByText('Test Modal').closest('.fixed')
    expect(backdrop).toHaveClass('fixed', 'inset-0', 'z-50', 'flex', 'items-center', 'justify-center')
    
    const modalContent = screen.getByText('Test Modal').closest('.bg-white')
    expect(modalContent).toHaveClass('bg-white', 'dark:bg-gray-800', 'rounded-lg', 'shadow-xl')
  })

  it('handles keyboard events properly', () => {
    render(<Modal {...defaultProps} />)
    
    // The modal should be focusable and handle keyboard navigation
    const closeButton = screen.getByLabelText('Close modal')
    expect(closeButton).toHaveAttribute('class')
    
    // Test that the close button can receive focus
    closeButton.focus()
    expect(document.activeElement).toBe(closeButton)
  })
})
