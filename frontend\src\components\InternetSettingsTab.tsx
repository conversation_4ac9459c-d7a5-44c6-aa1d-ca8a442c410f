import React from 'react';
import { useConfigStore } from '../store/configStore';

const InternetSettingsTab: React.FC = () => {
  // Select state and actions from the Zustand store
  const {
    internetSettings,
    setInternetEnabled,
    setSearchProvider,
    setSearchApiKey,
    setIncludedDomains,
    setExcludedDomains
  } = useConfigStore((state) => ({
    internetSettings: state.internetSettings,
    setInternetEnabled: state.setInternetEnabled,
    setSearchProvider: state.setSearchProvider,
    setSearchApiKey: state.setSearchApiKey,
    setIncludedDomains: state.setIncludedDomains,
    setExcludedDomains: state.setExcludedDomains,
  }));

  // Handler for domain textareas (converts string to array and vice-versa)
  const handleDomainChange = (
    event: React.ChangeEvent<HTMLTextAreaElement>,
    setter: (domains: string[]) => void
  ) => {
    const domains = event.target.value.split('\n').map(d => d.trim()).filter(d => d); // Split by newline, trim, remove empty
    setter(domains);
  };

  return (
    <div className="space-y-6 p-1"> {/* Added padding and spacing */}
      <h3 className="text-lg font-semibold mb-4 text-gray-800 dark:text-gray-200">Internet Settings</h3>

      {/* Global Enable Toggle */}
      <div className="flex items-center justify-between p-3 bg-gray-100 dark:bg-gray-700 rounded-md shadow-sm">
        <label htmlFor="internetEnabled" className="font-medium text-gray-700 dark:text-gray-300">
          Enable Internet Search
        </label>
        <button
          id="internetEnabled"
          onClick={() => setInternetEnabled(!internetSettings.enabled)}
          className={`relative inline-flex items-center h-6 rounded-full w-11 transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 ${
            internetSettings.enabled ? 'bg-indigo-600' : 'bg-gray-300 dark:bg-gray-600'
          }`}
        >
          <span
            className={`inline-block w-4 h-4 transform bg-white rounded-full transition-transform duration-200 ease-in-out ${
              internetSettings.enabled ? 'translate-x-6' : 'translate-x-1'
            }`}
          />
        </button>
      </div>

      {/* Search Provider Selection */}
      <div className="space-y-2">
        <label htmlFor="searchProvider" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
          Search Provider
        </label>
        <select
          id="searchProvider"
          value={internetSettings.searchProvider}
          onChange={(e) => setSearchProvider(e.target.value)}
          className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 dark:border-gray-600 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm rounded-md bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100"
        >
          <option value="All">All Available Providers</option>
          <option value="Serper">Serper</option>
          <option value="Google">Google Search API</option>
          <option value="DuckDuckGo">DuckDuckGo</option>
          <option value="Brave">Brave Search</option>
        </select>
      </div>

      {/* API Key Input */}
      <div className="space-y-2">
        <label htmlFor="searchApiKey" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
          {internetSettings.searchProvider} API Key
        </label>
        <input
          type="password" // Use password type to obscure key
          id="searchApiKey"
          value={internetSettings.searchApiKey}
          onChange={(e) => setSearchApiKey(e.target.value)}
          placeholder={`Enter your ${internetSettings.searchProvider} API Key`}
          className="mt-1 block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100"
        />
      </div>

      {/* Domain Filtering */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        {/* Included Domains */}
        <div className="space-y-2">
          <label htmlFor="includedDomains" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
            Included Domains (One per line)
          </label>
          <textarea
            id="includedDomains"
            rows={4}
            value={internetSettings.includedDomains.join('\n')}
            onChange={(e) => handleDomainChange(e, setIncludedDomains)}
            placeholder="e.g., wikipedia.org\nexample.com"
            className="mt-1 block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100"
          />
        </div>

        {/* Excluded Domains */}
        <div className="space-y-2">
          <label htmlFor="excludedDomains" className="block text-sm font-medium text-gray-700 dark:text-gray-300">
            Excluded Domains (One per line)
          </label>
          <textarea
            id="excludedDomains"
            rows={4}
            value={internetSettings.excludedDomains.join('\n')}
            onChange={(e) => handleDomainChange(e, setExcludedDomains)}
            placeholder="e.g., social-media-site.com\nnews-aggregator.net"
            className="mt-1 block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100"
          />
        </div>
      </div>
    </div>
  );
};

export default InternetSettingsTab;
