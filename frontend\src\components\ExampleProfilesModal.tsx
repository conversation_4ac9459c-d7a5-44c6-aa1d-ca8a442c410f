import React, { useState, useEffect } from 'react';
import { getExampleProfiles, getExampleProfile, ExampleProfileListItem } from '../services/importExportService';
import { useConfigStore } from '../store/configStore';

interface ExampleProfilesModalProps {
  isOpen: boolean;
  onClose: () => void;
}

const ExampleProfilesModal: React.FC<ExampleProfilesModalProps> = ({ isOpen, onClose }) => {
  const [exampleProfiles, setExampleProfiles] = useState<ExampleProfileListItem[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const {
    agentCount,
    agentConfigurations,
    setGeneralInstructions,
    setAgentInstructions,
    setGlobalError,
    setGlobalSuccess,
    clearGlobalMessages
  } = useConfigStore();

  useEffect(() => {
    if (isOpen) {
      fetchExampleProfiles();
    }
  }, [isOpen]);

  const fetchExampleProfiles = async () => {
    setIsLoading(true);
    setError(null);
    try {
      const data = await getExampleProfiles();
      setExampleProfiles(data.profiles);
    } catch (err: any) {
      setError(err.response?.data?.message || err.message || 'Failed to fetch example profiles.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleApplyInstructions = async (filename: string) => {
    setIsLoading(true);
    clearGlobalMessages();
    try {
      // Get the example profile
      const profileData = await getExampleProfile(filename);

      // Only apply the instructions from the profile
      if (profileData.settings) {
        // Set general instructions
        if (profileData.settings.generalInstructions) {
          setGeneralInstructions(profileData.settings.generalInstructions);
        }

        // Set agent-specific instructions
        if (profileData.settings.agentConfigurations && Array.isArray(profileData.settings.agentConfigurations)) {
          // Only apply instructions for the number of agents we currently have configured
          const currentAgentCount = agentCount;

          for (let i = 0; i < currentAgentCount && i < profileData.settings.agentConfigurations.length; i++) {
            const exampleConfig = profileData.settings.agentConfigurations[i];
            if (exampleConfig && exampleConfig.instructions) {
              // Only update the instructions, not the provider or model
              setAgentInstructions(i, exampleConfig.instructions);
            }
          }
        }

        setGlobalSuccess(`Instructions from '${profileData.name}' applied successfully!`);
      } else {
        throw new Error('Invalid profile data structure');
      }

      onClose();
    } catch (err: any) {
      setGlobalError(err.response?.data?.message || err.message || 'Failed to apply instructions.');
    } finally {
      setIsLoading(false);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white dark:bg-gray-800 rounded-lg p-6 max-w-2xl w-full max-h-[80vh] overflow-y-auto">
        <h2 className="text-xl font-semibold mb-4 text-gray-900 dark:text-gray-100">Example Agent Instructions</h2>
        <p className="mb-4 text-sm text-gray-700 dark:text-gray-300">These examples only apply the instructions for each agent. Your current model selections and other settings will remain unchanged.</p>

        {error && (
          <div className="mb-4 p-3 bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200 rounded">
            {error}
          </div>
        )}

        {isLoading ? (
          <div className="flex justify-center items-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
          </div>
        ) : (
          <div className="space-y-4">
            {exampleProfiles.map((profile) => (
              <div key={profile.file} className="border border-gray-300 dark:border-gray-600 rounded-lg p-4">
                <div className="flex justify-between items-start">
                  <div>
                    <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100">{profile.name}</h3>
                    <p className="text-sm text-gray-600 dark:text-gray-400">Agents: {profile.agentCount}</p>
                  </div>
                  <button
                    onClick={() => handleApplyInstructions(profile.file)}
                    disabled={isLoading}
                    className="px-3 py-1 bg-primary text-white rounded hover:bg-primary-dark transition-colors text-sm disabled:opacity-50"
                  >
                    Apply Instructions
                  </button>
                </div>
                <p className="mt-2 text-gray-700 dark:text-gray-300">{profile.description}</p>
              </div>
            ))}

            {exampleProfiles.length === 0 && !isLoading && (
              <p className="text-center py-4 text-gray-600 dark:text-gray-400">No example profiles found.</p>
            )}
          </div>
        )}

        <div className="mt-6 flex justify-end">
          <button
            onClick={onClose}
            className="px-4 py-2 bg-gray-200 dark:bg-gray-600 text-gray-700 dark:text-gray-200 rounded hover:bg-gray-300 dark:hover:bg-gray-500 transition-colors text-sm"
          >
            Close
          </button>
        </div>
      </div>
    </div>
  );
};

export default ExampleProfilesModal;
