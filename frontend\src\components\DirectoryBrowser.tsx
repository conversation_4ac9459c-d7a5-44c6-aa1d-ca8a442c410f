import React, { useState, useEffect, useCallback } from 'react';
import apiClient from '../services/authApiService';
import { VscFolder, VscChevronUp, VscNewFolder, VscLoading, VscError, VscFile, VscCheck } from 'react-icons/vsc';

interface DirectoryItem {
  name: string;
  path: string;
  type: 'directory' | 'file';
}

interface DirectoryBrowserProps {
  onSelectDirectory: (path: string) => void;
  onCancel: () => void;
  initialPath?: string;
  title?: string;
  selectButtonLabel?: string;
  showCreateButton?: boolean;
}

const DirectoryBrowser: React.FC<DirectoryBrowserProps> = ({
  onSelectDirectory,
  onCancel,
  initialPath = './',
  title = 'Select Directory',
  selectButtonLabel = 'Select This Directory',
  showCreateButton = true,
}) => {
  const [currentPath, setCurrentPath] = useState<string>(initialPath);
  const [items, setItems] = useState<DirectoryItem[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [selectedPath, setSelectedPath] = useState<string | null>(null);

  // Fetch directory contents
  // **** IMPORTANT: Requires Backend Endpoint: GET /rag/list-path-contents?path={path} ****
  // **** This endpoint MUST exist on your backend for this component to work. ****
  const fetchContents = useCallback(async (path: string) => {
    setIsLoading(true);
    setError(null);
    setSelectedPath(null);

    console.log(`DirectoryBrowser: Fetching contents for path: ${path}`); // Debug log

    try {
       // This endpoint MUST be implemented on your backend
      const response = await apiClient.get('/api/rag/list-path-contents', {
        params: { path } // Send path as query parameter
      });
      // Expected backend response format:
      // { currentPath: "/absolute/or/resolved/path", items: [{ name: string, path: string, type: 'directory' | 'file' }] }
      setItems(response.data.items || []);
      setCurrentPath(response.data.currentPath || path);
      console.log(`DirectoryBrowser: Received contents for ${response.data.currentPath || path}`, response.data.items); // Debug log
    } catch (err: any) {
      console.error('Error fetching directory contents:', err);
      const errorMsg = `Failed to list contents for '${path}': ${err.response?.data?.message || err.message} (Status: ${err.response?.status || 'N/A'})`;
      setError(errorMsg); // Display more informative error
      setItems([]);
    } finally {
      setIsLoading(false);
    }
  }, []); // No dependencies needed if logic is self-contained

  // Navigate into a directory
  const navigateToDirectory = (dirPath: string) => {
    fetchContents(dirPath);
  };

  // Navigate to parent directory
   const navigateToParent = () => {
    // Basic parent path calculation (might need refinement for edge cases like root)
    let parentPath = currentPath.replace(/\\/g, '/').split('/').filter(Boolean).slice(0, -1).join('/');
    // Handle root case or relative paths carefully
     if (currentPath.startsWith('/') && parentPath === '') parentPath = '/'; // Absolute root
     else if (!currentPath.startsWith('/') && parentPath === '') parentPath = '.'; // Relative root/current dir

    if (parentPath !== currentPath) { // Prevent staying in the same place
        fetchContents(parentPath || '.'); // Navigate to calculated parent or relative root
    } else {
         console.log("Already at root or cannot determine parent.");
         // Optionally fetch '.' again if needed
         // fetchContents('.');
    }
  };


   // Select an item (single click) - only allow selecting directories
   const handleItemClick = (item: DirectoryItem) => {
     if (item.type === 'directory') {
         setSelectedPath(item.path);
     } else {
         // Optionally allow selecting files later if needed for other purposes
         setSelectedPath(null); // Prevent file selection for now
     }
   };

   // Handle double-click for navigation into directories
   const handleItemDoubleClick = (item: DirectoryItem) => {
        if (item.type === 'directory') {
            navigateToDirectory(item.path);
        }
   };

  // Handle main select button action
  const handleSelect = () => {
    // Prefer the explicitly selected directory, fallback to the current path being viewed
    onSelectDirectory(selectedPath || currentPath);
  };

  // Create a new directory
  // **** IMPORTANT: Requires Backend Endpoint: POST /rag/create-directory ****
  // **** This endpoint MUST exist on your backend for this function to work. ****
  const handleCreateDirectory = async () => {
    const dirName = prompt('Enter new directory name:');
    if (!dirName || !dirName.trim()) return;

    setIsLoading(true);
    setError(null);

    try {
      // Construct the full path for the new directory
      // Ensure no double slashes, handle potential trailing slash on currentPath
      const pathSeparator = currentPath.includes('/') ? '/' : '\\'; // Detect separator (basic)
      const newDirPath = `${currentPath.replace(/[\\/]$/, '')}${pathSeparator}${dirName}`;

       console.log(`DirectoryBrowser: Attempting to create directory: ${newDirPath}`); // Debug log

       // This endpoint MUST be implemented on your backend
      await apiClient.post('/api/rag/create-directory', { path: newDirPath });

      // Refresh the current directory listing to show the new folder
      fetchContents(currentPath); // Reload current view
      setStatusMessage({type:"success", message:`Directory '${dirName}' created successfully.`}); // Provide feedback (if setStatusMessage is passed or handled globally)

    } catch (err: any) {
      console.error('Error creating directory:', err);
       const errorMsg = `Failed to create directory '${dirName}': ${err.response?.data?.message || err.message} (Status: ${err.response?.status || 'N/A'})`;
      setError(errorMsg); // Display more informative error
      setIsLoading(false); // Ensure loading is false on error
    }
    // setIsLoading(false) will be called by fetchContents on success if implemented correctly
  };

  // Initial fetch on mount
  useEffect(() => {
    fetchContents(initialPath);
  }, [initialPath, fetchContents]); // fetchContents is memoized


  // Determine if the "Up" button should be enabled
   const canNavigateUp = currentPath && currentPath !== '.' && currentPath !== '/' && currentPath !== './';


  return (
    // --- Modal Structure (same as before, ensure styling is correct) ---
    <div className="fixed inset-0 bg-black/60 flex items-center justify-center z-50 p-4 backdrop-blur-sm">
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-xl p-5 w-full max-w-2xl flex flex-col max-h-[80vh]">
        {/* Title */}
        <h2 className="text-xl font-semibold mb-4 text-gray-800 dark:text-gray-200 flex-shrink-0">
          {title}
        </h2>

        {/* Navigation and Path Display */}
        <div className="mb-3 flex items-center gap-2 flex-shrink-0">
           {/* Up Button */}
          <button onClick={navigateToParent} disabled={!canNavigateUp || isLoading} className="p-2 text-sm bg-gray-200 text-gray-700 rounded hover:bg-gray-300 dark:bg-gray-600 dark:text-gray-200 dark:hover:bg-gray-500 disabled:opacity-50" title="Go to parent directory">
            <VscChevronUp />
          </button>
          {/* Current Path */}
          <div className="flex-grow px-3 py-1.5 bg-gray-100 dark:bg-gray-700 rounded text-sm font-mono text-gray-800 dark:text-gray-200 overflow-x-auto whitespace-nowrap">
            {currentPath}
          </div>
           {/* New Folder Button */}
          {showCreateButton && (
              <button onClick={handleCreateDirectory} disabled={isLoading} className="p-2 text-sm bg-green-100 text-green-700 rounded hover:bg-green-200 dark:bg-green-900/50 dark:text-green-300 dark:hover:bg-green-800/50 disabled:opacity-50 flex items-center gap-1" title="Create new folder (Requires Backend Support)">
                <VscNewFolder /> New Folder
              </button>
          )}
        </div>

        {/* Error message Display */}
        {error && (
          <div className="mb-3 p-3 bg-red-100 text-red-700 rounded dark:bg-red-900/50 dark:text-red-300 flex items-center gap-2 flex-shrink-0 text-xs break-words"> {/* Allow error message to wrap */}
            <VscError className="flex-shrink-0" /> {error}
          </div>
        )}

        {/* Directory Listing Area */}
        <div className="border border-gray-300 dark:border-gray-600 rounded-md flex-grow overflow-y-auto mb-4 min-h-[200px]">
          {isLoading ? (
            <div className="flex items-center justify-center h-full text-gray-600 dark:text-gray-400"> <VscLoading className="animate-spin mr-2" /> Loading... </div>
          ) : items.length === 0 && !error ? (
            <div className="flex items-center justify-center h-full text-gray-600 dark:text-gray-400 italic"> Folder is empty or not accessible </div>
          ) : (
            <ul className="divide-y divide-gray-200 dark:divide-gray-700">
              {/* Render Directories */}
              {items.filter(item => item.type === 'directory').map((item) => (
                <li key={item.path} onClick={() => handleItemClick(item)} onDoubleClick={() => handleItemDoubleClick(item)} className={`p-2 hover:bg-gray-100 dark:hover:bg-gray-700 cursor-pointer flex items-center gap-2 text-sm ${ selectedPath === item.path ? 'bg-blue-100 dark:bg-blue-900/30 font-medium' : '' }`} >
                  <VscFolder className={`flex-shrink-0 ${selectedPath === item.path ? 'text-blue-600 dark:text-blue-400' : 'text-yellow-500'}`} />
                  <span className="text-gray-800 dark:text-gray-200 truncate flex-grow"> {item.name} </span>
                   {selectedPath === item.path && <VscCheck className="text-blue-600 dark:text-blue-400 flex-shrink-0" title="Selected"/>}
                </li>
              ))}
               {/* Render Files (grayed out) */}
               {items.filter(item => item.type === 'file').map((item) => (
                 <li key={item.path} className="p-2 flex items-center gap-2 text-sm text-gray-400 dark:text-gray-500 cursor-not-allowed">
                    <VscFile className="flex-shrink-0" />
                    <span className="truncate italic">{item.name}</span>
                 </li>
                ))}
            </ul>
          )}
        </div>

        {/* Action buttons */}
        <div className="flex justify-end space-x-3 flex-shrink-0">
          <button onClick={onCancel} className="px-4 py-2 bg-gray-200 text-gray-700 rounded hover:bg-gray-300 dark:bg-gray-600 dark:text-gray-200 dark:hover:bg-gray-500"> Cancel </button>
          <button onClick={handleSelect} className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 dark:bg-blue-600 dark:hover:bg-blue-700 disabled:opacity-50" disabled={isLoading || (!selectedPath && !currentPath)} > {selectButtonLabel} </button>
        </div>
      </div>
    </div>
  );
};

// Helper function to potentially pass status updates back up (optional)
declare global {
    interface Window {
        setStatusMessage?: (message: { type: 'success' | 'error' | 'info' | 'loading'; message: string }) => void;
    }
}
const setStatusMessage = (message: { type: 'success' | 'error' | 'info' | 'loading'; message: string }) => {
    if (window.setStatusMessage) {
        window.setStatusMessage(message);
    } else {
        console.log(`Status (${message.type}): ${message.message}`);
    }
};


export default DirectoryBrowser;