version: '3.8'

services:
  # MongoDB Service
  mongo:
    image: mongo:latest
    container_name: maia-mongo
    ports:
      - "27017:27017" # Expose default MongoDB port
    volumes:
      - mongo-data:/data/db # Persist data using a named volume
    restart: unless-stopped

  # Backend Service (Node.js)
  backend:
    build:
      context: . # Build from the root directory
      dockerfile: Dockerfile.backend # Specify the backend Dockerfile
    container_name: maia-backend
    ports:
      - "3000:3000" # Map host port 3000 to container port 3000
    depends_on:
      - mongo # Ensure MongoDB starts before the backend
    environment:
      # Pass environment variables from a .env file at the root
      # Create a .env file in the project root based on backend/.env.example
      - NODE_ENV=production # Set environment to production
      - PORT=3000
      - MONGO_URI=mongodb://mongo:27017/maia_db # Use service name 'mongo' for connection
      - JWT_SECRET=${JWT_SECRET} # Get from host environment or .env file
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - ANTHROPIC_API_KEY=${ANTHROPIC_API_KEY}
      - GOOGLE_API_KEY=${GOOGLE_API_KEY}
      - GROQ_API_KEY=${GROQ_API_KEY}
      - DEEPSEEK_API_KEY=${DEEPSEEK_API_KEY}
      - SERPER_API_KEY=${SERPER_API_KEY}
      - OLLAMA_BASE_URL=${OLLAMA_BASE_URL} # e.g., http://host.docker.internal:11434 if Ollama runs on host
      - LMSTUDIO_BASE_URL=${LMSTUDIO_BASE_URL} # e.g., http://host.docker.internal:1234/v1 if LM Studio runs on host
      - OLLAMA_REQUEST_TIMEOUT=${OLLAMA_REQUEST_TIMEOUT:-60000} # Timeout for Ollama model loading (default: 60 seconds)
      - LMSTUDIO_REQUEST_TIMEOUT=${LMSTUDIO_REQUEST_TIMEOUT:-30000} # Timeout for LM Studio model loading (default: 30 seconds)
      - FRONTEND_URL=${FRONTEND_URL} # e.g., http://localhost:5173 or deployed URL
    # Optional: Mount volumes for development (bind mounts)
    # volumes:
    #   - ./backend:/app/backend # Mount local backend code
    #   - /app/backend/node_modules # Exclude node_modules from being overwritten
    restart: unless-stopped

  # Frontend Service (React/Nginx)
  frontend:
    build:
      context: . # Build from the root directory
      dockerfile: Dockerfile.frontend # Specify the frontend Dockerfile
      args:
        # Pass build arguments for Vite environment variables
        VITE_API_BASE_URL: ${VITE_API_BASE_URL:-/api} # Default to relative /api if not set
        VITE_SOCKET_SERVER_URL: ${VITE_SOCKET_SERVER_URL:-} # Default to same origin if not set
    container_name: maia-frontend
    ports:
      - "5173:80" # Map host port 5173 to Nginx container port 80
    depends_on:
      - backend # Frontend might depend on backend API being available
    restart: unless-stopped

volumes:
  mongo-data: # Define the named volume for MongoDB data

# Note: For production, consider:
# - Using a production-ready Node.js base image (e.g., node:20-slim).
# - More robust handling of secrets (e.g., Docker secrets, Vault).
# - Setting up proper logging drivers.
# - Configuring Nginx for HTTPS, caching, etc.
# - Creating separate docker-compose.prod.yml and docker-compose.dev.yml files.
