import mongoose, { Schema, Document } from 'mongoose';

// Interface matching the structure in frontend/src/store/configStore.ts
// We only need the parts that are saved/loaded, not the actions or transient state like isProcessing.

interface AgentConfig {
  provider: string;
  model: string;
  instructions: string;
  useDefaultInstructions?: boolean;
}

interface InternetSettings {
  enabled: boolean;
  searchProvider: string;
  searchApiKey: string; // Consider encrypting sensitive keys in a real application
  includedDomains: string[];
  excludedDomains: string[];
}

interface RagSettings {
  enabled: boolean;
  chunkingStrategy: string;
  chunkSize: number;
  chunkOverlap: number;
  embeddingModel: string;
  retrievalNResults: number;
  retrievalThreshold: number;
  useReranking: boolean;
  useQueryExpansion: boolean;
}

// Interface for the settings object within the profile
interface ConfigurationSettings {
  agentCount: number;
  generalInstructions: string;
  agentConfigurations: AgentConfig[];
  internetSettings: InternetSettings;
  ragSettings: RagSettings;
  // New structure for user model management
  userManagedModels?: Record<string, {
    added: string[];
    removed: string[];
    lastSelected?: string;
  }>;
  maxAgentRuns?: number; // Add missing field
  baseInstructions?: string; // Add missing field
  useBaseInstructions?: boolean; // Add missing field
  // Token limit settings
  maxContextWindow?: number;
  workingContextSize?: number;
  // Add other settings groups here if they are added to the store later
}

// Interface for the Mongoose Document
export interface IConfigurationProfile extends Document {
  name: string;
  settings: ConfigurationSettings;
  userId: mongoose.Schema.Types.ObjectId; // Link to the User model
  createdAt: Date;
  updatedAt: Date;
}

const AgentConfigSchema = new Schema<AgentConfig>({
  provider: { type: String, default: '' },
  model: { type: String, default: '' },
  instructions: { type: String, default: '' },
  useDefaultInstructions: { type: Boolean, default: true },
}, { _id: false }); // Don't create separate IDs for subdocuments unless needed

const InternetSettingsSchema = new Schema<InternetSettings>({
  enabled: { type: Boolean, required: true, default: false },
  searchProvider: { type: String, required: true, default: 'Serper' },
  searchApiKey: { type: String, default: '' },
  includedDomains: { type: [String], default: [] },
  excludedDomains: { type: [String], default: [] },
}, { _id: false });

const RagSettingsSchema = new Schema<RagSettings>({
  enabled: { type: Boolean, required: true, default: false },
  chunkingStrategy: { type: String, required: true, default: 'semantic' },
  chunkSize: { type: Number, required: true, default: 512 },
  chunkOverlap: { type: Number, required: true, default: 50 },
  embeddingModel: { type: String, required: true, default: 'Xenova/all-mpnet-base-v2' },
  retrievalNResults: { type: Number, required: true, default: 5 },
  retrievalThreshold: { type: Number, required: true, default: 0.7 },
  useReranking: { type: Boolean, required: true, default: false },
  useQueryExpansion: { type: Boolean, required: true, default: false },
}, { _id: false });

const ConfigurationSettingsSchema = new Schema<ConfigurationSettings>({
    agentCount: { type: Number, required: true, min: 1, max: 5, default: 1 },
    generalInstructions: { type: String, default: '' },
    agentConfigurations: { type: [AgentConfigSchema], default: () => ([{ provider: '', model: '', instructions: '', useDefaultInstructions: true }]) }, // Default for 1 agent
    internetSettings: { type: InternetSettingsSchema, required: true, default: () => ({}) },
    ragSettings: { type: RagSettingsSchema, required: true, default: () => ({}) },
    // Use plain Object type for userManagedModels to avoid Map type conflicts
    userManagedModels: {
      type: Object,
      // Define the structure expected for each key within the object
      of: {
        added: { type: [String], default: [] },
        removed: { type: [String], default: [] },
        lastSelected: { type: String }
      },
      default: {} // Default to an empty object
    },
    maxAgentRuns: { type: Number, default: 1 }, // Add missing field schema
    baseInstructions: { type: String, default: '' }, // Add missing field schema
    useBaseInstructions: { type: Boolean, default: false }, // Add missing field schema
    // Token limit settings
    maxContextWindow: { type: Number, default: 20000 },
    workingContextSize: { type: Number, default: 16384 }
}, { _id: false });


const ConfigurationProfileSchema = new Schema<IConfigurationProfile>(
  {
    name: {
      type: String,
      required: true,
      trim: true,
      // Name uniqueness will be handled by the compound index with userId
    },
    settings: {
      type: ConfigurationSettingsSchema,
      required: true,
    },
    userId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User', // Reference the User model
      required: true, // Make it required
      index: true, // Add an index for faster lookups by user
    },
  },
  {
    timestamps: true,
  }
);

// Add a compound index to ensure profile names are unique per user
ConfigurationProfileSchema.index({ userId: 1, name: 1 }, { unique: true });

const ConfigurationProfile = mongoose.model<IConfigurationProfile>(
  'ConfigurationProfile',
  ConfigurationProfileSchema
);

export default ConfigurationProfile;
